import React, { useState, useEffect } from 'react';
import { useLocation  } from "react-router-dom";
import { <PERSON>evronLeft, ChevronRight, Eye, Heart, MapPin, Star,Pin,Languages  } from 'lucide-react';
import { Button } from '../components/ui/buttons';
import { Badge } from '../components/ui/badges';
import Header from '../components/Header';
import Footer from '../components/Footer';
import QuoteRequestModal from '../components/QuoteRequestModal';
import { useParams, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import {getAllCategories} from '../data/categories'


interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
}

// Interface pour les éléments du portfolio
interface PortfolioItem {
  id?: number;
  path?: string; 
  name?: string;
  type?: string;
  created_at?: string;
  // description?: string;
}

interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  address: string;
  city: string;
  country: string;
  skills: string[] | null;
  languages: string[] | null;
  availability_status: string | null;
  services_offered: string[];
  hourly_rate: string;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  avatar?: string;
  cover_photo?: string;
  profile_picture_path?: string;
  rating?: number;
  review_count?: number;
  bio?: string;
  title?: string;
  portfolio?: PortfolioItem[];
  user: User;
  likes_count?: number;
  views_count?: number;
}

interface Achievement {
  id: number;
  freelance_profile_id: number;
  title: string;
  organization: string;
  date_obtained: string;
  description: string;
  file_path: string | null;
  achievement_url: string | null;
  created_at: string;
  updated_at: string;
}

const ServiceDetail = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showQuoteModal, setShowQuoteModal] = useState(false);
  const { state } = useLocation();
  const project = state?.project||state?.service;
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [images, setImages] = useState([
    project?.image_url || "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=600&auto=format&fit=crop",
    ...(project?.file_urls?.length ? project.file_urls : []),
// "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=600&auto=format&fit=crop"
    ]);
  const [professional, setProfessional] = useState<FreelanceProfile | null>(null);
  const [error, setError] = useState<string | null>(null);
  
    const [offers, setOffers] = useState([]);
    const [selectedOfferId, setSelectedOfferId] = useState(null);
    const [showModal, setShowModal] = useState(false);

    const [achievements, setAchievements] = useState<Achievement[]>([]);
    const [projects, setProject] = useState<any | []>([]);
    const [services, setService] = useState<any | []>([]);
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // Fonction pour formater l'URL de l'image
  const getImageUrl = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  const getUrlProlfil = (path : string)  => {
      return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  };

  useEffect(() => {
    // recordProfessionalView(id);
    fetchProfessional();
    // fetchClientPendingOffers();
    fetchAchievements();
    fetchServiceOffert();
  }, [project?.user_id,project?.professional_id]);


  const fetchProfessional = async () => {
        try {
          setLoading(true);
          // Essayer d'abord l'endpoint professionals
          let response = await fetch(`${API_BASE_URL}/api/professionals/${project?.professional_id}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
          });
  
          // Si l'endpoint professionals échoue, essayer l'endpoint users
          if (!response.ok) {
            console.log('Endpoint professionals a échoué, essai de l\'endpoint users');
            response = await fetch(`${API_BASE_URL}/api/users/${project?.professional_id}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json',
              },
            });
          }
  
          if (!response.ok) {
            throw new Error('Impossible de récupérer les détails du professionnel');
          }
  
          const data = await response.json();
          console.log('Données du professionnel récupérées:', data);
  
          // Traiter les données selon le format
          if (data.professional) {
            // Traiter les skills qui peuvent être une chaîne JSON ou un tableau
            let skills = [];
            if (data.professional.skills) {
              if (Array.isArray(data.professional.skills)) {
                skills = data.professional.skills;
              } else if (typeof data.professional.skills === 'string') {
                try {
                  skills = JSON.parse(data.professional.skills);
                } catch (e) {
                  skills = [data.professional.skills]; // Si ce n'est pas un JSON valide, le traiter comme une chaîne simple
                }
              }
            }
  
            // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
            let portfolio = [];
            if (data.professional.portfolio) {
              if (Array.isArray(data.professional.portfolio)) {
                portfolio = data.professional.portfolio;
              } else if (typeof data.professional.portfolio === 'string') {
                try {
                  portfolio = JSON.parse(data.professional.portfolio);
                } catch (e) {
                  portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
                }
              }
            }
  
            // Mettre à jour les données du professionnel
            setProfessional({
              ...data.professional,
              skills: skills,
              portfolio: portfolio.length > 0 ? portfolio : [
                { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
              ],
              likes_count: data.professional.likes_count,
              views_count: data.professional.views_count,
            });
          } else if (data.user && data.profile_data) {
            // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
            let portfolio = [];
            if (data.profile_data.portfolio) {
              if (Array.isArray(data.profile_data.portfolio)) {
                portfolio = data.profile_data.portfolio;
              } else if (typeof data.profile_data.portfolio === 'string') {
                try {
                  portfolio = JSON.parse(data.profile_data.portfolio);
                } catch (e) {
                  portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
                }
              }
            }
  
            setProfessional({
              ...data.profile_data,
              user: data.user,
              portfolio: portfolio.length > 0 ? portfolio : [
                { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
              ],
              likes_count: data.user.likes_count || 0,
              views_count: data.user.views_count || 0,
            });
          } else if (data.user) {
            setProfessional({
              id: data.user.id,
              user_id: data.user.id,
              first_name: data.user.first_name,
              last_name: data.user.last_name,
              phone: '',
              address: '',
              city: data.user.city || 'Paris',
              country: data.user.country || 'France',
              skills: data.user.skills || ['Animation 3D', 'Modélisation 3D', 'Rigging'],
              languages: ['Français', 'Anglais'],
              availability_status: data.user.availability_status || 'available',
              services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
              hourly_rate: data.user.hourly_rate || '45',
              completion_percentage: data.completion_percentage || 100,
              created_at: data.user.created_at,
              updated_at: data.user.updated_at,
              avatar: data.user.avatar,
              profile_picture_path: data.user.profile_picture_path,
              rating: data.user.rating || 4.8,
              review_count: data.user.review_count || 27,
              bio: data.user.bio || 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
              title: data.user.title || 'Artiste 3D',
              portfolio: data.user.portfolio || [
                { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
              ],
              likes_count: data.user.likes_count || 0,
              views_count: data.user.views_count || 0,
              user: data.user,
            });
          } else {
            // Utiliser des données de démonstration
            setProfessional({
              id: parseInt(project.professional_id || '1'),
              user_id: parseInt(project.user_id || '1'),
              first_name: 'Thomas',
              last_name: 'Martin',
              phone: '+33 6 12 34 56 78',
              address: '123 Rue de la Création',
              city: 'Paris',
              country: 'France',
              skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
              languages: ['Français', 'Anglais'],
              availability_status: 'available',
              services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
              hourly_rate: '45',
              completion_percentage: 100,
              created_at: '2023-01-01',
              updated_at: '2023-01-01',
              avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
              profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
              rating: 4.8,
              review_count: 27,
              portfolio: [
                { name: 'Projet 3D 1', path: 'https://picsum.photos/seed/101/300/200', type: 'Modélisation 3D pour un jeu vidéo' },
                { name: 'Animation 3D', path: 'https://picsum.photos/seed/102/300/200', type: 'Animation de personnage' },
                { name: 'Rendu architectural', path: 'https://picsum.photos/seed/103/300/200', type: 'Visualisation architecturale' },
              ],
              likes_count: 0,
              views_count: 0,
              bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
              title: 'Artiste 3D',
              user: {
                id: parseInt(project.user_id || '1'),
                first_name: 'Thomas',
                last_name: 'Martin',
                email: '<EMAIL>',
                is_professional: true,
              },
            });
          }
        } catch (err) {
          console.error('Erreur lors de la récupération du professionnel:', err);
          if (err instanceof Error) {
            setError(err.message);
          } else {
            setError('Une erreur inconnue est survenue');
          }
  
          // Utiliser des données de démonstration en cas d'erreur
          setProfessional({
            id: parseInt(project.professional_id || '1'),
            user_id: parseInt(project.user_id || '1'),
            first_name: 'Thomas',
            last_name: 'Martin',
            phone: '+33 6 12 34 56 78',
            address: '123 Rue de la Création',
            city: 'Paris',
            country: 'France',
            skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
            languages: ['Français', 'Anglais'],
            availability_status: 'available',
            services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
            hourly_rate: '45',
            completion_percentage: 100,
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
            profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
            rating: 4.8,
            review_count: 27,
            portfolio: [
              { name: 'Projet 3D 1', path: 'https://picsum.photos/seed/101/300/200', type: 'Modélisation 3D pour un jeu vidéo' },
              { name: 'Animation 3D', path: 'https://picsum.photos/seed/102/300/200', type: 'Animation de personnage' },
              { name: 'Rendu architectural', path: 'https://picsum.photos/seed/103/300/200', type: 'Visualisation architecturale' },
            ],
            likes_count: 0,
            views_count: 0,
            bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
            title: 'Artiste 3D',
            user: {
              id: parseInt(project.user_id || '1'),
              first_name: 'Thomas',
              last_name: 'Martin',
              email: '<EMAIL>',
              is_professional: true,
            },
          });
        } finally {
          setLoading(false);
        }
      };


  const fetchServiceOffert = async () => {
        try {
          setLoading(true);
  
           const response = await fetch(`${API_BASE_URL}/api/professionals/${project?.user_id}/service-offers`);
  
            if (!response.ok) {
              throw new Error('Erreur lors de la récupération des réalisations');
            }
        
            const data = await response.json();
        
            console.log("data :", data);
        
            if (Array.isArray(data)) {
              const formatted = data.map((item) => ({
                id: item.id,
                title: item.title,
                description: item.description || 'Projet réalisé avec passion et expertise technique.',
                concepts : item.concepts,
                revisions : item.revisions,
                image_url : Array.isArray(item.files) && item.files.length > 0
                  ? `${API_BASE_URL}/storage/${item.files[0].path}`
                  : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
                file_urls: Array.isArray(item.files)
                  ? item.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
                  : [],
                category: item.categories ? item.categories.join(" - ") : "",
                client_name: item.execution_time,
                professional_name: item.user.first_name+' '+item.user.last_name, // Si tu n'as pas cette info dans l'API
                professional_id: item.user.id || 1,
                date_create : item.created_at,
                price : item.price,
                user_id : item.user.id,
                views : item.views,
                likes : item.likes,
                avatar: item.user.professional_details.avatar? getUrlProlfil(String(item.user.professional_details.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
              }));
        
              console.log("Data Formater :", formatted);
              setService(formatted);
            } else {
              setService([]);
            }
        } catch (error) {
          console.error("Erreur lors de la récupération des réalisations:", error);
          setError("Impossible de charger les réalisations. Veuillez réessayer plus tard.");
        }finally{
        setLoading(false);
      }
      };

  const fetchAchievements = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/professionals/${project?.professional_id}/achievements`);
        
        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des réalisations');
        }
        
        const data = await response.json();
        
        if (data.success && data.achievements) {
          setAchievements(data.achievements);

          const formatted = data.achievements.map((proj:any) => ({
            id: proj.id,
            title: proj.title,
            description: proj.description || 'Projet réalisé avec passion et expertise technique.',
            image_url : proj.cover_photo
            ? `${API_BASE_URL}/storage/${proj.cover_photo}`
            : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            file_urls : Array.isArray(proj.gallery_photos)
            ? proj.gallery_photos.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
            : [],
            category: proj.category,
            client_name: '',
            date_create: proj.created_at,
            professional_name: proj.professional.first_name+' '+proj.professional.last_name, // Si tu n'as pas cette info dans l'API
            professional_id: proj.professional_profile_id || 1,
            user_id : proj.professional.user_id,
            avatar: proj.professional.avatar? getUrlProlfil(String(proj.professional.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          }));

        console.log("Data Formater :", formatted);
        setProject(formatted);
        } else {
          setAchievements([]);
          setProject([]);
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des réalisations:", error);
        setError("Impossible de charger les réalisations. Veuillez réessayer plus tard.");
        
      }finally{
        setLoading(false);
      }
    };


  const otherServices = [
    {
      id: 1,
      title: "Maquette orbital",
      price: "USD 3000",
      author: "Jack and Moris Render",
      isPro: true,
      views: 1,
      likes: 20,
      image: '/lovable-uploads/fcf0d7f7-bd2d-4bde-932f-ed180072b22f.png'
    },
    {
      id: 2,
      title: "360 visite tours",
      price: "USD 2000",
      author: "Mimi_360Pro",
      isPro: true,
      views: 1,
      likes: 20,
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png'
    }
  ];
  

  const getCategoryLabel = (category: any) => {
    // Si c'est un chiffre, on cherche dans la liste
    if (!isNaN(category)) {
      const found = getAllCategories().find((item:any) => item.id === Number(category));
      return found ? found.label : category;
    }
    // Sinon on retourne directement la valeur (ex: texte personnalisé)
    return category || "SMARTEK";
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };


  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 2;

  const filteredServices = (services ?? []).filter(
    (item: any) => item.id !== project?.id
  );

  const totalPages = Math.ceil(filteredServices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentItems = filteredServices.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  const filteredAchievements = (projects ?? []).filter(
    (item: any) => item.id !== project?.id
  );

  const totalPagesAChievement = Math.ceil(filteredAchievements.length / itemsPerPage);
  const startIndexAChievement = (currentPage - 1) * itemsPerPage;
  const currentItemsAchievement = filteredAchievements.slice(
    startIndexAChievement,
    startIndexAChievement + itemsPerPage
  );

   if (loading) {
      return (
        <div className="min-h-screen bg-white">
          <Header />
          <div className="flex justify-center items-center h-96">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
          <Footer />
        </div>
      );
    }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <div className="pt-2 pb-8 w-full bg-white">
        <div className="w-full px-10 md:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-7 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-5 lg:pr-8 lg:border-r border-gray-200">
                {/* Hero Section */}
                <div className="mb-8">
                    <h1 className="text-[34px] md:text-[36px] leading-tight tracking-tight font-semibold text-gray-900 mb-3">
                    {project?.title || "3D Render Mockup Product For Your Brand Luqi Pamungkas"}
                    </h1>

                    {/* Image Carousel */}
                    <div className="relative mb-6">
                    {/* Image */}
                    <div className="relative h-screen rounded-3xl overflow-hidden">
                        <img
                        src={images[currentImageIndex]}
                        alt="Service showcase"
                        className="w-full h-full object-cover object-center"
                        draggable={false}
                        />

                        {/* Navigation Arrows (subtle chevrons) */}
                        <button
                        type="button"
                        onClick={prevImage}
                        aria-label="Previous image"
                        className="absolute left-3 md:left-4 top-1/2 -translate-y-1/2 p-1.5 md:p-2 text-gray-600 hover:text-gray-800 transition"
                        >
                        <ChevronLeft className="w-5 h-5 md:w-6 md:h-6" />
                        </button>

                        <button
                        type="button"
                        onClick={nextImage}
                        aria-label="Next image"
                        className="absolute right-3 md:right-4 top-1/2 -translate-y-1/2 p-1.5 md:p-2 text-gray-600 hover:text-gray-800 transition"
                        >
                        <ChevronRight className="w-5 h-5 md:w-6 md:h-6" />
                        </button>
                    </div>

                    {/* Image Indicators (small dots) */}
                    <div className="flex justify-center gap-2 md:gap-2.5 pt-4">
                        {images.map((_, index) => {
                        const active = index === currentImageIndex;
                        return (
                            <button
                            key={index}
                            type="button"
                            onClick={() => setCurrentImageIndex(index)}
                            aria-label={`Go to image ${index + 1}`}
                            className={`rounded-full transition-all ${
                                active ? "w-2.5 h-2.5 bg-gray-800" : "w-2 h-2 bg-gray-300"
                            }`}
                            />
                        );
                        })}
                    </div>
                    </div>
                </div>

              <div className="w-full bg-white">
                <div className="mx-auto max-w-7xl px-6 py-12 md:py-16">
                    {/* 2-column layout */}
                    <div className="grid grid-cols-1 gap-12 md:grid-cols-12 md:gap-16">
                    {/* LEFT – About this Service */}
                    <section className="md:col-span-8">
                        <h2 className="text-3xl md:text-4xl font-semibold tracking-tight text-gray-900 mb-6">
                        About this Service
                        </h2>

                        <div className="space-y-6 text-gray-800">
                        <p className="leading-7 md:text-[17px]">
                            <div dangerouslySetInnerHTML={{ __html: project?.description }} />
                        </p>

                        <div>
                            <p className="font-medium text-gray-900 mb-2">What You Get:</p>
                            <ul className="list-disc pl-5 space-y-1 leading-7 md:text-[17px]">
                            <li>High-Quality 3D Renders (2000x2000px)</li>
                            <li>Realistic Textures & Lighting</li>
                            <li>Multiple Angles & Views</li>
                            <li>Transparent & Custom Backgrounds</li>
                            <li>Professional Composition & Shadows</li>
                            <li>Commercial Use License</li>
                            </ul>
                        </div>

                        <div>
                            <p className="font-medium text-gray-900 mb-2">Why Choose Me?</p>
                            <ul className="list-disc pl-5 space-y-1 leading-7 md:text-[17px]">
                            <li>5+ Years of 3D Rendering Experience</li>
                            <li>Fast Delivery & Revisions Available</li>
                            <li>100% Satisfaction Guaranteed</li>
                            </ul>
                        </div>

                        <div>
                            <p className="font-medium text-gray-900 mb-2">Industries I Work With:</p>
                            <ul className="list-disc pl-5 space-y-1 leading-7 md:text-[17px]">
                            <li>Cosmetics & Beauty (Skincare, Makeup, Perfume Bottles)</li>
                            <li>Packaging & Labels (Boxes, Pouches, Bottles)</li>
                            <li>Tech & Electronics (Headphones, Gadgets, Accessories)</li>
                            <li>Food & Beverage (Cans, Bottles, Containers)</li>
                            <li>Pharmaceuticals & Supplements</li>
                            </ul>
                        </div>

                        <div>
                            <p className="font-medium text-gray-900 mb-2">What I Need from You:</p>
                            <ul className="list-disc pl-5 space-y-1 leading-7 md:text-[17px]">
                            <li>Product images or sketches (if available)</li>
                            <li>Dimensions & reference materials</li>
                            <li>Preferred angles or styles</li>
                            </ul>
                        </div>

                        <div className="pt-2">
                            <p className="text-gray-800 md:text-[17px] font-medium mb-1">
                            Ready to Elevate Your Products Look?
                            </p>
                            <p className="text-gray-800 md:text-[17px]">-Luqi</p>
                        </div>
                        </div>
                    </section>

                    {/* RIGHT – Details */}
                    <aside className="md:col-span-4">
                        <h2 className="text-3xl md:text-4xl font-semibold tracking-tight text-gray-900 mb-6">
                        Details
                        </h2>

                        <div className="space-y-3">
                        <details className="group">
                            <summary className="flex items-center gap-2 cursor-pointer select-none py-2">
                            <ChevronRight className="h-4 w-4 text-gray-700 transition-transform group-open:rotate-90" />
                            <span className="text-gray-900 font-medium">What You Get ?</span>
                            </summary>
                            <div className="pl-6 mt-2 text-gray-800">
                            <ul className="list-disc pl-4 space-y-1 leading-7 md:text-[17px]">
                                <li>High-Quality 3D Renders (2000x2000px)</li>
                                <li>Realistic Textures & Lighting</li>
                                <li>Multiple Angles & Views</li>
                                <li>Transparent & Custom Backgrounds</li>
                                <li>Professional Composition & Shadows</li>
                                <li>Commercial Use License</li>
                            </ul>
                            </div>
                        </details>

                        <details className="group">
                            <summary className="flex items-center gap-2 cursor-pointer select-none py-2">
                            <ChevronRight className="h-4 w-4 text-gray-700 transition-transform group-open:rotate-90" />
                            <span className="text-gray-900 font-medium">Who is this product for ?</span>
                            </summary>
                            <div className="pl-6 mt-2 text-gray-800 leading-7 md:text-[17px]">
                            This service is perfect for business owners, marketers, and designers looking to create
                            stunning product visuals for eCommerce, marketing campaigns, and brand presentations.
                            </div>
                        </details>

                        <details className="group">
                            <summary className="flex items-center gap-2 cursor-pointer select-none py-2">
                            <ChevronRight className="h-4 w-4 text-gray-700 transition-transform group-open:rotate-90" />
                            <span className="text-gray-900 font-medium">The delivery method ?</span>
                            </summary>
                            <div className="pl-6 mt-2 text-gray-800 leading-7 md:text-[17px]">
                            Final renders will be delivered digitally via cloud storage in high-resolution formats
                            (JPG, PNG, PSD). All files include commercial usage rights.
                            </div>
                        </details>

                        <details className="group">
                            <summary className="flex items-center gap-2 cursor-pointer select-none py-2">
                            <ChevronRight className="h-4 w-4 text-gray-700 transition-transform group-open:rotate-90" />
                            <span className="text-gray-900 font-medium">Why Choose Me ?</span>
                            </summary>
                            <div className="pl-6 mt-2 text-gray-800">
                            <ul className="list-disc pl-4 space-y-1 leading-7 md:text-[17px]">
                                <li>5+ Years of 3D Rendering Experience</li>
                                <li>Fast Delivery & Revisions Available</li>
                                <li>100% Satisfaction Guaranteed</li>
                                <li>Professional quality and attention to detail</li>
                            </ul>
                            </div>
                        </details>
                        </div>
                    </aside>
                    </div>
                </div>
                </div>

              {/* Other Services Proposed */}
              {project?.price ? 
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Other Services proposed</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* {services?.filter((item: any) => item.id !== project.id) */}
                  {currentItems.map((item:any) => {
                    return (
                      <div
                        key={item.id}
                        className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer"
                        // onClick={() => navigate(`/professionals/${item.id}`)}
                        onClick={() => {
                                navigate('/details-search', {
                                  state: { service : item }
                                });
                          }}
                      >
                        {/* Image container - ratio 4:3 */}
                        <div className="relative w-full pb-[75%]">
                          {" "}
                          {/* 4:3 ratio */}
                          <div
                            style={{
                              backgroundImage: `url(${item.image_url})`,
                              borderRadius: "5px",
                            }}
                            className="absolute inset-0 bg-cover bg-center z-1000"
                          />
                        </div>

                        {/* Content container */}
                        <div className="px-0 py-0 pr-4 flex flex-col mb-4">
                          {/* Titre avec alignement fixe */}
                          <div className="flex items-center min-h-[27px]">
                            <h2
                              className="font-medium text-[18px] line-clamp-1 flex-1"
                              style={{
                                fontFamily: "Arial, sans-serif",
                                color: "#000000",
                                fontWeight: "bold",
                              }}
                            >
                              {item?.title}
                            </h2>
                            <div
                              className="flex items-center gap-4"
                              style={{ fontFamily: "Arial, sans-serif" }}
                            >
                              <span className="flex items-center gap-1 text-[12px]">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="ionicon"
                                  viewBox="0 0 512 512"
                                  width="14"
                                  height="14"
                                  fill="#787777ff"
                                >
                                  <circle cx="256" cy="256" r="64" />
                                  <path d="M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96c-42.52 0-84.33 12.15-124.27 36.11-40.73 24.43-77.63 60.12-109.68 106.07a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416c46.71 0 93.81-14.43 136.2-41.72 38.46-24.77 72.72-59.66 99.08-100.92a32.2 32.2 0 00-.1-34.76zM256 352a96 96 0 1196-96 96.11 96.11 0 01-96 96z" />
                                </svg>
                                {item?.views}
                              </span>

                              <span
                                className="flex items-center gap-1 text-[12px]"
                                // onClick={(e) => handleLike(e, item.id)}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="ionicon"
                                  viewBox="0 0 512 512"
                                  width="14"
                                  height="14"
                                  fill = "#787777ff"
                                  // fill={isLiked ? "#006EFF" : "#787777ff"}
                                >
                                  <path d="M400 480a16 16 0 01-10.63-4L256 357.41 122.63 476A16 16 0 0196 464V96a64.07 64.07 0 0164-64h192a64.07 64.07 0 0164 64v368a16 16 0 01-16 16z"></path>
                                </svg>
                                {item?.likes}
                              </span>
                            </div>
                          </div>

                          <div className="flex justify-between items-end">
                            {/* Auteur en bas à gauche */}
                            <div className="flex items-center gap-2">
                              <img
                                src={item?.avatar}
                                alt={item?.professional_name}
                                className="w-5 h-5 rounded-full object-cover border-white"
                              />
                              <div className="flex items-center gap-1">
                                <h3 className="text-gray-900 text-[12px] font-medium leading-tight mr-1">
                                  {item?.professional_name}
                                </h3>
                                {item?.isPro && (
                                  <span className="bg-[#000000] text-white text-[10px] rounded-full px-2.5 py-0.5 inline-block">
                                    PRO
                                  </span>
                                )}
                              </div>
                            </div>
                            
                          </div>
                          
                          <p className="text-[13px] mt-2">USD  <strong>{item?.price}</strong></p>

                        </div>
                      </div>
                    );
                  })}
                </div>

                 {/* ✅ Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center items-center gap-4 mt-6">
                      <button
                        // className="px-4 py-2 bg-gray-100 rounded-lg text-gray-600 hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        className="px-4 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        Précédent
                      </button>
                      {/* <span className="text-sm text-gray-700">
                        Page {currentPage} / {totalPages}
                      </span> */}
                      <button
                        // className="px-4 py-2 bg-gray-100 rounded-lg text-gray-600 hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        className="px-4 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                        onClick={() =>
                          setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                        }
                        disabled={currentPage === totalPages}
                      >
                        Suivant
                      </button>
                    </div>
                  )}
              </div>
              : 
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Other Achievements proposed</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* {projects?.filter((item: any) => item.id !== project.id) */}
                  {currentItemsAchievement.map((item:any) => {
                    // const isLiked = likedProfiles.includes(item.id);
                    // const totalLikes = likesCount[item.id] || item.likes;

                    return (
                      <div
                        key={item.id}
                        className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer"
                        // onClick={() => navigate(`/professionals/${item.id}`)}
                        onClick={() => {
                                navigate('/details-search', {
                                  state: { service : item }
                                });
                          }}
                      >
                        {/* Image container - ratio 4:3 */}
                        <div className="relative w-full pb-[75%]">
                          {" "}
                          {/* 4:3 ratio */}
                          <div
                            style={{
                              backgroundImage: `url(${item?.image_url})`,
                              borderRadius: "5px",
                            }}
                            className="absolute inset-0 bg-cover bg-center z-1000"
                          />
                        </div>

                        {/* Content container */}
                        <div className="px-0 py-0 pr-4 flex flex-col mb-4">
                          {/* Titre avec alignement fixe */}
                          <div className="flex items-center min-h-[27px]">
                            <h2
                              className="font-medium text-[18px] line-clamp-1 flex-1"
                              style={{
                                fontFamily: "Arial, sans-serif",
                                color: "#000000",
                                fontWeight: "bold",
                              }}
                            >
                              {item?.title}
                            </h2>
                            <div
                              className="flex items-center gap-4"
                              style={{ fontFamily: "Arial, sans-serif" }}
                            >
                              <span className="flex items-center gap-1 text-[12px]">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="ionicon"
                                  viewBox="0 0 512 512"
                                  width="14"
                                  height="14"
                                  fill="#787777ff"
                                >
                                  <circle cx="256" cy="256" r="64" />
                                  <path d="M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96c-42.52 0-84.33 12.15-124.27 36.11-40.73 24.43-77.63 60.12-109.68 106.07a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416c46.71 0 93.81-14.43 136.2-41.72 38.46-24.77 72.72-59.66 99.08-100.92a32.2 32.2 0 00-.1-34.76zM256 352a96 96 0 1196-96 96.11 96.11 0 01-96 96z" />
                                </svg>
                                {item.views}
                              </span>

                              <span
                                className="flex items-center gap-1 text-[12px]"
                                // onClick={(e) => handleLike(e, item.id)}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="ionicon"
                                  viewBox="0 0 512 512"
                                  width="14"
                                  height="14"
                                  fill = "#787777ff"
                                  // fill={isLiked ? "#006EFF" : "#787777ff"}
                                >
                                  <path d="M400 480a16 16 0 01-10.63-4L256 357.41 122.63 476A16 16 0 0196 464V96a64.07 64.07 0 0164-64h192a64.07 64.07 0 0164 64v368a16 16 0 01-16 16z"></path>
                                </svg>
                                {item.likes}
                              </span>
                            </div>
                          </div>

                          <div className="flex justify-between items-end">
                            {/* Auteur en bas à gauche */}
                            <div className="flex items-center gap-2">
                              <img
                                src={item?.avatar}
                                alt={item?.professional_name}
                                className="w-5 h-5 rounded-full object-cover border-white"
                              />
                              <div className="flex items-center gap-1">
                                <h3 className="text-gray-900 text-[12px] font-medium leading-tight mr-1">
                                  {item?.professional_name}
                                </h3>
                                {item?.isPro && (
                                  <span className="bg-[#000000] text-white text-[10px] rounded-full px-2.5 py-0.5 inline-block">
                                    PRO
                                  </span>
                                )}
                              </div>
                            </div>
                            {/* Métriques en bas à droite */}
                          </div>
                          
                          {/* <p className="text-[13px] mt-2">USD  <strong>{item.price}</strong></p> */}

                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* ✅ Pagination */}
                  {totalPagesAChievement > 1 && (
                    <div className="flex justify-center items-center gap-4 mt-6">
                      <button
                        // className="px-4 py-2 bg-gray-100 rounded-lg text-gray-600 hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        className="px-4 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        Précédent
                      </button>
                      {/* <span className="text-sm text-gray-700">
                        Page {currentPage} / {totalPages}
                      </span> */}
                      <button
                        // className="px-4 py-2 bg-gray-100 rounded-lg text-gray-600 hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        className="px-4 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                        onClick={() =>
                          setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                        }
                        disabled={currentPage === totalPages}
                      >
                        Suivant
                      </button>
                    </div>
                  )}
              </div>
              
              }
            </div>


            {/* Sidebar */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm sticky top-24">
                <div className="bg-gray-100 rounded-2xl p-4 shadow-sm sticky top-24 space-y-6">
                <div>
                    <h3 className="text-3xl font-bold text-gray-900 mb-2">{project?.price ? `From ${Number(project.price).toFixed(2)} USD` : ""}</h3>
                    <p className="text-gray-500 text-sm"><strong>Catégorie :</strong> {getCategoryLabel(project?.category)}</p>
                    <p className="text-gray-500 text-sm">{project?.concepts ? `Concepts ${project.concepts}` : ""}</p>
                    <p className="text-gray-500 text-sm">{project?.revisions ? `Revision ${project.revisions}` : ""}</p>
                    <p className="text-gray-500 text-sm">{project?.date_create
                        ? `Create at : ${new Date(project.date_create).toLocaleDateString("fr-FR")}`
                        : ""}</p>
                </div>

                <button
                    onClick={() => setShowQuoteModal(true)}
                    className="w-full bg-black text-white py-3 rounded-full font-medium hover:bg-gray-800"
                >
                    Let's make a deal
                </button>
                <button 
                    onClick={() => setShowQuoteModal(true)}
                    className="w-full bg-white text-gray-800 py-3 rounded-full font-medium hover:bg-gray-50"
                >
                    Invite to an open offer
                </button>
                </div>

                {/* Designer Profile */}
                <div className="bg-gray-100 rounded-2xl p-2 mt-4 shadow-sm sticky top-24 space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center">
                    <img
                        src={getImageUrl(professional?.avatar)}
                        alt={`${professional?.first_name} ${professional?.last_name}`}
                        className="w-28 h-28 rounded-full object-cover mr-3"
                        onError={(e) => {
                            e.currentTarget.onerror = null; 
                            e.currentTarget.src = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // chemin de l'image par défaut
                            }}
                    />
                    <div>
                        <p className="text-xs text-gray-500 mt-10">Visite portfolio</p>
                        <h4 className="text-sm font-semibold text-gray-900">{project?.professional_name||"Jack and Moris Render"}</h4>
                        <p className="text-xs text-gray-500">{professional?.title || 'Architecture visualisation'}</p>
                    </div>
                    </div>

                    <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{professional?.city}, {professional?.country}</span>
                    </div>

                    <div className="flex items-center">
                    <span className="bg-gray-900 text-white text-xs px-2 py-0.5 rounded mr-2">PRO</span>
                    <span className="text-sm text-gray-600">Pro acount</span>
                    </div>

                    <div className="flex items-center text-sm text-gray-600">
                    <span className="mr-2 text-xl text-gray-700">
                        <Languages className="w-4 h-4 mr-1" />
                    </span>
                    <ul className="flex space-x-2 list-none uppercase">
                        {professional?.languages?.map((language, index) => (
                        <li key={index}>{language}</li>
                        ))}
                    </ul>
                    </div>

                    <button className="w-full bg-white text-gray-800 py-2 rounded-full flex items-center justify-center hover:bg-gray-50">
                    <Pin className="w-4 h-4 mr-2" /> Save for later
                    </button>
                </div>
                </div>

              </div>
            </div>


          </div>
        </div>
      </div>

      <Footer />
      
      <QuoteRequestModal 
        token={token}
        user={user}
        pro={professional}
        open={showQuoteModal} 
        onOpenChange={setShowQuoteModal} 
      />
    </div>
  );
};

export default ServiceDetail;
