import { API_BASE_URL } from '../config';

/**
 * Fonction utilitaire pour obtenir l'URL correcte d'un avatar
 * @param avatarPath Chemin de l'avatar
 * @returns URL complète de l'avatar ou undefined si aucun chemin n'est fourni
 */
export const getAvatarUrl = (avatarPath?: string): string | undefined => {
  if (!avatarPath) return undefined;
  
  // Si le chemin est déjà une URL complète
  if (avatarPath.startsWith('http')) {
    return avatarPath;
  }
  
  // Si le chemin commence par /storage/
  if (avatarPath.startsWith('/storage/')) {
    return `${API_BASE_URL}${avatarPath}`;
  }
  
  // Sinon, on suppose que c'est un chemin relatif
  return `${API_BASE_URL}/api/storage/${avatarPath}`;
};

/**
 * Fonction utilitaire pour obtenir les initiales d'un nom complet
 * @param firstName Prénom
 * @param lastName Nom de famille
 * @returns Initiales (maximum 2 caractères)
 */
export const getInitials = (firstName?: string, lastName?: string): string => {
  const firstInitial = firstName && firstName.length > 0 ? firstName.charAt(0).toUpperCase() : '';
  const lastInitial = lastName && lastName.length > 0 ? lastName.charAt(0).toUpperCase() : '';
  
  return `${firstInitial}${lastInitial}`;
};
