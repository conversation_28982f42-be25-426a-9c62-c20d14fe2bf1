import { getAuthToken } from './auth';
import { API_BASE_URL } from '../config';

/**
 * Utility function for making API requests
 * @param endpoint API endpoint (without base URL)
 * @param options Fetch options
 * @returns Promise with response data
 */
export async function fetchApi<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const token = getAuthToken();
  
  // Prepare headers
  const headers = new Headers(options.headers);
  
  // Add content type if not already set and not FormData
  if (
    !headers.has('Content-Type') && 
    !(options.body instanceof FormData)
  ) {
    headers.set('Content-Type', 'application/json');
  }
  
  // Add authorization token if available
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }
  
  // Prepare URL
  const url = `${API_BASE_URL}${endpoint}`;
  
  // Make the request
  const response = await fetch(url, {
    ...options,
    headers,
  });
  
  // Handle non-2xx responses
  if (!response.ok) {
    try {
      const errorData = await response.json();
      throw new Error(JSON.stringify({
        status: response.status,
        message: errorData.message || response.statusText,
        errors: errorData.errors,
      }));
    } catch (e) {
      // If parsing JSON fails, throw a generic error
      throw new Error(JSON.stringify({
        status: response.status,
        message: response.statusText,
      }));
    }
  }
  
  // For 204 No Content responses, return empty object
  if (response.status === 204) {
    return {} as T;
  }
  
  // Parse and return JSON response
  return await response.json();
}
