import React, { ReactNode } from 'react';
import Container from './Container';

export interface SectionProps {
  children: ReactNode;
  className?: string;
  background?: 'white' | 'light' | 'primary' | 'secondary';
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  containerWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  id?: string;
}

const Section: React.FC<SectionProps> = ({
  children,
  className = '',
  background = 'white',
  spacing = 'lg',
  containerWidth = 'xl',
  id,
}) => {
  // Background classes
  const backgroundClasses = {
    white: 'bg-white',
    light: 'bg-neutral-50',
    primary: 'bg-primary-600 text-white',
    secondary: 'bg-secondary-50',
  };

  // Spacing classes
  const spacingClasses = {
    none: 'py-0',
    sm: 'py-4 md:py-6',
    md: 'py-6 md:py-8',
    lg: 'py-8 md:py-12',
    xl: 'py-12 md:py-16',
  };

  return (
    <section
      className={`${backgroundClasses[background]} ${spacingClasses[spacing]} ${className}`}
      id={id}
    >
      <Container maxWidth={containerWidth}>
        {children}
      </Container>
    </section>
  );
};

export default Section;
