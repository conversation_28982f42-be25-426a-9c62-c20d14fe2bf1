import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Plus, AlertCircle } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import ProjectList from './ProjectList';
import ProjectForm, { ProjectFormData } from './ProjectForm';
import ProjectDetails from './ProjectDetails';
import Button from '../ui/Button';

const ProjectManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id?: string }>();
  const [projects, setProjects] = useState<any[]>([]);
  const [selectedProject, setSelectedProject] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formLoading, setFormLoading] = useState(false);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');
  const isProfessional = user?.is_professional === true;

  // Fetch projects
  useEffect(() => {
    const fetchProjects = async () => {
      setIsLoading(true);
      try {
        const endpoint = isProfessional
          ? `${API_BASE_URL}/api/open-offers`
          : `${API_BASE_URL}/api/users/${user.id}/open-offers`;

        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des projets');
        }

        const data = await response.json();

        // Transform API data to ProjectCard format
        const formattedProjects = data.offers.map((offer: any) => ({
          id: offer.id,
          title: offer.title,
          description: offer.description,
          budget: offer.budget,
          deadline: offer.deadline,
          status: offer.status,
          categories: offer.categories || [],
          client: {
            id: offer.user_id,
            name: `${offer.user.first_name} ${offer.user.last_name}`,
            avatar: offer.user.avatar,
          },
          applicationsCount: offer.applications_count || 0,
        }));

        setProjects(formattedProjects);

        // If ID is provided in URL, select that project
        if (id) {
          const project = formattedProjects.find((p: any) => p.id === parseInt(id));
          if (project) {
            setSelectedProject(project);
          } else {
            // Project not found, redirect to projects list
            navigate('/projects');
          }
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError('Impossible de charger les projets');
      } finally {
        setIsLoading(false);
      }
    };

    if (token) {
      fetchProjects();
    }
  }, [token, user.id, isProfessional, id, navigate]);

  // Handle project click
  const handleProjectClick = (projectId: number) => {
    const project = projects.find(p => p.id === projectId);
    if (project) {
      setSelectedProject(project);
      navigate(`/projects/${projectId}`);
    }
  };

  // Handle create project
  const handleCreateProject = () => {
    setSelectedProject(null);
    setIsEditMode(false);
    setShowForm(true);
  };

  // Handle edit project
  const handleEditProject = () => {
    setIsEditMode(true);
    setShowForm(true);
  };

  // Handle delete project
  const handleDeleteProject = async () => {
    if (!selectedProject) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${selectedProject.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression du projet');
      }

      // Remove project from state
      setProjects(prev => prev.filter(p => p.id !== selectedProject.id));
      setSelectedProject(null);
      navigate('/projects');
    } catch (err) {
      console.error('Error deleting project:', err);
      alert('Erreur lors de la suppression du projet');
    }
  };

  // Handle form submission
  const handleFormSubmit = async (formData: ProjectFormData) => {
    setFormLoading(true);

    try {
      const method = isEditMode ? 'PUT' : 'POST';
      const endpoint = isEditMode
        ? `${API_BASE_URL}/api/open-offers/${selectedProject.id}`
        : `${API_BASE_URL}/api/open-offers`;

      // Format deadline
      const formattedDeadline = new Date(formData.deadline).toISOString();

      // Prepare payload
      const payload = {
        title: formData.title,
        categories: formData.categories,
        budget: formData.budget,
        deadline: formattedDeadline,
        company: formData.company,
        website: formData.website,
        description: formData.description,
        recruitment_type: formData.recruitmentType,
        open_to_applications: formData.openToApplications,
        auto_invite: formData.autoInvite,
        status: formData.status,
        filters: formData.filters,
      };

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Erreur lors de ${isEditMode ? 'la mise à jour' : 'la création'} du projet`);
      }

      const data = await response.json();

      if (isEditMode) {
        // Update project in state
        setProjects(prev => prev.map(p =>
          p.id === selectedProject.id
            ? {
                ...p,
                title: formData.title,
                description: formData.description,
                budget: formData.budget,
                deadline: formattedDeadline,
                status: formData.status,
                categories: formData.categories,
              }
            : p
        ));

        // Update selected project
        setSelectedProject((prev: any) => ({
          ...prev,
          title: formData.title,
          description: formData.description,
          budget: formData.budget,
          deadline: formattedDeadline,
          status: formData.status,
          categories: formData.categories,
        }));
      } else {
        // Add new project to state
        const newProject = {
          id: data.offer.id,
          title: formData.title,
          description: formData.description,
          budget: formData.budget,
          deadline: formattedDeadline,
          status: formData.status,
          categories: formData.categories,
          client: {
            id: user.id,
            name: `${user.first_name} ${user.last_name}`,
            avatar: user.avatar,
          },
          applicationsCount: 0,
        };

        setProjects(prev => [newProject, ...prev]);
        setSelectedProject(newProject);
        navigate(`/projects/${newProject.id}`);
      }

      setShowForm(false);
    } catch (err) {
      console.error('Error submitting project:', err);
      alert(`Erreur lors de ${isEditMode ? 'la mise à jour' : 'la création'} du projet`);
    } finally {
      setFormLoading(false);
    }
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setShowForm(false);
  };

  // Handle apply to project
  const handleApplyToProject = () => {
    if (!selectedProject) return;

    // Navigate to application form
    navigate(`/projects/${selectedProject.id}/apply`);
  };

  // Handle contact client
  const handleContactClient = () => {
    if (!selectedProject) return;

    // Navigate to messages with client
    navigate(`/discussions/${selectedProject.client.id}`);
  };

  // Handle share project
  const handleShareProject = () => {
    if (!selectedProject) return;

    // Copy project URL to clipboard
    const url = `${window.location.origin}/projects/${selectedProject.id}`;
    navigator.clipboard.writeText(url);
    alert('Lien du projet copié dans le presse-papier');
  };

  return (
    <DashboardLayout
      title={selectedProject ? selectedProject.title : 'Gestion des projets'}
      subtitle={selectedProject
        ? `Projet ${selectedProject.status === 'open' ? 'ouvert' : selectedProject.status === 'in_progress' ? 'en cours' : selectedProject.status === 'completed' ? 'terminé' : 'annulé'}`
        : isProfessional
          ? 'Trouvez des projets qui correspondent à vos compétences'
          : 'Gérez vos projets et trouvez des professionnels'
      }
      actions={
        selectedProject ? (
          <Button
            variant="outline"
            onClick={() => {
              setSelectedProject(null);
              navigate('/projects');
            }}
          >
            Retour à la liste
          </Button>
        ) : !isProfessional ? (
          <Button
            variant="primary"
            leftIcon={<Plus className="h-5 w-5" />}
            onClick={handleCreateProject}
            style={{ backgroundColor: '#2980b9', color: 'white', padding: '0.75rem 1.5rem', fontWeight: 'bold', borderRadius: '0.5rem' }}
          >
            Créer un projet
          </Button>
        ) : undefined
      }
    >
      {error && !isLoading && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-start">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {showForm ? (
        <ProjectForm
          initialData={isEditMode ? selectedProject : undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          isLoading={formLoading}
        />
      ) : selectedProject ? (
        <ProjectDetails
          {...selectedProject}
          createdAt={selectedProject.created_at || new Date().toISOString()}
          updatedAt={selectedProject.updated_at || new Date().toISOString()}
          viewsCount={selectedProject.views_count || 0}
          recruitmentType={selectedProject.recruitment_type || 'company'}
          openToApplications={selectedProject.open_to_applications !== false}
          onEdit={!isProfessional ? handleEditProject : undefined}
          onDelete={!isProfessional ? handleDeleteProject : undefined}
          onApply={isProfessional ? handleApplyToProject : undefined}
          onContact={isProfessional ? handleContactClient : undefined}
          onShare={handleShareProject}
          isOwner={!isProfessional}
          isProfessional={isProfessional}
        />
      ) : (
        <ProjectList
          projects={projects}
          isLoading={isLoading}
          error={error || undefined}
          onProjectClick={handleProjectClick}
          onCreateProject={!isProfessional ? handleCreateProject : undefined}
          isProfessional={isProfessional}
          emptyMessage={isProfessional
            ? 'Aucun projet disponible pour le moment'
            : 'Vous n\'avez pas encore créé de projet'
          }
        />
      )}
    </DashboardLayout>
  );
};

export default ProjectManagementPage;
