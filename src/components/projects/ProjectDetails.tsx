import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Calendar, 
  DollarSign, 
  Briefcase, 
  Globe, 
  FileText, 
  Clock, 
  Users, 
  Edit, 
  Trash, 
  MessageSquare, 
  Share, 
  Eye, 
  CheckCircle, 
  XCircle,
  AlertTriangle
} from 'lucide-react';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Avatar from '../ui/Avatar';

export interface ProjectDetailsProps {
  id: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company?: string;
  website?: string;
  description: string;
  recruitmentType: 'company' | 'personal';
  openToApplications: boolean;
  status: 'draft' | 'open' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  viewsCount: number;
  applicationsCount: number;
  client: {
    id: number;
    name: string;
    avatar?: string;
    location?: string;
    rating?: number;
    projectsCompleted?: number;
  };
  professional?: {
    id: number;
    name: string;
    avatar?: string;
    rating?: number;
  };
  files?: {
    name: string;
    url: string;
    type: string;
  }[];
  onEdit?: () => void;
  onDelete?: () => void;
  onApply?: () => void;
  onContact?: () => void;
  onShare?: () => void;
  isOwner?: boolean;
  isProfessional?: boolean;
}

const ProjectDetails: React.FC<ProjectDetailsProps> = ({
  id,
  title,
  categories,
  budget,
  deadline,
  company,
  website,
  description,
  recruitmentType,
  openToApplications,
  status,
  createdAt,
  updatedAt,
  viewsCount,
  applicationsCount,
  client,
  professional,
  files = [],
  onEdit,
  onDelete,
  onApply,
  onContact,
  onShare,
  isOwner = false,
  isProfessional = false,
}) => {
  const navigate = useNavigate();
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  // Calculate days remaining
  const getDaysRemaining = () => {
    const deadlineDate = new Date(deadline);
    const now = new Date();
    const diffTime = deadlineDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return 'Délai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  // Status badge configuration
  const statusConfig = {
    draft: { label: 'Brouillon', color: 'neutral' },
    open: { label: 'Ouvert', color: 'success' },
    in_progress: { label: 'En cours', color: 'primary' },
    completed: { label: 'Terminé', color: 'neutral' },
    cancelled: { label: 'Annulé', color: 'danger' },
  };

  // Handle delete confirmation
  const handleDeleteClick = () => {
    setShowConfirmDelete(true);
  };

  const handleConfirmDelete = () => {
    if (onDelete) {
      onDelete();
    }
    setShowConfirmDelete(false);
  };

  const handleCancelDelete = () => {
    setShowConfirmDelete(false);
  };

  // Handle client or professional profile click
  const handleProfileClick = (profileId: number, isPro: boolean) => {
    navigate(isPro ? `/artist/${profileId}` : `/profile/${profileId}`);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      {/* Project Header */}
      <div className="p-6 border-b border-neutral-200">
        <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <h1 className="text-2xl font-bold text-neutral-900">{title}</h1>
              <Badge color={statusConfig[status].color as any}>
                {statusConfig[status].label}
              </Badge>
            </div>
            
            <div className="flex flex-wrap gap-2 mb-4">
              {categories.map((category) => (
                <span
                  key={category}
                  className="px-3 py-1 bg-neutral-100 text-neutral-800 rounded-full text-sm"
                >
                  {category}
                </span>
              ))}
            </div>
            
            <div className="flex flex-wrap gap-x-6 gap-y-2 text-sm text-neutral-600">
              <div className="flex items-center">
                <DollarSign className="h-4 w-4 mr-1 text-neutral-500" />
                <span className="font-medium">{budget}</span>
              </div>
              
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1 text-neutral-500" />
                <span>{formatDate(deadline)}</span>
              </div>
              
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1 text-neutral-500" />
                <span>{getDaysRemaining()}</span>
              </div>
              
              <div className="flex items-center">
                <Eye className="h-4 w-4 mr-1 text-neutral-500" />
                <span>{viewsCount} vue{viewsCount !== 1 ? 's' : ''}</span>
              </div>
              
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-1 text-neutral-500" />
                <span>{applicationsCount} candidature{applicationsCount !== 1 ? 's' : ''}</span>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            {isOwner ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  leftIcon={<Edit className="h-4 w-4" />}
                  onClick={onEdit}
                >
                  Modifier
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  leftIcon={<Trash className="h-4 w-4" />}
                  onClick={handleDeleteClick}
                >
                  Supprimer
                </Button>
              </>
            ) : isProfessional ? (
              <>
                {status === 'open' && openToApplications && (
                  <Button
                    variant="primary"
                    size="sm"
                    leftIcon={<Briefcase className="h-4 w-4" />}
                    onClick={onApply}
                  >
                    Postuler
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  leftIcon={<MessageSquare className="h-4 w-4" />}
                  onClick={onContact}
                >
                  Contacter
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Share className="h-4 w-4" />}
                onClick={onShare}
              >
                Partager
              </Button>
            )}
          </div>
        </div>
      </div>
      
      {/* Project Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <div>
            <h2 className="text-lg font-semibold text-neutral-900 mb-3">Description du projet</h2>
            <div className="prose prose-neutral max-w-none">
              {description.split('\n').map((paragraph, index) => (
                <p key={index} className="mb-4 text-neutral-700">
                  {paragraph}
                </p>
              ))}
            </div>
          </div>
          
          {/* Files */}
          {files.length > 0 && (
            <div>
              <h2 className="text-lg font-semibold text-neutral-900 mb-3">Fichiers joints</h2>
              <ul className="divide-y divide-neutral-200 border border-neutral-200 rounded-md overflow-hidden">
                {files.map((file, index) => (
                  <li key={index} className="px-4 py-3 flex items-center text-sm">
                    <FileText className="h-5 w-5 text-neutral-400 mr-3" />
                    <span className="flex-1 truncate">{file.name}</span>
                    <a
                      href={file.url}
                      download
                      className="ml-4 font-medium text-primary-600 hover:text-primary-500"
                    >
                      Télécharger
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          {/* Client Information (for professionals) */}
          {isProfessional && (
            <div>
              <h2 className="text-lg font-semibold text-neutral-900 mb-3">À propos du client</h2>
              <div 
                className="flex items-start p-4 bg-neutral-50 rounded-lg border border-neutral-200 cursor-pointer hover:bg-neutral-100"
                onClick={() => handleProfileClick(client.id, false)}
              >
                <Avatar
                  src={client.avatar}
                  fallback={client.name.charAt(0)}
                  size="lg"
                  className="mr-4"
                />
                <div>
                  <h3 className="font-medium text-neutral-900">{client.name}</h3>
                  {client.location && (
                    <p className="text-sm text-neutral-600 mb-2">{client.location}</p>
                  )}
                  <div className="flex flex-wrap gap-x-4 gap-y-1 text-sm">
                    {client.rating !== undefined && (
                      <div className="flex items-center">
                        <span className="text-yellow-500 mr-1">★</span>
                        <span>{client.rating.toFixed(1)}</span>
                      </div>
                    )}
                    {client.projectsCompleted !== undefined && (
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                        <span>{client.projectsCompleted} projet{client.projectsCompleted !== 1 ? 's' : ''} terminé{client.projectsCompleted !== 1 ? 's' : ''}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Professional Information (for clients, if assigned) */}
          {isOwner && professional && status === 'in_progress' && (
            <div>
              <h2 className="text-lg font-semibold text-neutral-900 mb-3">Professionnel assigné</h2>
              <div 
                className="flex items-start p-4 bg-neutral-50 rounded-lg border border-neutral-200 cursor-pointer hover:bg-neutral-100"
                onClick={() => handleProfileClick(professional.id, true)}
              >
                <Avatar
                  src={professional.avatar}
                  fallback={professional.name.charAt(0)}
                  size="lg"
                  className="mr-4"
                />
                <div>
                  <h3 className="font-medium text-neutral-900">{professional.name}</h3>
                  {professional.rating !== undefined && (
                    <div className="flex items-center text-sm">
                      <span className="text-yellow-500 mr-1">★</span>
                      <span>{professional.rating.toFixed(1)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Sidebar */}
        <div className="space-y-6">
          {/* Project Details */}
          <div className="bg-neutral-50 rounded-lg border border-neutral-200 overflow-hidden">
            <div className="px-4 py-3 bg-neutral-100 border-b border-neutral-200">
              <h3 className="font-medium text-neutral-800">Détails du projet</h3>
            </div>
            <div className="p-4 space-y-3">
              <div>
                <p className="text-sm text-neutral-500">Publié le</p>
                <p className="font-medium">{formatDate(createdAt)}</p>
              </div>
              
              <div>
                <p className="text-sm text-neutral-500">Date limite</p>
                <p className="font-medium">{formatDate(deadline)}</p>
              </div>
              
              <div>
                <p className="text-sm text-neutral-500">Budget</p>
                <p className="font-medium">{budget}</p>
              </div>
              
              {company && (
                <div>
                  <p className="text-sm text-neutral-500">Entreprise</p>
                  <p className="font-medium">{company}</p>
                </div>
              )}
              
              {website && (
                <div>
                  <p className="text-sm text-neutral-500">Site web</p>
                  <a 
                    href={website.startsWith('http') ? website : `https://${website}`} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="font-medium text-primary-600 hover:text-primary-700"
                  >
                    {website}
                  </a>
                </div>
              )}
              
              <div>
                <p className="text-sm text-neutral-500">Type de recrutement</p>
                <p className="font-medium">{recruitmentType === 'company' ? 'Entreprise' : 'Personnel'}</p>
              </div>
              
              <div>
                <p className="text-sm text-neutral-500">Candidatures</p>
                <p className="font-medium">{openToApplications ? 'Ouvert aux candidatures' : 'Sur invitation uniquement'}</p>
              </div>
            </div>
          </div>
          
          {/* Call to Action */}
          {!isOwner && isProfessional && status === 'open' && openToApplications && (
            <div className="bg-primary-50 rounded-lg border border-primary-200 p-4 text-center">
              <h3 className="font-semibold text-primary-800 mb-2">Ce projet vous intéresse ?</h3>
              <p className="text-primary-700 text-sm mb-4">Postulez maintenant pour montrer votre intérêt et discuter des détails avec le client.</p>
              <Button
                variant="primary"
                fullWidth
                onClick={onApply}
              >
                Postuler à ce projet
              </Button>
            </div>
          )}
          
          {/* Share Project */}
          <div className="bg-neutral-50 rounded-lg border border-neutral-200 p-4 text-center">
            <h3 className="font-semibold text-neutral-800 mb-2">Partager ce projet</h3>
            <p className="text-neutral-600 text-sm mb-4">Vous connaissez quelqu'un qui pourrait être intéressé par ce projet ?</p>
            <Button
              variant="outline"
              fullWidth
              leftIcon={<Share className="h-4 w-4" />}
              onClick={onShare}
            >
              Partager
            </Button>
          </div>
        </div>
      </div>
      
      {/* Delete Confirmation Modal */}
      {showConfirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-500 mr-3" />
              <h3 className="text-lg font-semibold text-neutral-900">Confirmer la suppression</h3>
            </div>
            <p className="text-neutral-700 mb-6">
              Êtes-vous sûr de vouloir supprimer ce projet ? Cette action est irréversible.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={handleCancelDelete}
              >
                Annuler
              </Button>
              <Button
                variant="danger"
                onClick={handleConfirmDelete}
              >
                Supprimer
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectDetails;
