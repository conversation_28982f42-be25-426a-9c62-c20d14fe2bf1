import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Filter, ThumbsUp, ThumbsDown } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import OfferInvitationCard from '../dashboard/OfferInvitationCard';
import Alert from '../ui/Alert';

interface ReceivedOffer {
  id: number;
  id_offer : number;
  title: string;
  description: string;
  budget: string;
  deadline: string;
  createdAt: string;
  isInvited: boolean;
  status : string;
  client: {
    id: number;
    name: string;
    avatar?: string;
  };
}

const ReceivedOffersPage: React.FC = () => {
  const navigate = useNavigate();
  const [receivedOffers, setReceivedOffers] = useState<ReceivedOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'invited', 'regular'

  const token = localStorage.getItem('token');

  useEffect(() => {
    const fetchReceivedOffers = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/offer-applications/received`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des appels d\'offre reçus');
        }

        const data = await response.json();

        if (data.offers && data.offers.length > 0) {
          setReceivedOffers(data.offers.map((offer: any) => ({
            id: offer.id,
            id_offer: offer.id_offer,
            title: offer.title,
            description: offer.description,
            budget: offer.budget,
            deadline: offer.deadline,
            createdAt: offer.created_at,
            isInvited: offer.is_invited || false,
            status: offer.status,
            client: {
              id: offer.client.id,
              name: offer.client.name,
              avatar: offer.client.avatar,
            },
          })));
        } else {
          // Si aucune offre n'est trouvée, utiliser des données de secours
          loadStaticData();
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les appels d\'offre reçus');
        loadStaticData();
      } finally {
        setLoading(false);
      }
    };

    // Fonction pour charger des données statiques en cas d'erreur
    const loadStaticData = () => {
      setReceivedOffers([
        {
          id: 201,
          id_offer: 4,
          title: 'Création d\'un environnement 3D pour jeu mobile',
          description: 'Nous recherchons un artiste 3D pour créer un environnement complet pour notre jeu mobile d\'aventure.',
          budget: '1 500 €',
          deadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days from now
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
          isInvited: true,
          status:'accepted',
          client: {
            id: 101,
            name: 'MobileGames Studio',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
        },
        {
          id: 202,
          id_offer: 5,
          title: 'Modélisation de personnages pour série animée',
          description: 'Projet de modélisation de 5 personnages principaux pour une série animée en 3D.',
          budget: '2 000 €',
          deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
          isInvited: false,
          status:'accepted',
          client: {
            id: 102,
            name: 'Animation Productions',
            avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
          },
        },
        {
          id: 203,
          id_offer: 6,
          title: 'Animation de logo pour entreprise tech',
          description: 'Animation 3D d\'un logo pour une entreprise de technologie, à utiliser sur leur site web et leurs réseaux sociaux.',
          budget: '800 €',
          deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days from now
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
          isInvited: true,
          status:'accepted',
          client: {
            id: 103,
            name: 'TechCorp',
            avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
          },
        },
      ]);
    };

    fetchReceivedOffers();
  }, [token]);

  const handleViewOffer = (offerId: number) => {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const isClient = user.role === 'client' || !user.is_professional;

    if (isClient) {
      navigate(`/dashboard/client/offers/${offerId}`);
    } else {
      navigate(`/dashboard/offers/${offerId}`);
    }
  };

  const handleAcceptOffer = async (offerId: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/offer-applications/${offerId}/accept`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de l\'acceptation de l\'offre');
      }

      // Mettre à jour la liste des offres reçues
      // setReceivedOffers(receivedOffers.filter(offer => offer.id !== offerId));
      window.location.reload();


      // Afficher un message de succès
      setSuccessMessage('Vous avez indiqué votre intérêt pour cette offre. Le client en sera informé et pourra vous contacter.');
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible d\'accepter l\'offre. Veuillez réessayer plus tard.');
    }
  };

  const handleDeclineOffer = async (offerId: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/offer-applications/${offerId}/decline`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors du refus de l\'offre');
      }

      // Mettre à jour la liste des offres reçues
      // setReceivedOffers(receivedOffers.filter(offer => offer.id !== offerId));
      window.location.reload();


      // Afficher un message de succès
      setSuccessMessage('Vous avez indiqué que vous n\'êtes pas disponible pour cette offre.');
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de refuser l\'offre. Veuillez réessayer plus tard.');
    }
  };

  // Filtrer les offres en fonction de la recherche et du filtre
  // const filteredOffers = receivedOffers.filter(offer => {
  //   const matchesSearch = offer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
  //                        offer.description.toLowerCase().includes(searchQuery.toLowerCase());

  //   if (filter === 'all') return matchesSearch;
  //   if (filter === 'invited') return matchesSearch && offer.isInvited;
  //   if (filter === 'regular') return matchesSearch && !offer.isInvited;

  //   return matchesSearch;
  // });

  const filteredOffers = receivedOffers.filter(offer => {
  const matchesSearch =
    offer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    offer.description.toLowerCase().includes(searchQuery.toLowerCase());

  if (!matchesSearch) return false;

  if (filter === 'all') return true;

  return offer.status === filter;
});



  return (
    <DashboardLayout
      title="Appels d'offre reçus"
      subtitle="Consultez et répondez aux appels d'offre qui correspondent à votre profil"
    >
      {/* Success Alert */}
      {successMessage && (
        <Alert
          type="success"
          title="Succès"
          onClose={() => setSuccessMessage(null)}
          className="mb-6"
        >
          {successMessage}
        </Alert>
      )}

      {/* Error Alert */}
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {/* Search and Filter */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden mb-6">
        <div className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="Rechercher des appels d'offre..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-neutral-400" />
            </div>

            {/* Filter Dropdown */}
            <div className="w-full md:w-auto">
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">Tous les appels d'offre</option>
                <option value="pending">En attente</option>
                <option value="accepted">Offres acceptées</option>
                <option value="rejected">Offres refusées</option>
                <option value="invited">Invitations personnalisées</option>
                {/* <option value="all">Tous les appels d'offre</option>
                <option value="invited">Invitations personnalisées</option>
                <option value="regular">Appels d'offre standards</option> */}
              </select>
            </div>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : filteredOffers.length === 0 ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
          <h3 className="text-lg font-medium text-neutral-700 mb-4">Aucun appel d'offre trouvé</h3>
          <p className="text-neutral-500 mb-6">
            {searchQuery || filter !== 'all'
              ? 'Aucun résultat ne correspond à votre recherche. Essayez de modifier vos critères.'
              : 'Vous n\'avez pas encore reçu d\'appels d\'offre. Complétez votre profil pour augmenter vos chances d\'être contacté.'}
          </p>
          <Button
            variant="primary"
            onClick={() => navigate('/dashboard/profile/edit')}
            style={{ backgroundColor: '#2980b9', color: 'black' }}
          >
            Compléter mon profil
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredOffers.map(offer => (
            <OfferInvitationCard
              key={offer.id}
              {...offer}
              onAccept={handleAcceptOffer}
              onDecline={handleDeclineOffer}
              onView={handleViewOffer}
            />
          ))}
        </div>
      )}
    </DashboardLayout>
  );
};

export default ReceivedOffersPage;
