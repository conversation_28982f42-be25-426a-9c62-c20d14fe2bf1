import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Send, Edit, Trash2, AlertTriangle } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import PendingOfferCard from '../dashboard/PendingOfferCard';
import Modal from '../ui/Modal';
import Alert from '../ui/Alert';

interface PendingOffer {
  id: number;
  title: string;
  description: string;
  createdAt: string;
  filters: {
    location?: string;
    languages?: string[];
    skills?: string[];
  };
  maxProposals?: number;
}

const PendingOffersPage: React.FC = () => {
  const navigate = useNavigate();
  const [pendingOffers, setPendingOffers] = useState<PendingOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPublishModal, setShowPublishModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedOfferId, setSelectedOfferId] = useState<number | null>(null);
  const [publishSuccess, setPublishSuccess] = useState<string | null>(null);

  const token = localStorage.getItem('token');

  useEffect(() => {
    const fetchPendingOffers = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/open-offers?status=pending`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des appels d\'offre en attente');
        }

        const data = await response.json();

        if (data.open_offers && data.open_offers.length > 0) {
          setPendingOffers(data.open_offers.map((offer: any) => ({
            id: offer.id,
            title: offer.title,
            description: offer.description,
            createdAt: offer.created_at,
            filters: {
              location: offer.location,
              languages: offer.languages || [],
              skills: offer.skills || [],
            },
            maxProposals: offer.max_proposals || 10,
          })));
        } else {
          // Si aucun appel d'offre n'est trouvé, utiliser des données de secours
          loadStaticData();
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les appels d\'offre en attente');
        loadStaticData();
      } finally {
        setLoading(false);
      }
    };

    // Fonction pour charger des données statiques en cas d'erreur
    const loadStaticData = () => {
      setPendingOffers([
        {
          id: 101,
          title: 'Création d\'un environnement 3D pour jeu mobile',
          description: 'Nous recherchons un artiste 3D pour créer un environnement complet pour notre jeu mobile d\'aventure.',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
          filters: {
            location: 'France',
            languages: ['Français', 'Anglais'],
            skills: ['Blender', 'Unity', 'Texturing'],
          },
          maxProposals: 5,
        },
        {
          id: 102,
          title: 'Modélisation de personnages pour série animée',
          description: 'Projet de modélisation de 5 personnages principaux pour une série animée en 3D.',
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
          filters: {
            location: 'Europe',
            languages: ['Français'],
            skills: ['Character Design', 'Maya', 'ZBrush'],
          },
          maxProposals: 8,
        },
        {
          id: 103,
          title: 'Animation de logo pour entreprise tech',
          description: 'Animation 3D d\'un logo pour une entreprise de technologie, à utiliser sur leur site web et leurs réseaux sociaux.',
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
          filters: {
            location: 'International',
            languages: ['Anglais'],
            skills: ['After Effects', 'Cinema 4D', 'Motion Graphics'],
          },
          maxProposals: 6,
        },
      ]);
    };

    fetchPendingOffers();
  }, [token]);

  const handleCreateOffer = () => {
    navigate('/dashboard/create-project');
  };

  const handleEditOffer = (id: number) => {
    navigate(`/dashboard/edit-project/${id}`);
  };

  const handlePublishClick = (id: number) => {
    setSelectedOfferId(id);
    setShowPublishModal(true);
  };

  const handlePublishConfirm = async () => {
    if (!selectedOfferId) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${selectedOfferId}/publish`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la publication de l\'appel d\'offre');
      }

      // Mettre à jour la liste des appels d'offre en attente
      setPendingOffers(pendingOffers.filter(offer => offer.id !== selectedOfferId));
      setPublishSuccess('Appel d\'offre publié avec succès ! Les professionnels correspondant aux critères ont été notifiés.');
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de publier l\'appel d\'offre');
    } finally {
      setShowPublishModal(false);
      setSelectedOfferId(null);
    }
  };

  const handleDeleteClick = (id: number) => {
    setSelectedOfferId(id);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedOfferId) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${selectedOfferId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression de l\'appel d\'offre');
      }

      // Mettre à jour la liste des appels d'offre en attente
      setPendingOffers(pendingOffers.filter(offer => offer.id !== selectedOfferId));
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de supprimer l\'appel d\'offre');
    } finally {
      setShowDeleteModal(false);
      setSelectedOfferId(null);
    }
  };

  return (
    <DashboardLayout
      title="Appels d'offre en attente"
      subtitle="Gérez vos appels d'offre avant leur publication"
      actions={
        <Button
          variant="primary"
          leftIcon={<Plus className="h-5 w-5" />}
          onClick={handleCreateOffer}
          style={{ backgroundColor: '#2980b9', color: 'black' }}
        >
          Créer un appel d'offre
        </Button>
      }
    >
      {/* Success Alert */}
      {publishSuccess && (
        <Alert
          type="success"
          title="Succès"
          onClose={() => setPublishSuccess(null)}
          className="mb-6"
        >
          {publishSuccess}
        </Alert>
      )}

      {/* Error Alert */}
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : pendingOffers.length === 0 ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
          <h3 className="text-lg font-medium text-neutral-700 mb-4">Aucun appel d'offre en attente</h3>
          <p className="text-neutral-500 mb-6">Créez un nouvel appel d'offre pour commencer à trouver des professionnels.</p>
          <Button
            variant="primary"
            onClick={handleCreateOffer}
            style={{ backgroundColor: '#2980b9', color: 'black' }}
          >
            Créer un appel d'offre
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {pendingOffers.map(offer => (
            <PendingOfferCard
              key={offer.id}
              {...offer}
              onPublish={handlePublishClick}
              onEdit={handleEditOffer}
              onDelete={handleDeleteClick}
            />
          ))}
        </div>
      )}

      {/* Publish Confirmation Modal */}
      <Modal
        isOpen={showPublishModal}
        onClose={() => setShowPublishModal(false)}
        title="Publier l'appel d'offre"
      >
        <div className="p-6">
          <p className="mb-6">
            Êtes-vous sûr de vouloir publier cet appel d'offre ? Une fois publié, il sera envoyé aux professionnels correspondant aux critères que vous avez définis.
          </p>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowPublishModal(false)}
            >
              Annuler
            </Button>

            <Button
              variant="primary"
              leftIcon={<Send className="h-4 w-4" />}
              onClick={handlePublishConfirm}
              style={{ backgroundColor: '#2980b9', color: 'black' }}
            >
              Publier
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Supprimer l'appel d'offre"
      >
        <div className="p-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="h-6 w-6 text-red-500 mr-3" />
            <p className="mb-6">
              Êtes-vous sûr de vouloir supprimer cet appel d'offre ? Cette action est irréversible.
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              Annuler
            </Button>

            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
            >
              Supprimer
            </Button>
          </div>
        </div>
      </Modal>
    </DashboardLayout>
  );
};

export default PendingOffersPage;
