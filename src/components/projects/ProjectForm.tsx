import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Calendar, 
  DollarSign, 
  Briefcase, 
  Globe, 
  FileText, 
  Tag, 
  Clock, 
  Users, 
  Upload, 
  X, 
  Check, 
  AlertCircle 
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';
import FormSelect from '../ui/FormSelect';
import Checkbox from '../ui/Checkbox';
import Badge from '../ui/Badge';

export interface ProjectFormData {
  id?: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company: string;
  website: string;
  description: string;
  recruitmentType: 'company' | 'personal';
  openToApplications: boolean;
  autoInvite: boolean;
  status: 'draft' | 'open' | 'in_progress' | 'completed' | 'cancelled';
  files: File[];
  filters: {
    languages: string[];
    skills: string[];
    location: string;
    experience_years: number;
    availability_status: string;
  };
}

interface ProjectFormProps {
  initialData?: Partial<ProjectFormData>;
  onSubmit: (data: ProjectFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ProjectForm: React.FC<ProjectFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const navigate = useNavigate();
  const isEditMode = !!initialData?.id;

  // Form state
  const [formData, setFormData] = useState<ProjectFormData>({
    title: initialData?.title || '',
    categories: initialData?.categories || [],
    budget: initialData?.budget || '',
    deadline: initialData?.deadline || '',
    company: initialData?.company || '',
    website: initialData?.website || '',
    description: initialData?.description || '',
    recruitmentType: initialData?.recruitmentType || 'company',
    openToApplications: initialData?.openToApplications ?? true,
    autoInvite: initialData?.autoInvite ?? false,
    status: initialData?.status || 'draft',
    files: initialData?.files || [],
    filters: initialData?.filters || {
      languages: [],
      skills: [],
      location: '',
      experience_years: 0,
      availability_status: 'available',
    },
  });

  // Validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showFilters, setShowFilters] = useState(false);

  // Available options
  const [availableCategories, setAvailableCategories] = useState<string[]>([
    'Modélisation 3D',
    'Animation 3D',
    'Rendu 3D',
    'Texturing et Shading',
    'Effets visuels (VFX)',
    'Conception de personnages 3D',
    'Environnements 3D',
    'Réalité virtuelle (VR)',
    'Réalité augmentée (AR)',
    'Simulations physiques',
    'Rigging 3D',
    'Lighting 3D',
    'Compositing 3D',
    'Design de produits 3D',
    'Architecture 3D',
    'Jeux vidéo 3D',
    'Cinéma 4D',
    'Blender',
    'ZBrush',
    'Substance Painter',
  ]);

  const [availableLanguages, setAvailableLanguages] = useState<string[]>([
    'Français',
    'Anglais',
    'Espagnol',
    'Allemand',
    'Italien',
    'Portugais',
    'Russe',
    'Chinois',
    'Japonais',
    'Arabe',
  ]);

  const [availableSkills, setAvailableSkills] = useState<string[]>([
    'Blender',
    'Maya',
    '3ds Max',
    'ZBrush',
    'Substance Painter',
    'Cinema 4D',
    'Houdini',
    'Unity',
    'Unreal Engine',
    'After Effects',
    'Photoshop',
    'Illustrator',
  ]);

  const deadlineOptions = [
    { value: '1_week', label: '1 semaine' },
    { value: '2_weeks', label: '2 semaines' },
    { value: '1_month', label: '1 mois' },
    { value: '3_months', label: '3 mois' },
    { value: '6_months', label: '6 mois' },
    { value: 'custom', label: 'Date personnalisée' },
  ];

  const experienceOptions = [
    { value: '0', label: 'Tous niveaux' },
    { value: '1', label: 'Au moins 1 an' },
    { value: '2', label: 'Au moins 2 ans' },
    { value: '3', label: 'Au moins 3 ans' },
    { value: '5', label: 'Au moins 5 ans' },
    { value: '10', label: 'Plus de 10 ans' },
  ];

  const availabilityOptions = [
    { value: 'any', label: 'Toutes disponibilités' },
    { value: 'available', label: 'Disponible maintenant' },
    { value: 'busy', label: 'Partiellement disponible' },
  ];

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Handle filter changes
  const handleFilterChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [name]: value,
      },
    }));
  };

  // Handle category selection
  const handleCategoryToggle = (category: string) => {
    setFormData(prev => {
      const categories = prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category];
      
      return {
        ...prev,
        categories,
      };
    });
    
    // Clear error when field is edited
    if (errors.categories) {
      setErrors(prev => ({ ...prev, categories: '' }));
    }
  };

  // Handle language selection
  const handleLanguageToggle = (language: string) => {
    setFormData(prev => {
      const languages = prev.filters.languages.includes(language)
        ? prev.filters.languages.filter(l => l !== language)
        : [...prev.filters.languages, language];
      
      return {
        ...prev,
        filters: {
          ...prev.filters,
          languages,
        },
      };
    });
  };

  // Handle skill selection
  const handleSkillToggle = (skill: string) => {
    setFormData(prev => {
      const skills = prev.filters.skills.includes(skill)
        ? prev.filters.skills.filter(s => s !== skill)
        : [...prev.filters.skills, skill];
      
      return {
        ...prev,
        filters: {
          ...prev.filters,
          skills,
        },
      };
    });
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      
      setFormData(prev => ({
        ...prev,
        files: [...prev.files, ...newFiles],
      }));
    }
  };

  // Remove file
  const handleRemoveFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index),
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Le titre du projet est requis';
    }
    
    if (formData.categories.length === 0) {
      newErrors.categories = 'Sélectionnez au moins une catégorie';
    }
    
    if (!formData.budget.trim()) {
      newErrors.budget = 'Le budget est requis';
    }
    
    if (!formData.deadline.trim()) {
      newErrors.deadline = 'La date limite est requise';
    }
    
    if (formData.recruitmentType === 'company' && !formData.company.trim()) {
      newErrors.company = 'Le nom de l\'entreprise est requis';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'La description du projet est requise';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Get deadline date from option
  const getDeadlineDate = (option: string): string => {
    const today = new Date();
    
    switch (option) {
      case '1_week':
        today.setDate(today.getDate() + 7);
        break;
      case '2_weeks':
        today.setDate(today.getDate() + 14);
        break;
      case '1_month':
        today.setMonth(today.getMonth() + 1);
        break;
      case '3_months':
        today.setMonth(today.getMonth() + 3);
        break;
      case '6_months':
        today.setMonth(today.getMonth() + 6);
        break;
      case 'custom':
        // Return the custom date as is
        return formData.deadline;
      default:
        // Default to 1 month
        today.setMonth(today.getMonth() + 1);
    }
    
    return today.toISOString().split('T')[0];
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-neutral-200">
        <h2 className="text-xl font-semibold text-neutral-900">
          {isEditMode ? 'Modifier le projet' : 'Créer un nouveau projet'}
        </h2>
        <p className="text-neutral-600 text-sm mt-1">
          {isEditMode 
            ? 'Mettez à jour les détails de votre projet' 
            : 'Remplissez les détails de votre projet pour trouver les meilleurs professionnels '}
        </p>
      </div>
      
      <form onSubmit={handleSubmit} className="p-6">
        <div className="space-y-6">
          {/* Project Title */}
          <FormInput
            label="Titre du projet"
            id="title"
            name="title"
            placeholder="Ex: Création d'un personnage 3D pour jeu vidéo"
            value={formData.title}
            onChange={handleInputChange}
            error={errors.title}
            icon={<Briefcase className="h-5 w-5 text-neutral-400" />}
            required
          />
          
          {/* Categories */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">
              Catégories <span className="text-red-500">*</span>
            </label>
            <div className="flex flex-wrap gap-2 mb-2">
              {availableCategories.map(category => (
                <button
                  key={category}
                  type="button"
                  onClick={() => handleCategoryToggle(category)}
                  className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                    formData.categories.includes(category)
                      ? 'bg-primary-100 text-primary-800 hover:bg-primary-200'
                      : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
            {errors.categories && (
              <p className="mt-1 text-sm text-red-600">{errors.categories}</p>
            )}
          </div>
          
          {/* Budget */}
          <FormInput
            label="Budget"
            id="budget"
            name="budget"
            placeholder="Ex: 1000 €"
            value={formData.budget}
            onChange={handleInputChange}
            error={errors.budget}
            icon={<DollarSign className="h-5 w-5 text-neutral-400" />}
            helperText="Indiquez votre budget pour ce projet"
            required
          />
          
          {/* Deadline */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">
              Délai <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormSelect
                id="deadline-option"
                name="deadline-option"
                options={deadlineOptions}
                onChange={(e) => {
                  const selectedOption = e.target.value;
                  if (selectedOption === 'custom') {
                    // Do nothing, let the user select a custom date
                  } else {
                    setFormData(prev => ({
                      ...prev,
                      deadline: getDeadlineDate(selectedOption),
                    }));
                  }
                }}
                icon={<Clock className="h-5 w-5 text-neutral-400" />}
              />
              
              <FormInput
                id="deadline"
                name="deadline"
                type="date"
                value={formData.deadline}
                onChange={handleInputChange}
                error={errors.deadline}
                icon={<Calendar className="h-5 w-5 text-neutral-400" />}
                required
              />
            </div>
          </div>
          
          {/* Recruitment Type */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">
              Type de recrutement
            </label>
            <div className="flex space-x-4">
              <div className="flex items-center">
                <input
                  type="radio"
                  id="company"
                  name="recruitmentType"
                  value="company"
                  checked={formData.recruitmentType === 'company'}
                  onChange={() => setFormData(prev => ({ ...prev, recruitmentType: 'company' }))}
                  className="h-4 w-4 text-primary-600 border-neutral-300 focus:ring-primary-500"
                />
                <label htmlFor="company" className="ml-2 text-sm text-neutral-700">
                  Entreprise
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="personal"
                  name="recruitmentType"
                  value="personal"
                  checked={formData.recruitmentType === 'personal'}
                  onChange={() => setFormData(prev => ({ ...prev, recruitmentType: 'personal' }))}
                  className="h-4 w-4 text-primary-600 border-neutral-300 focus:ring-primary-500"
                />
                <label htmlFor="personal" className="ml-2 text-sm text-neutral-700">
                  Personnel
                </label>
              </div>
            </div>
          </div>
          
          {/* Company and Website (if company recruitment) */}
          {formData.recruitmentType === 'company' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                label="Nom de l'entreprise"
                id="company"
                name="company"
                placeholder="Ex: Studio GameArt"
                value={formData.company}
                onChange={handleInputChange}
                error={errors.company}
                icon={<Briefcase className="h-5 w-5 text-neutral-400" />}
                required
              />
              
              <FormInput
                label="Site web"
                id="website"
                name="website"
                placeholder="Ex: https://www.example.com"
                value={formData.website}
                onChange={handleInputChange}
                error={errors.website}
                icon={<Globe className="h-5 w-5 text-neutral-400" />}
              />
            </div>
          )}
          
          {/* Project Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-neutral-700 mb-1">
              Description du projet <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              rows={6}
              placeholder="Décrivez votre projet en détail..."
              value={formData.description}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border ${
                errors.description ? 'border-red-500' : 'border-neutral-300'
              } rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500`}
              required
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description}</p>
            )}
          </div>
          
          {/* File Upload */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">
              Fichiers joints
            </label>
            <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-neutral-300 border-dashed rounded-md">
              <div className="space-y-1 text-center">
                <Upload className="mx-auto h-12 w-12 text-neutral-400" />
                <div className="flex text-sm text-neutral-600">
                  <label
                    htmlFor="file-upload"
                    className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
                  >
                    <span>Télécharger des fichiers</span>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      className="sr-only"
                      multiple
                      onChange={handleFileUpload}
                    />
                  </label>
                  <p className="pl-1">ou glisser-déposer</p>
                </div>
                <p className="text-xs text-neutral-500">
                  PNG, JPG, PDF jusqu'à 10MB
                </p>
              </div>
            </div>
            
            {/* File List */}
            {formData.files.length > 0 && (
              <ul className="mt-3 divide-y divide-neutral-200 border border-neutral-200 rounded-md overflow-hidden">
                {formData.files.map((file, index) => (
                  <li key={index} className="px-4 py-3 flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 text-neutral-400 mr-2" />
                      <span className="truncate">{file.name}</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveFile(index)}
                      className="ml-2 text-red-600 hover:text-red-800"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </div>
          
          {/* Project Options */}
          <div className="space-y-3">
            <Checkbox
              id="openToApplications"
              name="openToApplications"
              label="Ouvert aux candidatures (les professionnels peuvent postuler à votre projet)"
              checked={formData.openToApplications}
              onChange={handleCheckboxChange}
            />
            
            <Checkbox
              id="autoInvite"
              name="autoInvite"
              label="Inviter automatiquement les professionnels correspondant à vos critères"
              checked={formData.autoInvite}
              onChange={handleCheckboxChange}
            />
          </div>
          
          {/* Professional Filters */}
          <div className="border border-neutral-200 rounded-lg overflow-hidden">
            <div 
              className="px-4 py-3 bg-neutral-50 border-b border-neutral-200 flex justify-between items-center cursor-pointer"
              onClick={() => setShowFilters(!showFilters)}
            >
              <div className="flex items-center">
                <Users className="h-5 w-5 text-neutral-500 mr-2" />
                <h3 className="font-medium text-neutral-700">Filtres de recherche de professionnels</h3>
              </div>
              <div className="text-neutral-500">
                {showFilters ? '−' : '+'}
              </div>
            </div>
            
            {showFilters && (
              <div className="p-4 space-y-4">
                {/* Languages */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Langues
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {availableLanguages.map(language => (
                      <button
                        key={language}
                        type="button"
                        onClick={() => handleLanguageToggle(language)}
                        className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                          formData.filters.languages.includes(language)
                            ? 'bg-primary-100 text-primary-800 hover:bg-primary-200'
                            : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                        }`}
                      >
                        {language}
                      </button>
                    ))}
                  </div>
                </div>
                
                {/* Skills */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Compétences
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {availableSkills.map(skill => (
                      <button
                        key={skill}
                        type="button"
                        onClick={() => handleSkillToggle(skill)}
                        className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                          formData.filters.skills.includes(skill)
                            ? 'bg-primary-100 text-primary-800 hover:bg-primary-200'
                            : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                        }`}
                      >
                        {skill}
                      </button>
                    ))}
                  </div>
                </div>
                
                {/* Location */}
                <FormInput
                  label="Localisation"
                  id="location"
                  name="location"
                  placeholder="Ex: Paris, France"
                  value={formData.filters.location}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  icon={<MapMarkerAlt className="h-5 w-5 text-neutral-400" />}
                  helperText="Laissez vide pour toutes les localisations"
                />
                
                {/* Experience */}
                <FormSelect
                  label="Expérience minimale"
                  id="experience_years"
                  name="experience_years"
                  options={experienceOptions}
                  value={formData.filters.experience_years.toString()}
                  onChange={(e) => handleFilterChange('experience_years', parseInt(e.target.value))}
                  icon={<Briefcase className="h-5 w-5 text-neutral-400" />}
                />
                
                {/* Availability */}
                <FormSelect
                  label="Disponibilité"
                  id="availability_status"
                  name="availability_status"
                  options={availabilityOptions}
                  value={formData.filters.availability_status}
                  onChange={(e) => handleFilterChange('availability_status', e.target.value)}
                  icon={<Clock className="h-5 w-5 text-neutral-400" />}
                />
              </div>
            )}
          </div>
          
          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Annuler
            </Button>
            
            <Button
              type="submit"
              variant="primary"
              isLoading={isLoading}
              leftIcon={isEditMode ? <Check className="h-5 w-5" /> : undefined}
            >
              {isEditMode ? 'Mettre à jour le projet' : 'Créer le projet'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

// Add the missing MapMarkerAlt icon
const MapMarkerAlt = (props: any) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M12 22s-8-4.5-8-11.8A8 8 0 0 1 12 2a8 8 0 0 1 8 8.2c0 7.3-8 11.8-8 11.8z" />
    <circle cx="12" cy="10" r="3" />
  </svg>
);

export default ProjectForm;
