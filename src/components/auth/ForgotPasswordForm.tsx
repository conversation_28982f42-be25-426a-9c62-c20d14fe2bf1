import React, { useState } from "react";
import { useN<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Mail, AlertCircle, CheckCircle, ArrowLeft } from "lucide-react";
import { API_BASE_URL } from "../../config";
import { useToast } from "../../context/ToastContext";
import Button from "../ui/Button";
import FormInput from "../ui/FormInput";
import { ApiError } from "../../services/api";

interface ForgotPasswordFormProps {
  onClose?: () => void;
  onBackToLogin?: () => void;
}

const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  onClose,
  onBackToLogin,
}) => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [formErrors, setFormErrors] = useState({
    email: "",
  });

  const validateForm = () => {
    let isValid = true;
    const errors = {
      email: "",
    };

    if (!email) {
      errors.email = "L'adresse email est obligatoire";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = "L'adresse email est invalide";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      if (formErrors.email) {
        showToast("error", formErrors.email);
      }
      return;
    }

    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch(`${API_BASE_URL}/api/password/forgot`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (response.ok) {
        const successMessage =
          result.message ||
          "Un email de réinitialisation a été envoyé à votre adresse email.";
        setSuccess(successMessage);
        showToast("success", successMessage);
        setEmail("");
      } else {
        let errorMessage: string;

        const apiError = result as ApiError;

        if (response.status === 422) {
          if (
            apiError.errors &&
            apiError.errors.email &&
            Array.isArray(apiError.errors.email) &&
            apiError.errors.email.length > 0
          ) {
            errorMessage = apiError.errors.email[0];
          } else if (apiError.message) {
            errorMessage = apiError.message;
          } else {
            errorMessage = "Veuillez vérifier l'adresse email saisie.";
          }
        } else if (response.status === 404) {
          errorMessage =
            apiError.message ||
            "Aucun utilisateur trouvé avec cette adresse e-mail.";
        } else {
          errorMessage =
            apiError.message ||
            "Une erreur est survenue lors de l'envoi de l'email de réinitialisation.";
        }

        setError(errorMessage);
        showToast("error", errorMessage);
      }
    } catch (err) {
      const networkErrorMessage =
        "Erreur de connexion à l'API. Veuillez vérifier votre connexion internet ou que le serveur backend est en cours d'exécution.";
      setError(networkErrorMessage);
      showToast("error", networkErrorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div
        className="bg-white rounded-lg overflow-hidden relative"
        style={{ width: "920px", height: "480px", borderRadius: "30px" }}
      >
        {/* Bouton de fermeture */}
        <button
          onClick={onClose}
          className="absolute top-6 right-6 z-10 flex items-center justify-center 
             transition-all duration-200"
          style={{
            backgroundColor: "rgba(0, 0, 0, 0.478)",
            color: "#FFFFFF",
            borderRadius: "10px",
            padding: "12px",
            boxShadow: "-10px 10px 30px rgba(0, 0, 0, 0.15)",
            border: "none",
            cursor: "pointer",
            width: "40px",
            height: "40px",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.478)";
          }}
          aria-label="Close modal"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="28"
            height="28"
            viewBox="0 0 24 24"
            fill="#FFFFFF"
          >
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
              stroke="currentColor"
              strokeWidth="0.5"
            />
          </svg>
        </button>

        <div className="flex h-full">
          {/* Partie formulaire */}
          <div className="w-1/2 p-8 flex flex-col">
            <h2
              className="text-3xl mb-4 text-center font-sans font-medium tracking-tight"
              style={{ letterSpacing: "0.05em" }}
            >
              FORGOT PASSWORD
            </h2>

            <p
              className="text-base mb-6 text-center font-sans font-normal"
              style={{ letterSpacing: "0.02em", color: "#000000" }}
            >
              Enter your email to receive a reset link
            </p>

            {/* Success message */}
            {success && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg flex items-start text-sm">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-green-700">{success}</p>
              </div>
            )}

            {/* Error message */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start text-sm">
                <AlertCircle className="h-4 w-4 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-red-700">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <FormInput
                type="email"
                id="email"
                className="bg-[#F5F5F5] focus:border-[#BDBDBD] focus:ring-0"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                icon={<Mail className="h-4 w-4 text-gray-400" />}
                error={formErrors.email}
                required
              />

              <Button
                type="submit"
                variant="primary"
                className="w-full mt-4 flex items-center justify-center gap-1"
                disabled={isLoading}
                aria-busy={isLoading}
              >
                {isLoading ? (
                  "Loading..."
                ) : (
                  <>
                    Send reset link
                    <span className="ml-1">&gt;</span>
                  </>
                )}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <button
                onClick={onBackToLogin}
                className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to login
              </button>
            </div>
          </div>

          {/* Partie image */}
          <div className="w-1/2 bg-gray-100">
            <img
              src="https://hi-3d.com/wp-content/uploads/2025/08/basel-lindenhofareal-miller-maranta-visualisierung-1.jpg"
              alt="Forgot password visual"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordForm;
