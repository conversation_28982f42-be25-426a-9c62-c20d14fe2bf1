import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, Link } from 'react-router-dom';
import { Lock, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';

const ResetPasswordForm: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [token, setToken] = useState('');
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Get token and email from URL
    const tokenParam = searchParams.get('token');
    const emailParam = searchParams.get('email');

    if (tokenParam) {
      setToken(tokenParam);
    } else {
      setError('Token de réinitialisation manquant');
    }

    if (emailParam) {
      setEmail(emailParam);
    } else {
      setError('Email manquant');
    }
  }, [searchParams]);

  // Form validation
  const [formErrors, setFormErrors] = useState({
    password: '',
    confirmPassword: '',
  });

  const validateForm = () => {
    let isValid = true;
    const errors = {
      password: '',
      confirmPassword: '',
    };

    // Password validation
    if (!password) {
      errors.password = 'Le mot de passe est obligatoire';
      isValid = false;
    } else if (password.length < 8) {
      errors.password = 'Le mot de passe doit contenir au moins 8 caractères';
      isValid = false;
    }

    // Confirm password validation
    if (password !== confirmPassword) {
      errors.confirmPassword = 'Les mots de passe ne correspondent pas';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch(`${API_BASE_URL}/api/password/reset`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          password_confirmation: confirmPassword,
          token,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setSuccess(result.message || 'Votre mot de passe a été réinitialisé avec succès.');
        
        // Clear form
        setPassword('');
        setConfirmPassword('');
        
        // Redirect to login page after a delay
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      } else {
        setError(result.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe');
      }
    } catch (err) {
      setError('Erreur de connexion à l\'API');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-neutral-900">Réinitialiser le mot de passe</h1>
        <p className="text-neutral-600 mt-2">
          Créez un nouveau mot de passe pour votre compte
        </p>
      </div>

      {/* Success message */}
      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-green-700">{success}</p>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Reset password form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <FormInput
          label="Nouveau mot de passe"
          type="password"
          id="password"
          placeholder="••••••••"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          icon={<Lock className="h-5 w-5 text-neutral-400" />}
          error={formErrors.password}
          showPasswordToggle
          helperText="8 caractères minimum"
          required
        />

        <FormInput
          label="Confirmer le mot de passe"
          type="password"
          id="confirmPassword"
          placeholder="••••••••"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          icon={<Lock className="h-5 w-5 text-neutral-400" />}
          error={formErrors.confirmPassword}
          showPasswordToggle
          required
        />

        <Button
          type="submit"
          variant="primary"
          fullWidth
          isLoading={isLoading}
          disabled={!token || !email}
        >
          Réinitialiser le mot de passe
        </Button>
      </form>

      <div className="mt-8 text-center">
        <Link
          to="/login"
          className="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Retour à la connexion
        </Link>
      </div>
    </div>
  );
};

export default ResetPasswordForm;
