import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams, Link } from "react-router-dom";
import { Mail, Lock } from "lucide-react";
import { ApiError, testApiConnection } from "../../services/api";
import authService from "../../services/authService";
import { API_BASE_URL } from "../../config";
import { useProfileWizard } from "../../context/ProfileWizardContext";
import { useToast } from "../../context/ToastContext";
import Button from "../ui/Button";
import FormInput from "../ui/FormInput";
import Checkbox from "../ui/Checkbox";

interface LoginFormProps {
  onToggleForm?: () => void;
  onSuccess?: () => void;
  onClose?: () => void;
  onOpenRegister?: () => void;
  onOpenForgotPassword?: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onToggleForm,
  onSuccess,
  onClose,
  onOpenRegister,
  onOpenForgotPassword,
}): React.ReactElement => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { openProfileWizard } = useProfileWizard();
  const { showToast } = useToast();
  const [formErrors, setFormErrors] = useState({
    email: "",
    password: "",
  });

  useEffect(() => {
    if (searchParams.get("verified") === "true") {
      setSuccessMessage("Votre adresse e-mail a été vérifiée avec succès !");
    }
  }, [searchParams]);

  const validateForm = () => {
    let isValid = true;
    const errors = {
      email: "",
      password: "",
    };

    if (!email) {
      errors.email = "L'adresse email est obligatoire";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = "L'adresse email est invalide";
      isValid = false;
    }

    if (!password) {
      errors.password = "Le mot de passe est obligatoire";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsLoading(true);
    setError("");

    const isServerConnected = await testApiConnection();
    if (!isServerConnected) {
      const errorMessage =
        "Impossible de se connecter au serveur backend. Vérifiez que le serveur est en cours d'exécution à l'adresse " +
        API_BASE_URL;
      setError(errorMessage);
      showToast("error", errorMessage);
      setIsLoading(false);
      return;
    }

    try {
      const result = await authService.login({ email, password });

      if (result && typeof result === "object") {
        const typedResult = result as {
          user: any;
          token: string;
          message?: string;
        };

        if (typedResult.user && typedResult.token) {
          if (rememberMe) {
            localStorage.setItem("rememberedEmail", email);
          }

          showToast("success", "Connexion réussie !");
          navigate("/dashboard");
          openProfileWizard();

          if (onSuccess) {
            onSuccess();
          }
        } else if (typedResult.message) {
          setError(typedResult.message);
          showToast("error", typedResult.message);
        }
      }
    } catch (err) {
      let errorMessage = "Une erreur inconnue est survenue.";
      if ((err as ApiError).message) {
        const apiError = err as ApiError;
        errorMessage = apiError.message;

        if (apiError.status === 401) {
          errorMessage = "Email ou mot de passe incorrect";
        } else if (apiError.status === 403) {
          errorMessage =
            "Votre e-mail n'est pas vérifié. Veuillez vérifier votre boîte de réception.";
        }
      }
      setError(errorMessage);
      showToast("error", errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div
        className="bg-white rounded-lg overflow-hidden relative"
        style={{ width: "920px", height: "589px", borderRadius: "30px" }}
      >
        {/* Bouton de fermeture stylisé avec effets de survol */}
        <button
          onClick={onClose}
          className="absolute top-6 right-6 z-10 flex items-center justify-center 
             transition-all duration-200"
          style={{
            backgroundColor: "rgba(0, 0, 0, 0.478)",
            color: "#FFFFFF",
            borderRadius: "10px",
            padding: "12px",
            boxShadow: "-10px 10px 30px rgba(0, 0, 0, 0.15)",
            border: "none",
            cursor: "pointer",
            width: "40px",
            height: "40px",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.478)";
          }}
          aria-label="Close modal"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="28"
            height="28"
            viewBox="0 0 24 24"
            fill="#FFFFFF"
          >
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
              stroke="currentColor"
              strokeWidth="0.5"
            />
          </svg>
        </button>

        <div className="flex h-full">
          {/* Partie formulaire */}
          <div className="w-1/2 p-8 flex flex-col">
            <h2
              className="text-3xl mb-4 text-center font-sans font-medium tracking-tight"
              style={{ letterSpacing: "0.05em" }}
            >
              LOG IN
            </h2>

            <p
              className="text-base mb-6 text-center font-sans font-normal"
              style={{ letterSpacing: "0.02em", color: "#000000" }}
            >
              Don't have an account?{" "}
              <button
                onClick={onOpenRegister || onToggleForm}
                className="text-blue-600 font-normal"
                style={{ color: "#000000" }}
              >
                Sign Up
              </button>
            </p>

            <Button
              type="button"
              variant="outline"
              className="flex items-center justify-center gap-2 mb-6 bg-[#F5F5F5] hover:bg-[#E0E0E0]"
              style={{ borderRadius: "14px" }}
            >
              <img
                src="https://www.google.com/favicon.ico"
                alt="Google"
                className="w-4 h-4"
              />
              Continue with Google
            </Button>

            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-xs">
                <span className="px-2 bg-white text-gray-500">or</span>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4 ">
              <FormInput
                // label="Email"
                type="email"
                id="email"
                className="bg-[#F5F5F5]  focus:border-[#BDBDBD] focus:ring-0"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                icon={<Mail className="h-4 w-4 text-gray-400" />}
                error={formErrors.email}
                required
              />

              <FormInput
                // label="Password"
                type="password"
                id="password"
                className="bg-[#F5F5F5]  focus:border-[#BDBDBD] focus:ring-0"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                icon={<Lock className="h-4 w-4 text-gray-400" />}
                error={formErrors.password}
                showPasswordToggle
                required
              />

              <div className="flex items-center justify-between text-sm">
                <Checkbox
                  id="remember-me"
                  label="Remember me"
                  checked={rememberMe}
                  onChange={() => setRememberMe(!rememberMe)}
                />

                <button
                  onClick={() => {
                    onClose?.();
                    onOpenForgotPassword?.();
                  }}
                  className="text-blue-600 hover:underline"
                >
                  Forgot password?
                </button>
              </div>

              <Button
                type="submit"
                variant="primary"
                className="w-full mt-4 flex items-center justify-center gap-1"
                disabled={isLoading}
                aria-busy={isLoading}
              >
                {isLoading ? (
                  "Loading..."
                ) : (
                  <>
                    Continue with email
                    <span className="ml-1">&gt;</span>
                  </>
                )}
              </Button>
            </form>

            <p
              className="text-sm text-gray-500 mt-3 mb-8 text-center font-light"
              style={{ fontSize: "13px" }}
            >
              By signing up, you agree to our <br />
              <a
                href="/terms"
                className="text-gray-600 hover:underline font-normal"
              >
                Terms of Service
              </a>{" "}
              &{" "}
              <a
                href="/privacy"
                className="text-gray-600 hover:underline font-normal"
              >
                Privacy Policy
              </a>
            </p>
          </div>

          {/* Partie image */}
          <div className="w-1/2 bg-gray-100">
            <img
              src="https://hi-3d.com/wp-content/uploads/2025/08/basel-lindenhofareal-miller-maranta-visualisierung-1.jpg"
              alt="Login visual"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
