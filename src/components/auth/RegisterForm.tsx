import React, { useState } from "react";
import { useNavigate, <PERSON> } from "react-router-dom";
import { Mail, Lock, User, AlertCircle, CheckCircle } from "lucide-react";
import { authService, ApiError } from "../../services/api";
import { useToast } from "../../context/ToastContext";
import Button from "../ui/Button";
import FormInput from "../ui/FormInput";
import Checkbox from "../ui/Checkbox";
import FormDivider from "../ui/FormDivider";
import FormSelect from "../ui/FormSelect";

interface RegisterFormProps {
  onToggleForm?: () => void;
  onClose?: () => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({
  onToggleForm,
  onClose,
}) => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [accountType, setAccountType] = useState("client");
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const [formErrors, setFormErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreeTerms: "",
  });

  const validateForm = () => {
    let isValid = true;
    const errors = {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      agreeTerms: "",
    };

    if (!firstName.trim()) {
      errors.firstName = "Le prénom est obligatoire";
      isValid = false;
    }

    if (!lastName.trim()) {
      errors.lastName = "Le nom est obligatoire";
      isValid = false;
    }

    if (!email) {
      errors.email = "L'adresse email est obligatoire";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = "L'adresse email est invalide";
      isValid = false;
    }

    if (!password) {
      errors.password = "Le mot de passe est obligatoire";
      isValid = false;
    } else if (password.length < 8) {
      errors.password = "Le mot de passe doit contenir au moins 8 caractères";
      isValid = false;
    }

    if (password !== confirmPassword) {
      errors.confirmPassword = "Les mots de passe ne correspondent pas";
      isValid = false;
    }

    if (!agreeTerms) {
      errors.agreeTerms = "Vous devez accepter les conditions d'utilisation";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      const hasErrors = Object.values(formErrors).some((error) => error !== "");
      if (hasErrors) {
        showToast("error", "Veuillez corriger les erreurs dans le formulaire");
      }
      return;
    }

    setIsLoading(true);
    setError("");
    setSuccess("");

    const payload = {
      first_name: firstName,
      last_name: lastName,
      email,
      password,
      password_confirmation: confirmPassword,
      is_professional: accountType === "professional",
    };

    try {
      await authService.ping();

      const result = await authService.register(payload);

      const successMessage =
        result && typeof result === "object" && "message" in result
          ? (result.message as string)
          : "Inscription réussie ! Veuillez vérifier votre email pour activer votre compte.";

      setSuccess(successMessage);
      showToast("success", successMessage);

      setFirstName("");
      setLastName("");
      setEmail("");
      setPassword("");
      setConfirmPassword("");
      setAgreeTerms(false);

      setTimeout(() => {
        if (onToggleForm) {
          onToggleForm();
        } else {
          navigate("/login");
        }
      }, 3000);
    } catch (err) {
      let errorMessage = "Une erreur inconnue est survenue";

      if ((err as ApiError).message) {
        const apiError = err as ApiError;
        errorMessage =
          apiError.message || "Une erreur est survenue côté serveur.";

        if (apiError.status === 422) {
          if (
            apiError.message &&
            apiError.message.includes("Cette adresse email est déjà utilisée")
          ) {
            errorMessage = "Cette adresse email est déjà utilisée.";
          } else {
            errorMessage = "Veuillez vérifier les informations saisies.";
          }
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      showToast("error", errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div
        className="bg-white rounded-lg overflow-hidden relative"
        style={{ width: "920px", height: "580px", borderRadius: "30px" }}
      >
        {/* Bouton de fermeture */}
        <button
          onClick={onClose}
          className="absolute top-6 right-6 z-10 flex items-center justify-center 
             transition-all duration-200"
          style={{
            backgroundColor: "rgba(0, 0, 0, 0.478)",
            color: "#FFFFFF",
            borderRadius: "10px",
            padding: "12px",
            boxShadow: "-10px 10px 30px rgba(0, 0, 0, 0.15)",
            border: "none",
            cursor: "pointer",
            width: "40px",
            height: "40px",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.478)";
          }}
          aria-// label="Close modal"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="28"
            height="28"
            viewBox="0 0 24 24"
            fill="#FFFFFF"
          >
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
              stroke="currentColor"
              strokeWidth="0.5"
            />
          </svg>
        </button>

        <div className="flex h-full">
          {/* Partie formulaire */}
          <div className="w-1/2 p-8 flex flex-col">
            <h2
              className="text-3xl mb-4 text-center font-sans font-medium tracking-tight"
              style={{ letterSpacing: "0.05em" }}
            >
              SIGN UP
            </h2>

            <p
              className="text-base mb-6 text-center font-sans font-normal"
              style={{ letterSpacing: "0.02em", color: "#000000" }}
            >
              Already have an account?{" "}
              <button
                onClick={onToggleForm}
                className="text-blue-600 font-normal"
                style={{ color: "#000000" }}
              >
                Log In
              </button>
            </p>

            {/* Success message */}
            {success && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg flex items-start text-sm">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-green-700">{success}</p>
              </div>
            )}

            {/* Error message */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start text-sm">
                <AlertCircle className="h-4 w-4 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-red-700">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <FormInput
                  // // label="First Name"
                  type="text"
                  id="firstName"
                  className="bg-[#F5F5F5] focus:border-[#BDBDBD] focus:ring-0"
                  placeholder="First Name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  icon={<User className="h-4 w-4 text-gray-400" />}
                  error={formErrors.firstName}
                  required
                />

                <FormInput
                  // // label="Last Name"
                  type="text"
                  id="lastName"
                  className="bg-[#F5F5F5] focus:border-[#BDBDBD] focus:ring-0"
                  placeholder="Last Name"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  icon={<User className="h-4 w-4 text-gray-400" />}
                  error={formErrors.lastName}
                  required
                />
              </div>

              <FormInput
                // label="Email"
                type="email"
                id="email"
                className="bg-[#F5F5F5] focus:border-[#BDBDBD] focus:ring-0"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                icon={<Mail className="h-4 w-4 text-gray-400" />}
                error={formErrors.email}
                required
              />

              <FormSelect
                // label="Account Type"
                id="accountType"
                className="bg-[#F5F5F5] focus:border-[#BDBDBD] focus:ring-0"
                value={accountType}
                onChange={(e) => setAccountType(e.target.value)}
                options={[
                  {
                    value: "client",
                    label: "Client - Looking for professionals",
                  },
                  {
                    value: "professional",
                    label: "Professional - Offering services",
                  },
                ]}
              />

              <div className="grid grid-cols-2 gap-3">
                <FormInput
                  // label="Password"
                  type="password"
                  id="password"
                  className="bg-[#F5F5F5] focus:border-[#BDBDBD] focus:ring-0"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  icon={<Lock className="h-4 w-4 text-gray-400" />}
                  error={formErrors.password}
                  showPasswordToggle
                  helperText="8 characters minimum"
                  required
                />

                <FormInput
                  // label="Confirm Password"
                  type="password"
                  id="confirmPassword"
                  className="bg-[#F5F5F5] focus:border-[#BDBDBD] focus:ring-0"
                  placeholder="••••••••"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  icon={<Lock className="h-4 w-4 text-gray-400" />}
                  error={formErrors.confirmPassword}
                  showPasswordToggle
                  required
                />
              </div>

              <Checkbox
                id="agree-terms"
                label={
                  <span>
                    I agree to the{" "}
                    <Link to="/terms" className="text-blue-600 hover:underline">
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link
                      to="/privacy"
                      className="text-blue-600 hover:underline"
                    >
                      Privacy Policy
                    </Link>
                  </span>
                }
                checked={agreeTerms}
                onChange={() => setAgreeTerms(!agreeTerms)}
                error={formErrors.agreeTerms}
              />

              <Button
                type="submit"
                variant="primary"
                className="w-full mt-4 flex items-center justify-center gap-1"
                disabled={isLoading}
                aria-busy={isLoading}
              >
                {isLoading ? (
                  "Loading..."
                ) : (
                  <>
                    Create Account
                    <span className="ml-1">&gt;</span>
                  </>
                )}
              </Button>
            </form>

            <p
              className="text-sm text-gray-500 mt-3 mb-8 text-center font-light"
              style={{ fontSize: "13px" }}
            >
              By signing up, you agree to our <br />
              <a
                href="/terms"
                className="text-gray-600 hover:underline font-normal"
              >
                Terms of Service
              </a>{" "}
              &{" "}
              <a
                href="/privacy"
                className="text-gray-600 hover:underline font-normal"
              >
                Privacy Policy
              </a>
            </p>
          </div>

          {/* Partie image */}
          <div className="w-1/2 bg-gray-100">
            <img
              src="https://hi-3d.com/wp-content/uploads/2025/08/basel-lindenhofareal-miller-maranta-visualisierung-1.jpg"
              alt="Register visual"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterForm;
