import React, { useState } from 'react';
import { Search, Filter, ChevronDown } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';
import Container from '../layout/Container';

interface HeroSectionProps {
  onSearch?: (query: string) => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onSearch }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchQuery);
    } else {
      navigate(`/lists-independants?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <div className="bg-gradient-to-r from-primary-50 to-primary-100 py-16 md:py-24">
      <Container>
        <div className="text-center max-w-3xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-4">
            Découvrez l'univers de la création 3D
          </h1>
          <p className="text-neutral-600 text-lg mb-12 max-w-2xl mx-auto">
            Rejoignez une communauté d'artistes talentueux et explorez des projets innovants en modélisation 3D,
            animation et design.
          </p>

          {/* Search Form */}
          <form onSubmit={handleSearch} className="relative max-w-2xl mx-auto">
            <div className="flex gap-3 flex-col md:flex-row">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-neutral-400" />
                </div>
                <input
                  type="text"
                  placeholder="Rechercher un professionnel, une compétence..."
                  className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-full focus:ring-2 focus:ring-primary-500 focus:border-primary-500 shadow-sm"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => setShowFilters(!showFilters)}
                  className="px-4 py-3 bg-white border border-neutral-300 rounded-full flex items-center gap-2 hover:bg-neutral-50 shadow-sm text-neutral-800 font-semibold"
                >
                  <Filter className="h-5 w-5 text-neutral-500" />
                  <span className="hidden md:inline">Filtres</span>
                </button>

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  className="px-6"
                >
                  Rechercher
                </Button>
              </div>
            </div>

            {/* Filters Panel (hidden by default) */}
            {showFilters && (
              <div className="mt-4 p-4 bg-white rounded-lg shadow-lg border border-neutral-200 text-left">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Compétences</label>
                    <select className="w-full p-2 border border-neutral-300 rounded-md">
                      <option value="">Toutes les compétences</option>
                      <option value="modeling">Modélisation 3D</option>
                      <option value="animation">Animation</option>
                      <option value="rendering">Rendu</option>
                      <option value="texturing">Texturing</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Disponibilité</label>
                    <select className="w-full p-2 border border-neutral-300 rounded-md">
                      <option value="">Toutes disponibilités</option>
                      <option value="available">Disponible maintenant</option>
                      <option value="part-time">Temps partiel</option>
                      <option value="full-time">Temps plein</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Trier par</label>
                    <select className="w-full p-2 border border-neutral-300 rounded-md">
                      <option value="relevance">Pertinence</option>
                      <option value="rating">Évaluation</option>
                      <option value="price-low">Prix (croissant)</option>
                      <option value="price-high">Prix (décroissant)</option>
                    </select>
                  </div>
                </div>

                <div className="mt-4 flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    className="mr-2"
                    onClick={() => setShowFilters(false)}
                  >
                    Annuler
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleSearch}
                  >
                    Appliquer les filtres
                  </Button>
                </div>
              </div>
            )}
          </form>
        </div>
      </Container>
    </div>
  );
};

export default HeroSection;
