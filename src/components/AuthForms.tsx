import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { KeyRound, Mail, Lock, UserCircle2 } from 'lucide-react';
import { API_BASE_URL } from '../config';
import { useProfileWizard } from '../context/ProfileWizardContext';
import Header from './Header';
import Footer from './Footer';

export default function AuthForms() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { openProfileWizard } = useProfileWizard();

  useEffect(() => {
    if (searchParams.get('verified') === 'true') {
      setSuccessMessage('Votre adresse e-mail a été vérifiée avec succès !');
    }
  }, [searchParams]);

  const checkClientProfile = async (userId: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/profile/client/show/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
      });

      const result = await response.json();

      if (response.ok) {
        return result.profile;
      } else {
        return null;
      }
    } catch (err) {
      console.error('Erreur lors de la vérification du profil client:', err);
      return null;
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    const data = {
      email,
      password,
      ...(isLogin ? {} : { first_name: firstName, last_name: lastName }),
    };

    try {
      const response = await fetch(`${API_BASE_URL}/api/${isLogin ? 'login' : 'register'}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        console.log('Réponse de l\'API :', result);

        if (isLogin && result.user && result.token) {
          localStorage.removeItem('user');
          localStorage.setItem('user', JSON.stringify(result.user));
          localStorage.setItem('token', result.token);

          if (result.user.is_professional === false) {
            const clientProfile = await checkClientProfile(result.user.id);

            if (!clientProfile) {
              navigate('/create-profile-client');
            } else {
              navigate('/professionel-independants');
            }
          } else {
            navigate(`/profile/${result.user.id}`);
          }

          openProfileWizard();
        } else if (!isLogin) {
          navigate('/login');
        }
      } else {
        setError(result.message || 'Une erreur est survenue');
      }
    } catch (err) {
      setError('Erreur de connexion à l\'API');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleForm = () => {
    setIsLogin(!isLogin);
    setError('');
  };

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full">
          <div className="text-center mb-8">
            {isLogin ? (
              <KeyRound className="w-16 h-16 mx-auto text-indigo-600 mb-4" />
            ) : (
              <UserCircle2 className="w-16 h-16 mx-auto text-indigo-600 mb-4" />
            )}
            <h1 className="text-2xl font-bold text-gray-900">
              {isLogin ? 'Connexion' : 'Inscription'}
            </h1>
            <p className="text-gray-600 mt-2">
              {isLogin
                ? 'Connectez-vous à votre compte'
                : 'Créez votre compte pour commencer'}
            </p>
            {successMessage && (
              <div className="mt-4 p-4 bg-green-50 border border-green-200 text-green-700 rounded-lg">
                {successMessage}
              </div>
            )}
          </div>
          {error && (
            <div className="text-red-500 text-center mb-4">
              {error}
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-6">
            {!isLogin && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Prénom</label>
                  <input
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    required
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 bg-gray-50 px-4 py-2"
                    placeholder="Jean"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nom</label>
                  <input
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    required
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 bg-gray-50 px-4 py-2"
                    placeholder="Dupont"
                  />
                </div>
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700">Email</label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="block w-full pl-10 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 bg-gray-50 px-4 py-2"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Mot de passe</label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength={8}
                  className="block w-full pl-10 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 bg-gray-50 px-4 py-2"
                  placeholder="••••••••"
                />
              </div>
              {!isLogin && (
                <p className="mt-1 text-sm text-gray-500">
                  Le mot de passe doit contenir au moins 8 caractères
                </p>
              )}
            </div>
            {!isLogin && (
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Confirmer le mot de passe
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="password"
                    required
                    minLength={8}
                    className="block w-full pl-10 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 bg-gray-50 px-4 py-2"
                    placeholder="••••••••"
                  />
                </div>
              </div>
            )}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition-colors disabled:bg-indigo-300 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                </div>
              ) : (
                isLogin ? 'Se connecter' : "S'inscrire"
              )}
            </button>
          </form>
          <div className="mt-6 text-center">
            <button
              type="button"
              onClick={toggleForm}
              className="text-sm font-medium text-indigo-600 hover:text-indigo-500"
            >
              {isLogin ? "Pas encore de compte ? S'inscrire" : "Déjà un compte ? Se connecter"}
            </button>
          </div>
          <div className="mt-6 text-center">
            <button
              type="button"
              onClick={() => navigate('/forgot-password')}
              className="text-sm font-medium text-indigo-600 hover:text-indigo-500"
            >
              Mot de passe oublié ?
            </button>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}