import React from "react";

interface Tab {
  id: string;
  label: string;
}

interface TabsProps {
  tabs: Tab[]; // La liste des onglets
  activeTab: string; // L'onglet actif
  setActiveTab: (tabId: string) => void; // Fonction pour changer l'onglet actif
}

const Tabs: React.FC<TabsProps> = ({ tabs, activeTab, setActiveTab }) => {
  return (
    <div className="flex space-x-4">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => setActiveTab(tab.id)}
          className={`px-4 py-2 ${
            activeTab === tab.id
              ? "text-blue-500 font-bold"
              : "text-gray-700 hover:text-blue-500"
          }`}
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
};

export default Tabs;
