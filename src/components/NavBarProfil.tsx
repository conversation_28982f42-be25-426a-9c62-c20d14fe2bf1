import React, { useState } from "react";
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faSearch,
  faBell,
  faEnvelope,
  faChevronDown,
} from "@fortawesome/free-solid-svg-icons";
import MenuProfile from './MenuProfile'; // Importez le composant MenuProfile
import NotificationList from './NotificationList'; // Importez le composant NotificationList

const NavBarProfil: React.FC = () => {
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false); // État pour gérer l'ouverture du menu
  const [isNotificationOpen, setIsNotificationOpen] = useState(false); // État pour gérer l'ouverture des notifications
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const handleHomeClick = () => {
    if (user && user.is_professional === false) {
      navigate('/lists-independants'); // Redirige vers la page des indépendants si l'utilisateur n'est pas un professionnel
    } else {
      navigate('/'); // Redirige vers la page d'accueil par défaut
    }
    // navigate('/'); // Redirige vers la page d'accueil
  };

  const handleListeOffre = () => {
    if (user && user.is_professional === false) {
      navigate('/lists-independants'); // Redirige vers la page des indépendants si l'utilisateur n'est pas un professionnel
    } else {
      navigate('/pro/offres'); // Redirige vers la page d'accueil par défaut
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    localStorage.removeItem('userProfile');
    navigate('/login');
  };

  // Récupérez les données de l'utilisateur

  return (
    <nav className="bg-white shadow-sm py-2 px-6 flex items-center justify-between">
      {/* Partie gauche */}
      <div className="flex items-center space-x-6">
        <span
          className="text-2xl font-bold cursor-pointer"
          onClick={handleHomeClick}
        >
          Hi 3D Artiste
        </span>
        <span className="text-gray-600 cursor-pointer">Explorer</span>
        <span className="text-gray-600 cursor-pointer"
          onClick={handleListeOffre}
        >
          {user && user.is_professional === false ? 'Services Disponible' : 'Offres Disponible'}
          
        </span>

        <div className="flex items-center space-x-1 cursor-pointer">
          <span className="text-gray-600">Recrutez des indépendants</span>
          <FontAwesomeIcon icon={faChevronDown} className="text-gray-500 text-sm" />
        </div>
      </div>

      {/* Barre de recherche */}
      <div className="relative w-80">
        <input
          type="text"
          placeholder="Recherche..."
          className="w-full px-4 py-2 border rounded-full pl-10 bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300"
        />
        <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-3 text-gray-500" />
      </div>

      {/* Partie droite */}
      <div className="flex items-center space-x-4">
        <button className="border px-4 py-2 rounded-full text-sm font-semibold">
          Partager votre travail
        </button>
        <FontAwesomeIcon
          icon={faEnvelope}
          className="text-gray-600 text-lg cursor-pointer relative"
          onClick={() => setIsNotificationOpen(!isNotificationOpen)}
        />
        {isNotificationOpen && (
          <NotificationList />
        )}
        <FontAwesomeIcon icon={faBell} className="text-gray-600 text-lg cursor-pointer" />
        <div className="relative">
          <img
            src="https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            alt="Profil"
            className="w-8 h-8 rounded-full cursor-pointer"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          />
          {isMenuOpen && (
            <div className="absolute mt-2 bg-white border rounded-lg px-4 w-48 z-50 ml-[-180px]">
              <MenuProfile
                user={user}
                setIsModalOpen={() => { }}
                handleLogout={handleLogout}
                setIsMenuOpen={setIsMenuOpen}
              />
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default NavBarProfil;