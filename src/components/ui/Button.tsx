import React, { ButtonHTMLAttributes, ReactNode } from 'react';
import { Link } from 'react-router-dom';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  fullWidth?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  isLoading?: boolean;
  href?: string;
  external?: boolean;
  ariaLabel?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  leftIcon,
  rightIcon,
  isLoading = false,
  href,
  external = false,
  className = '',
  ariaLabel,
  ...props
}) => {
  // Base classes
  const baseClasses = 'inline-flex items-center justify-center font-normal transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';

  // Variant classes
  const variantClasses = {
    primary: 'bg-primary-600 text-black hover:bg-primary-700 focus:ring-primary-500 border border-transparent shadow-md text-opacity-100',
    secondary: 'bg-secondary-600 text-black hover:bg-secondary-700 focus:ring-secondary-500 border border-transparent shadow-md',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500',
    ghost: 'bg-transparent hover:bg-neutral-100 text-neutral-700 focus:ring-neutral-500 border border-transparent',
    danger: 'bg-red-600 text-black hover:bg-red-700 focus:ring-red-500 border border-transparent shadow-md',
  };

  // Appliquer des styles spécifiques pour les boutons avec fond blanc
  if (className?.includes('bg-white')) {
    variantClasses.primary = 'bg-white text-black hover:bg-neutral-50 focus:ring-primary-500 border border-neutral-200 shadow-sm';
  }

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm rounded-full',
    md: 'px-4 py-2 rounded-full',
    lg: 'px-6 py-3 text-lg rounded-full',
    icon: 'p-2 rounded-full',
  };

  // Width classes
  const widthClasses = fullWidth ? 'w-full' : '';

  // Combine all classes
  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClasses} ${className}`;

  // Loading state
  const loadingSpinner = (
    <svg
      className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      aria-hidden="true"
      role="status"
    >
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  );

  // Button content
  const content = (
    <>
      {isLoading && loadingSpinner}
      {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
      {children}
      {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
    </>
  );

  // If href is provided, render as a link
  if (href) {
    if (external) {
      return (
        <a
          href={href}
          className={buttonClasses}
          target="_blank"
          rel="noopener noreferrer"
          aria-label={ariaLabel}
        >
          {content}
        </a>
      );
    }

    return (
      <Link to={href} className={buttonClasses} aria-label={ariaLabel}>
        {content}
      </Link>
    );
  }

  // Otherwise render as a button
  return (
    <button
      className={buttonClasses}
      disabled={isLoading || props.disabled}
      aria-label={ariaLabel}
      aria-busy={isLoading ? 'true' : 'false'}
      {...props}
    >
      {content}
    </button>
  );
};

// Optimiser le composant avec React.memo pour éviter les re-rendus inutiles
export default React.memo(Button);
