# Composants UI

Ce dossier contient les composants UI réutilisables pour l'application. Ces composants sont conçus pour être modulaires, accessibles et faciles à utiliser.

## Composants disponibles

### Alert

Un composant pour afficher des messages d'information, d'er<PERSON><PERSON>, de succès ou d'avertissement.

```tsx
import Alert from './components/ui/Alert';

<Alert 
  type="success" 
  title="Opération réussie" 
  onClose={() => setShowAlert(false)}
>
  Votre profil a été mis à jour avec succès.
</Alert>
```

**Props:**
- `type`: 'success' | 'error' | 'warning' | 'info' (obligatoire)
- `title`: string (optionnel)
- `children`: ReactNode (obligatoire)
- `onClose`: () => void (optionnel)
- `className`: string (optionnel)
- `icon`: ReactNode (optionnel)

### Button

Un composant de bouton polyvalent qui peut être utilisé comme un bouton standard, un lien ou un bouton de soumission de formulaire.

```tsx
import Button from './components/ui/Button';

<Button 
  variant="primary" 
  size="md" 
  onClick={handleClick}
  leftIcon={<Icon />}
>
  Cliquez-moi
</Button>
```

**Props:**
- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' (défaut: 'primary')
- `size`: 'sm' | 'md' | 'lg' (défaut: 'md')
- `fullWidth`: boolean (défaut: false)
- `leftIcon`: ReactNode (optionnel)
- `rightIcon`: ReactNode (optionnel)
- `isLoading`: boolean (défaut: false)
- `href`: string (optionnel, transforme le bouton en lien)
- `external`: boolean (défaut: false, pour les liens externes)
- `ariaLabel`: string (optionnel)
- ...autres props HTML de bouton

### Card

Un composant pour afficher du contenu dans une carte.

```tsx
import Card from './components/ui/Card';

<Card 
  title="Titre de la carte" 
  subtitle="Sous-titre optionnel"
  footer={<Button>Action</Button>}
  hoverable
>
  Contenu de la carte
</Card>
```

**Props:**
- `children`: ReactNode (obligatoire)
- `title`: string | ReactNode (optionnel)
- `subtitle`: string (optionnel)
- `footer`: ReactNode (optionnel)
- `className`: string (optionnel)
- `headerClassName`: string (optionnel)
- `bodyClassName`: string (optionnel)
- `footerClassName`: string (optionnel)
- `onClick`: () => void (optionnel)
- `hoverable`: boolean (défaut: false)
- `bordered`: boolean (défaut: true)
- `shadow`: 'none' | 'sm' | 'md' | 'lg' (défaut: 'md')

### LoadingSpinner

Un composant pour indiquer les états de chargement.

```tsx
import LoadingSpinner from './components/ui/LoadingSpinner';

<LoadingSpinner size="md" label="Chargement en cours" />
```

**Props:**
- `size`: 'sm' | 'md' | 'lg' (défaut: 'md')
- `color`: string (défaut: 'currentColor')
- `className`: string (optionnel)
- `label`: string (défaut: 'Chargement en cours')

### Modal

Un composant modal accessible pour afficher du contenu dans une fenêtre modale.

```tsx
import Modal from './components/ui/Modal';

<Modal 
  isOpen={isOpen} 
  onClose={handleClose} 
  title="Titre du modal"
  footer={<Button onClick={handleSave}>Enregistrer</Button>}
>
  Contenu du modal
</Modal>
```

**Props:**
- `isOpen`: boolean (obligatoire)
- `onClose`: () => void (obligatoire)
- `title`: string (obligatoire)
- `children`: ReactNode (obligatoire)
- `footer`: ReactNode (optionnel)
- `size`: 'sm' | 'md' | 'lg' | 'xl' | 'full' (défaut: 'md')
- `closeOnClickOutside`: boolean (défaut: true)
- `closeOnEsc`: boolean (défaut: true)
- `className`: string (optionnel)

### PageTitle

Un composant pour afficher le titre de la page.

```tsx
import PageTitle from './components/ui/PageTitle';
import { Home } from 'lucide-react';

<PageTitle 
  title="Tableau de bord" 
  subtitle="Bienvenue sur votre tableau de bord"
  icon={<Home className="h-6 w-6" />}
  actions={<Button>Nouvelle action</Button>}
/>
```

**Props:**
- `title`: string (obligatoire)
- `subtitle`: string (optionnel)
- `icon`: ReactNode (optionnel)
- `actions`: ReactNode (optionnel)
- `className`: string (optionnel)

## Bonnes pratiques

1. **Accessibilité**: Tous les composants sont conçus pour être accessibles. Assurez-vous de fournir les attributs ARIA appropriés lorsque nécessaire.

2. **Réutilisabilité**: Ces composants sont conçus pour être réutilisables. Évitez d'ajouter des styles ou des comportements spécifiques à un cas d'utilisation particulier.

3. **Tests**: Chaque composant a des tests unitaires. Assurez-vous de mettre à jour les tests lorsque vous modifiez un composant.

4. **Documentation**: Mettez à jour cette documentation lorsque vous ajoutez ou modifiez un composant.

5. **Performance**: Utilisez `React.memo` et `useCallback` pour optimiser les performances lorsque nécessaire.

## Exemples d'utilisation

### Formulaire avec validation

```tsx
import Button from './components/ui/Button';
import Alert from './components/ui/Alert';
import { useState } from 'react';

const MyForm = () => {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Logique de soumission du formulaire
      await submitForm();
    } catch (err) {
      setError('Une erreur est survenue. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && (
        <Alert type="error" onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      
      {/* Champs du formulaire */}
      
      <Button type="submit" isLoading={isLoading}>
        Soumettre
      </Button>
    </form>
  );
};
```

### Page avec titre et contenu

```tsx
import PageTitle from './components/ui/PageTitle';
import Card from './components/ui/Card';
import Button from './components/ui/Button';
import { Plus } from 'lucide-react';

const MyPage = () => {
  return (
    <div className="container mx-auto p-4">
      <PageTitle 
        title="Mes projets" 
        subtitle="Gérez vos projets en cours"
        actions={
          <Button leftIcon={<Plus className="h-4 w-4" />}>
            Nouveau projet
          </Button>
        }
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
        <Card 
          title="Projet A" 
          subtitle="En cours"
          footer={<Button variant="outline">Voir les détails</Button>}
          hoverable
        >
          <p>Description du projet A...</p>
        </Card>
        
        {/* Autres cartes */}
      </div>
    </div>
  );
};
```
