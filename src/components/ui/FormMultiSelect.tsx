import React, { ReactNode, useState, useRef, useEffect } from 'react';
import { ChevronDown, X } from 'lucide-react';

interface Option {
  value: string;
  label: string;
}

interface FormMultiSelectProps {
  label: string;
  id: string;
  options: Option[];
  value: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  icon?: ReactNode;
}

const FormMultiSelect: React.FC<FormMultiSelectProps> = ({
  label,
  id,
  options,
  value,
  onChange,
  placeholder = 'Sélectionner...',
  error,
  required = false,
  disabled = false,
  className = '',
  icon,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Toggle option selection
  const toggleOption = (optionValue: string) => {
    if (value.includes(optionValue)) {
      onChange(value.filter(v => v !== optionValue));
    } else {
      onChange([...value, optionValue]);
    }
  };

  // Remove a selected option
  const removeOption = (optionValue: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(value.filter(v => v !== optionValue));
  };

  // Get selected option labels
  const selectedLabels = value.map(v => {
    const option = options.find(opt => opt.value === v);
    return option ? option.label : v;
  });

  return (
    <div className={`space-y-1 ${className}`} ref={dropdownRef}>
      <label htmlFor={id} className="block text-sm font-medium text-neutral-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {icon}
          </div>
        )}

        <div
          className={`
            block w-full rounded-md shadow-sm border
            ${icon ? 'pl-10' : 'pl-3'}
            pr-10 py-2
            ${error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-neutral-300 focus:ring-primary-500 focus:border-primary-500'}
            ${disabled ? 'bg-neutral-100 text-neutral-500' : 'cursor-pointer'}
            min-h-[42px]
          `}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          {value.length === 0 ? (
            <span className="text-neutral-500">{placeholder}</span>
          ) : (
            <div className="flex flex-wrap gap-1">
              {selectedLabels.map((label, index) => (
                <div
                  key={index}
                  className="bg-primary-100 text-primary-800 px-2 py-1 rounded-md text-sm flex items-center"
                >
                  {label}
                  <button
                    type="button"
                    onClick={(e) => removeOption(value[index], e)}
                    className="ml-1 text-primary-600 hover:text-primary-800"
                    title={`Supprimer ${label}`}
                    aria-label={`Supprimer ${label}`}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          )}

          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <ChevronDown className={`h-5 w-5 text-neutral-400 transition-transform ${isOpen ? 'transform rotate-180' : ''}`} />
          </div>
        </div>

        {isOpen && !disabled && (
          <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-1 max-h-60 overflow-auto">
            {options.length === 0 ? (
              <div className="px-3 py-2 text-neutral-500">Aucune option disponible</div>
            ) : (
              options.map((option) => (
                <div
                  key={option.value}
                  className={`
                    px-3 py-2 cursor-pointer hover:bg-neutral-100
                    ${value.includes(option.value) ? 'bg-primary-50 text-primary-800' : 'text-neutral-700'}
                  `}
                  onClick={() => toggleOption(option.value)}
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`${id}-option-${option.value}`}
                      name={`${id}-option-${option.value}`}
                      checked={value.includes(option.value)}
                      onChange={() => {}}
                      className="h-4 w-4 text-primary-600 border-neutral-300 rounded"
                      aria-label={option.label}
                    />
                    <span className="ml-2">{option.label}</span>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>

      {error && (
        <p className="text-red-600 text-sm mt-1">{error}</p>
      )}
    </div>
  );
};

export default FormMultiSelect;
