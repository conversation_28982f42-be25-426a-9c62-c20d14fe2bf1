import React, { useState } from 'react';
import { X, ChevronDown, ChevronUp, Filter } from 'lucide-react';
import Button from './Button';

export interface FilterOption {
  id: string;
  label: string;
  options: { value: string; label: string }[];
  type: 'select' | 'checkbox' | 'radio' | 'range';
  multiple?: boolean;
}

export interface FilterPanelProps {
  title?: string;
  filters: FilterOption[];
  onApplyFilters: (filters: Record<string, any>) => void;
  onClearFilters?: () => void;
  className?: string;
  compact?: boolean;
  initialValues?: Record<string, any>;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  title = 'Filtres',
  filters,
  onApplyFilters,
  onClearFilters,
  className = '',
  compact = false,
  initialValues = {},
}) => {
  const [isOpen, setIsOpen] = useState(!compact);
  const [filterValues, setFilterValues] = useState<Record<string, any>>(initialValues);
  
  const handleFilterChange = (filterId: string, value: any) => {
    setFilterValues(prev => ({
      ...prev,
      [filterId]: value,
    }));
  };
  
  const handleApplyFilters = () => {
    onApplyFilters(filterValues);
    if (compact) {
      setIsOpen(false);
    }
  };
  
  const handleClearFilters = () => {
    const emptyFilters = Object.keys(filterValues).reduce((acc, key) => {
      acc[key] = filters.find(f => f.id === key)?.multiple ? [] : '';
      return acc;
    }, {} as Record<string, any>);
    
    setFilterValues(emptyFilters);
    
    if (onClearFilters) {
      onClearFilters();
    }
  };
  
  return (
    <div className={`bg-white rounded-lg border border-neutral-200 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-200">
        <div className="flex items-center">
          <Filter className="h-5 w-5 text-neutral-500 mr-2" />
          <h3 className="font-medium text-neutral-800">{title}</h3>
        </div>
        
        {compact && (
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="text-neutral-500 hover:text-neutral-700"
          >
            {isOpen ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </button>
        )}
      </div>
      
      {/* Filter Content */}
      {isOpen && (
        <>
          <div className="p-4 space-y-6">
            {filters.map((filter) => (
              <div key={filter.id} className="space-y-2">
                <label className="block text-sm font-medium text-neutral-700">
                  {filter.label}
                </label>
                
                {filter.type === 'select' && (
                  <select
                    className="w-full p-2 border border-neutral-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    value={filterValues[filter.id] || ''}
                    onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                  >
                    <option value="">Tous</option>
                    {filter.options.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                )}
                
                {filter.type === 'checkbox' && (
                  <div className="space-y-2">
                    {filter.options.map((option) => {
                      const isChecked = Array.isArray(filterValues[filter.id])
                        ? filterValues[filter.id]?.includes(option.value)
                        : false;
                      
                      return (
                        <div key={option.value} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`${filter.id}-${option.value}`}
                            checked={isChecked}
                            onChange={(e) => {
                              const currentValues = Array.isArray(filterValues[filter.id])
                                ? [...filterValues[filter.id]]
                                : [];
                              
                              if (e.target.checked) {
                                handleFilterChange(filter.id, [...currentValues, option.value]);
                              } else {
                                handleFilterChange(
                                  filter.id,
                                  currentValues.filter((v) => v !== option.value)
                                );
                              }
                            }}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                          />
                          <label
                            htmlFor={`${filter.id}-${option.value}`}
                            className="ml-2 text-sm text-neutral-700"
                          >
                            {option.label}
                          </label>
                        </div>
                      );
                    })}
                  </div>
                )}
                
                {filter.type === 'radio' && (
                  <div className="space-y-2">
                    {filter.options.map((option) => (
                      <div key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          id={`${filter.id}-${option.value}`}
                          name={filter.id}
                          value={option.value}
                          checked={filterValues[filter.id] === option.value}
                          onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300"
                        />
                        <label
                          htmlFor={`${filter.id}-${option.value}`}
                          className="ml-2 text-sm text-neutral-700"
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
                
                {filter.type === 'range' && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={filterValues[filter.id]?.min || ''}
                      onChange={(e) => 
                        handleFilterChange(filter.id, {
                          ...filterValues[filter.id],
                          min: e.target.value,
                        })
                      }
                      className="w-full p-2 border border-neutral-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    />
                    <span className="text-neutral-500">-</span>
                    <input
                      type="number"
                      placeholder="Max"
                      value={filterValues[filter.id]?.max || ''}
                      onChange={(e) =>
                        handleFilterChange(filter.id, {
                          ...filterValues[filter.id],
                          max: e.target.value,
                        })
                      }
                      className="w-full p-2 border border-neutral-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {/* Footer */}
          <div className="p-4 border-t border-neutral-200 flex justify-end space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearFilters}
            >
              Réinitialiser
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={handleApplyFilters}
            >
              Appliquer
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default FilterPanel;
