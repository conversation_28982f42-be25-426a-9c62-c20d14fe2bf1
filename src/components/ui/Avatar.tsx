import React from 'react';

export interface AvatarProps {
  src?: string;
  alt?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  className?: string;
  status?: 'online' | 'offline' | 'busy' | 'away';
  fallback?: string; // Initials or icon
  onClick?: () => void;
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  alt = 'Avatar',
  size = 'sm',
  className = '',
  status,
  fallback,
  onClick,
}) => {
  // Size classes
  const sizeClasses = {
    xs: 'h-6 w-6 text-xs',
    sm: 'h-8 w-8 text-sm',
    md: 'h-10 w-10 text-base',
    lg: 'h-12 w-12 text-lg',
    xl: 'h-16 w-16 text-xl',
    xxl: 'h-20 w-20 text-2xl',
  };
  
  // Status classes and positions
  const statusClasses = {
    online: 'bg-green-500',
    offline: 'bg-neutral-400',
    busy: 'bg-red-500',
    away: 'bg-yellow-500',
  };
  
  const statusSizes = {
    xs: 'h-1.5 w-1.5',
    sm: 'h-2 w-2',
    md: 'h-2.5 w-2.5',
    lg: 'h-3 w-3',
    xl: 'h-4 w-4',
    xxl: 'h-6 w-6',
  };
  
  // Generate initials from alt text if fallback not provided
  const getInitials = () => {
    if (fallback) return fallback;
    
    return alt
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };
  
  // Clickable classes
  const clickableClasses = onClick ? 'cursor-pointer' : '';
  
  return (
    <div className={`relative inline-block ${clickableClasses}`} onClick={onClick}>
      {src ? (
        <img
          src={src}
          alt={alt}
          className={`rounded-full object-cover ${sizeClasses[size]} ${className}`}
        />
      ) : (
        <div className={`rounded-full flex items-center justify-center bg-primary-100 text-primary-800 font-medium ${sizeClasses[size]} ${className}`}>
          {getInitials()}
        </div>
      )}
      
      {status && (
        <span className={`absolute bottom-0 right-0 block rounded-full ring-2 ring-white ${statusClasses[status]} ${statusSizes[size]}`} />
      )}
    </div>
  );
};

export default Avatar;
