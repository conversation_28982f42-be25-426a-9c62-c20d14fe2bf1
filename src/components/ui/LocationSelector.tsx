import React, { useState, useEffect } from "react";
import { Search, ChevronDown, MapPin } from "lucide-react";
import { COUNTRIES } from "../../data/countries";
import { CITIES_BY_COUNTRY } from "../../data/cities";

interface LocationSelectorProps {
  country: string;
  city: string;
  address: string;
  onCountryChange: (country: string) => void;
  onCityChange: (city: string) => void;
  onAddressChange: (address: string) => void;
  required?: boolean;
}

const LocationSelector: React.FC<LocationSelectorProps> = ({
  country,
  city,
  address,
  onCountryChange,
  onCityChange,
  onAddressChange,
  required = false,
}) => {
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [countrySearchTerm, setCountrySearchTerm] = useState("");
  const [filteredCountries, setFilteredCountries] = useState(COUNTRIES);
  const [showCityDropdown, setShowCityDropdown] = useState(false);
  const [citySearchTerm, setCitySearchTerm] = useState("");
  const [filteredCities, setFilteredCities] = useState<string[]>([]);
  const [addressSuggestions, setAddressSuggestions] = useState<Array<{
    display_name: string;
    lat: string;
    lon: string;
  }>>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Filtrer les pays en fonction du terme de recherche
  useEffect(() => {
    if (countrySearchTerm) {
      const filtered = COUNTRIES.filter(country =>
        country.toLowerCase().includes(countrySearchTerm.toLowerCase())
      );
      setFilteredCountries(filtered);
    } else {
      setFilteredCountries(COUNTRIES);
    }
  }, [countrySearchTerm]);

  // Filtrer les villes en fonction du pays sélectionné et du terme de recherche
  useEffect(() => {
    if (country && CITIES_BY_COUNTRY[country]) {
      const cities = CITIES_BY_COUNTRY[country];
      if (citySearchTerm) {
        const filtered = cities.filter(city =>
          city.toLowerCase().includes(citySearchTerm.toLowerCase())
        );
        setFilteredCities(filtered);
      } else {
        setFilteredCities(cities);
      }
    } else {
      setFilteredCities([]);
    }
  }, [country, citySearchTerm]);

  // Fonction pour obtenir le code pays ISO
  const getCountryCode = (country: string): string => {
    const countryCodes: Record<string, string> = {
      "France": "fr",
      "Belgique": "be",
      "Suisse": "ch",
      "Canada": "ca",
      "États-Unis": "us",
      "Royaume-Uni": "gb",
      "Allemagne": "de",
      "Espagne": "es",
      "Italie": "it",
      "Portugal": "pt"
    };
    return countryCodes[country] || "";
  };

  // Fonction pour récupérer les suggestions d'adresse
  const fetchAddressSuggestions = async (query: string) => {
    if (query.length < 3 || !country || !city) {
      setAddressSuggestions([]);
      return;
    }

    try {
      const searchQuery = `${query}, ${city}, ${country}`;
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
          searchQuery
        )}&limit=5&addressdetails=1&countrycodes=${getCountryCode(country)}`
      );
      const data = await response.json();
      
      const filteredData = data.filter((item: any) => {
        const address = item.address;
        return (
          address.city?.toLowerCase() === city.toLowerCase() ||
          address.town?.toLowerCase() === city.toLowerCase() ||
          address.village?.toLowerCase() === city.toLowerCase() ||
          address.suburb?.toLowerCase() === city.toLowerCase()
        );
      });

      setAddressSuggestions(filteredData);
    } catch (error) {
      console.error("Erreur lors de la récupération des suggestions:", error);
      setAddressSuggestions([]);
    }
  };

  // Fonction pour gérer le changement d'adresse
  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    onAddressChange(value);
    
    if (country && city) {
      fetchAddressSuggestions(value);
      setShowSuggestions(true);
    } else {
      setAddressSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Fonction pour sélectionner une adresse
  const handleAddressSelect = (suggestion: {
    display_name: string;
    lat: string;
    lon: string;
  }) => {
    onAddressChange(suggestion.display_name);
    setAddressSuggestions([]);
    setShowSuggestions(false);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label
            htmlFor="country"
            className="block text-sm font-medium text-neutral-700 mb-1"
          >
            Pays {required && "*"}
          </label>
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowCountryDropdown(!showCountryDropdown)}
              className="w-full flex items-center justify-between px-4 py-2 border border-neutral-300 rounded-lg bg-white text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <span>{country || "Sélectionner un pays"}</span>
              <ChevronDown
                size={20}
                className={`transition-transform ${
                  showCountryDropdown ? "rotate-180" : ""
                }`}
              />
            </button>

            {showCountryDropdown && (
              <div className="absolute z-50 mt-1 w-full bg-white border border-neutral-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                <div className="sticky top-0 bg-white p-2 border-b border-neutral-200">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search size={18} className="text-neutral-400" />
                    </div>
                    <input
                      type="text"
                      value={countrySearchTerm}
                      onChange={(e) => setCountrySearchTerm(e.target.value)}
                      className="pl-10 w-full px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Rechercher un pays..."
                    />
                  </div>
                </div>
                <div className="max-h-48 overflow-y-auto">
                  {filteredCountries.map((countryOption) => (
                    <div
                      key={countryOption}
                      className="px-4 py-2 hover:bg-neutral-100 cursor-pointer"
                      onClick={() => {
                        onCountryChange(countryOption);
                        setShowCountryDropdown(false);
                        setCountrySearchTerm("");
                      }}
                    >
                      {countryOption}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <div>
          <label
            htmlFor="city"
            className="block text-sm font-medium text-neutral-700 mb-1"
          >
            Ville {required && "*"}
          </label>
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowCityDropdown(!showCityDropdown)}
              disabled={!country}
              className={`w-full flex items-center justify-between px-4 py-2 border border-neutral-300 rounded-lg bg-white text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                !country ? "bg-neutral-50 text-neutral-500 cursor-not-allowed" : ""
              }`}
            >
              <span>{city || "Sélectionner une ville"}</span>
              <ChevronDown
                size={20}
                className={`transition-transform ${
                  showCityDropdown ? "rotate-180" : ""
                }`}
              />
            </button>

            {showCityDropdown && country && (
              <div className="absolute z-50 mt-1 w-full bg-white border border-neutral-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                <div className="sticky top-0 bg-white p-2 border-b border-neutral-200">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search size={18} className="text-neutral-400" />
                    </div>
                    <input
                      type="text"
                      value={citySearchTerm}
                      onChange={(e) => setCitySearchTerm(e.target.value)}
                      className="pl-10 w-full px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Rechercher une ville..."
                    />
                  </div>
                </div>
                <div className="max-h-48 overflow-y-auto">
                  {filteredCities.length > 0 ? (
                    filteredCities.map((cityOption) => (
                      <div
                        key={cityOption}
                        className="px-4 py-2 hover:bg-neutral-100 cursor-pointer"
                        onClick={() => {
                          onCityChange(cityOption);
                          setShowCityDropdown(false);
                          setCitySearchTerm("");
                        }}
                      >
                        {cityOption}
                      </div>
                    ))
                  ) : (
                    <div className="px-4 py-2 text-neutral-500">
                      Aucune ville trouvée
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="relative">
        <label
          htmlFor="address"
          className="block text-sm font-medium text-neutral-700 mb-1"
        >
          Adresse {required && "*"}
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MapPin className="h-5 w-5 text-neutral-400" />
          </div>
          <input
            type="text"
            id="address"
            value={address}
            onChange={handleAddressChange}
            onFocus={() => setShowSuggestions(true)}
            onBlur={() => {
              setTimeout(() => {
                setShowSuggestions(false);
              }, 200);
            }}
            className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            placeholder="Votre adresse"
            required={required}
          />
          {showSuggestions && addressSuggestions.length > 0 && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-neutral-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {addressSuggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="px-4 py-2 hover:bg-neutral-100 cursor-pointer text-sm"
                  onClick={() => handleAddressSelect(suggestion)}
                >
                  {suggestion.display_name}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LocationSelector; 