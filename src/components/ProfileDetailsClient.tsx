import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMapMarkerAlt } from "@fortawesome/free-solid-svg-icons";
import { useNavigate } from "react-router-dom";
import React, { useState, useEffect } from 'react';
import { MoreHorizontal } from "lucide-react";
import { API_BASE_URL } from '../config';

interface ProfileDetailsProps {
  setIsEditingProfile: (isEditing: boolean) => void;
  setCompletionPercentage: (percentage: number) => void;
}

interface ClientProfile {
  id: number;
  user_id: number;
  type: string;
  company_name: string;
  industry: string;
  description: string;
  created_at: string;
  updated_at: string;
}

const ProfileDetailsClient: React.FC<ProfileDetailsProps> = ({ setIsEditingProfile, setCompletionPercentage }) => {
  const storedProfile = JSON.parse(localStorage.getItem('userProfile') || '{}');
  const user_id = storedProfile?.user_id; // Récupérer l'user_id depuis le localStorage

  const [profile, setProfile] = useState({
    first_name: storedProfile?.profile_data?.first_name || "",
    last_name: storedProfile?.profile_data?.last_name || "",
    phone: storedProfile?.profile_data?.phone || "",
    address: storedProfile?.profile_data?.address || "",
    city: storedProfile?.profile_data?.city || "",
    country: storedProfile?.profile_data?.country || "",
    profileType: storedProfile?.profile_type === "freelance" ? "Indépendant" : "Entreprise",
    availability_status: storedProfile?.profile_data?.availability_status || "",
    created_at: storedProfile?.profile_data?.created_at || "",
    completion_percentage: storedProfile?.completion_percentage || 0,
  });

  const [clientProfile, setClientProfile] = useState<ClientProfile | null>(null);

  useEffect(() => {
    if (Object.keys(storedProfile).length === 0 || !storedProfile.profile_data) {
      const fetchUserProfile = async () => {
        const token = localStorage.getItem('token');
        if (!token) return;

        try {
          const response = await fetch(`${API_BASE_URL}/api/profile/completion`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
          const data = await response.json();

          if (response.ok) {
            setProfile({
              ...profile,
              ...data.profile_data,
              completion_percentage: data.completion_percentage,
            });
            localStorage.setItem('userProfile', JSON.stringify(data));
            setCompletionPercentage(data.completion_percentage);
          } else if (response.status === 404 || data.message === "Profil non trouvé.") {
            setProfile(JSON.parse('{}'));
            localStorage.removeItem('userProfile');
          }
        } catch (error) {
          console.error('Erreur lors de la récupération du profil', error);
        }
      };

      fetchUserProfile();
    }
  }, []);

  useEffect(() => {
    const fetchClientProfile = async () => {
      const token = localStorage.getItem('token');
      const userString = localStorage.getItem('user');
      const user = userString ? JSON.parse(userString) : null;

      console.log("token==============", token);
      console.log("user===========", user);

      if (!token || !user) return; // Vérifier que le token et l'utilisateur sont disponibles

      const userId = user.id; // Récupérer l'ID de l'utilisateur
      console.log("user_id===========", userId);

      try {
        const response = await fetch(`${API_BASE_URL}/api/profile/client/show/${userId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
        const data = await response.json();

        if (response.ok) {
          setClientProfile(data.profile);
        } else {
          console.error('Erreur lors de la récupération du profil client', data);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération du profil client', error);
      }
    };

    fetchClientProfile();
  }, []);

  const navigate = useNavigate();

  const handleContact = () => {
    navigate('/contact');
  };

  return (
    <div className="w-full p-4 bg-white rounded-lg shadow-md mt-16">
      {profile ? (
        <>
          {/* Titre */}
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{profile.first_name} {profile.last_name}</h1>

          {/* Informations du profil */}
          <div className="space-y-2 text-gray-600">
            <p className="flex items-center">
              <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2 text-gray-400" />
              {profile.city}, {profile.country}
            </p>
          </div>

          {/* Boutons */}
          <div className="mt-6 space-y-4">
            <button
              className="w-full px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 flex items-center justify-center gap-2"
              onClick={() => setIsEditingProfile(true)}
            >
              <span>Améliorer le profil</span>
              {profile.completion_percentage > 0 && (
                <span>{profile.completion_percentage}%</span>
              )}
            </button>
          </div>

          {/* Section Engager */}
          <div className="mt-6 p-4 border rounded-lg bg-gray-50">
            {clientProfile ? (
              <>
                <h2 className="text-xl font-bold text-gray-900 mb-2">Profil Client</h2>
                <p><strong>Nom de l'entreprise:</strong> {clientProfile.company_name}</p>
                <p><strong>Industrie:</strong> {clientProfile.industry}</p>
                <p><strong>Description:</strong> {clientProfile.description}</p>
                <p><strong>Type:</strong> {clientProfile.type}</p>
                <p><strong>Créé le:</strong> {new Date(clientProfile.created_at).toLocaleDateString('fr-FR')}</p>
              </>
            ) : (
              <p className="text-gray-600">Chargement du profil client...</p>
            )}
          </div>
        </>
      ) : (
        <p className="text-gray-600">Aucun profil trouvé.</p>
      )}

      <h4 className="text-normal text-gray-900 mb-2 mt-4">CONTACT</h4>
      <div className="w-full">
        <div className="flex items-center justify-between bg-white shadow-sm border rounded-md p-4 w-full">
          <div className="flex items-center space-x-4">
            <div className="bg-gray-200 p-2 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-8 h-8 text-gray-500"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.5 19.5a8.25 8.25 0 0115 0"
                />
              </svg>
            </div>
            <span className="text-gray-700 font-semibold">Mes contacts</span>
          </div>
          <button className="text-gray-500 hover:text-gray-700"
          onClick={handleContact}
          >
            <MoreHorizontal />
          </button>
        </div>
      </div> 
      <h4 className="text-normal text-gray-900 mb-2 mt-4">EQUIPES/MEMBRES</h4>
      <div className="w-full">
        <div className="flex items-center justify-between bg-white shadow-sm border rounded-md p-4 w-full">
          <div className="flex items-center space-x-4">
            <div className="bg-gray-200 p-2 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-8 h-8 text-gray-500"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.5 19.5a8.25 8.25 0 0115 0"
                />
              </svg>
            </div>
            <span className="text-gray-700 font-semibold">Membre 1</span>
          </div>
          <button className="text-gray-500 hover:text-gray-700">
            Suivre
          </button>
        </div>
      </div> 
      <div className="w-full mt-2">
        <div className="flex items-center justify-between bg-white shadow-sm border rounded-md p-4 w-full">
          <div className="flex items-center space-x-4">
            <div className="bg-gray-200 p-2 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-8 h-8 text-gray-500"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.5 19.5a8.25 8.25 0 0115 0"
                />
              </svg>
            </div>
            <span className="text-gray-700 font-semibold">Membre 2</span>
          </div>
          <button className="text-gray-500 hover:text-gray-700">
            Suivre
          </button>
        </div>
      </div> 
    </div>
  );
};

export default ProfileDetailsClient;