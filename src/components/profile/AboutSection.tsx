import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface AboutSectionProps {
  biography?: string;
  education?: string[];
  services?: string[];
}

const AboutSection: React.FC<AboutSectionProps> = ({
  biography,
  education = [],
  services = [],
}) => {
  const [expanded, setExpanded] = useState(false);

  // Mock data if real data is not provided
  const mockBiography = `Je suis un artiste 3D passionné avec plus de 5 ans d'expérience dans la création de modèles 3D, d'animations et de rendus pour divers projets. Ma spécialité est la modélisation de personnages et d'environnements pour les jeux vidéo et les films d'animation.

J'ai travaillé avec des studios de jeux indépendants et des agences de publicité pour créer des assets 3D de haute qualité. Mon objectif est toujours de dépasser les attentes de mes clients en livrant un travail qui combine créativité et précision technique.

Je maîtrise plusieurs logiciels dont Blender, Maya, ZBrush et Substance Painter. Je suis constamment en train d'apprendre de nouvelles techniques pour rester à jour avec les dernières tendances et technologies dans l'industrie de la 3D.`;

  const mockEducation = [
    'Diplôme en Animation 3D, École des Arts Numériques, 2018',
    'Formation avancée en Modélisation 3D, Certification Autodesk, 2019',
    'Master en Design Numérique, Université des Arts Visuels, 2020',
  ];

  const mockServices = [
    'Modélisation de personnages 3D',
    'Animation 3D',
    'Texturing et Shading',
    'Rendu 3D',
    'Conception d\'environnements 3D',
    'Rigging de personnages',
  ];

  const displayBiography = biography || mockBiography;
  const displayEducation = education.length > 0 ? education : mockEducation;
  const displayServices = services.length > 0 ? services : mockServices;

  // Truncate biography for collapsed view
  const truncatedBiography = displayBiography.length > 300 
    ? `${displayBiography.substring(0, 300)}...` 
    : displayBiography;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-6">À propos</h2>
        
        <div className="space-y-6">
          {/* Biography */}
          <div>
            <h3 className="text-lg font-medium mb-3">Biographie</h3>
            <div className="text-neutral-700 whitespace-pre-line">
              {expanded ? displayBiography : truncatedBiography}
              
              {displayBiography.length > 300 && (
                <button 
                  className="text-primary-600 font-medium flex items-center mt-2"
                  onClick={() => setExpanded(!expanded)}
                >
                  {expanded ? (
                    <>Voir moins <ChevronUp className="h-4 w-4 ml-1" /></>
                  ) : (
                    <>Voir plus <ChevronDown className="h-4 w-4 ml-1" /></>
                  )}
                </button>
              )}
            </div>
          </div>
          
          {/* Education */}
          <div>
            <h3 className="text-lg font-medium mb-3">Formation</h3>
            <ul className="list-disc pl-5 space-y-2 text-neutral-700">
              {displayEducation.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
          
          {/* Services */}
          <div>
            <h3 className="text-lg font-medium mb-3">Services proposés</h3>
            <ul className="list-disc pl-5 space-y-2 text-neutral-700">
              {displayServices.map((service, index) => (
                <li key={index}>{service}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutSection;
