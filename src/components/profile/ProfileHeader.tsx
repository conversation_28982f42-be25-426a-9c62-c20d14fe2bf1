import React from 'react';
import { MapPin, Mail, Calendar, Globe, Clock, Star } from 'lucide-react';
import Avatar from '../ui/Avatar';
import Badge from '../ui/Badge';
import Button from '../ui/Button';

interface ProfileHeaderProps {
  firstName: string;
  lastName: string;
  title?: string;
  location?: {
    city?: string;
    country?: string;
  };
  availability?: 'available' | 'busy' | 'unavailable';
  rating?: number;
  reviewCount?: number;
  memberSince?: string;
  responseTime?: string;
  avatarUrl?: string;
  coverUrl?: string;
  onContactClick?: () => void;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  firstName,
  lastName,
  title = 'Artist 3D & Modelisation',
  location,
  availability = 'available',
  rating = 4.8,
  reviewCount = 24,
  memberSince,
  responseTime = 'Moins de 2 heures',
  avatarUrl,
  coverUrl,
  onContactClick,
}) => {
  const defaultCover = 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D';
  const defaultAvatar = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';

  const availabilityText = {
    available: 'Disponible',
    busy: 'Occupé',
    unavailable: 'Indisponible',
  };

  const availabilityVariant = {
    available: 'success',
    busy: 'warning',
    unavailable: 'error',
  };

  return (
    <div className="relative mb-24">
      {/* Cover Image */}
      <div className="h-64 w-full overflow-hidden rounded-lg">
        <img 
          src={coverUrl || defaultCover} 
          alt="Cover" 
          className="w-full h-full object-cover"
        />
      </div>

      {/* Profile Info Container */}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative -mt-16">
          {/* Avatar and Basic Info */}
          <div className="flex flex-col md:flex-row md:items-end">
            <Avatar 
              src={avatarUrl || defaultAvatar}
              fallback={`${firstName[0]}${lastName[0]}`}
              size="xl"
              className="ring-4 ring-white shadow-lg"
            />
            
            <div className="mt-4 md:mt-0 md:ml-6 md:mb-2">
              <div className="flex flex-col md:flex-row md:items-center gap-2">
                <h1 className="text-2xl font-bold text-neutral-900">
                  {firstName} {lastName}
                </h1>
                <Badge 
                  variant={availabilityVariant[availability] as 'success' | 'warning' | 'error'}
                  size="sm"
                >
                  {availabilityText[availability]}
                </Badge>
              </div>
              <p className="text-neutral-600">{title}</p>
            </div>
            
            <div className="mt-4 md:mt-0 md:ml-auto">
              <Button 
                variant="primary"
                leftIcon={<Mail className="h-4 w-4" />}
                onClick={onContactClick}
              >
                Contacter
              </Button>
            </div>
          </div>
          
          {/* Stats and Additional Info */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 border-t border-neutral-200 pt-6">
            <div className="flex items-center text-neutral-600">
              <MapPin className="h-4 w-4 mr-2" />
              <span>{location?.city || 'Paris'}, {location?.country || 'France'}</span>
            </div>
            
            <div className="flex items-center text-neutral-600">
              <Star className="h-4 w-4 mr-2 text-yellow-500" />
              <span>{rating} ({reviewCount} avis)</span>
            </div>
            
            <div className="flex items-center text-neutral-600">
              <Clock className="h-4 w-4 mr-2" />
              <span>Répond en {responseTime}</span>
            </div>
            
            {memberSince && (
              <div className="flex items-center text-neutral-600">
                <Calendar className="h-4 w-4 mr-2" />
                <span>Membre depuis {memberSince}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
