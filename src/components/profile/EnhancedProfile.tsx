import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Edit, MapPin, Mail, Phone, Calendar, Globe, Clock, Award, Briefcase, Star, ChevronRight, Share2, Heart, MessageSquare, Download } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import Header from '../Header';
import Footer from '../Footer';
import Button from '../ui/Button';
import Container from '../layout/Container';
import Grid from '../layout/Grid';
import Section from '../layout/Section';
import { useProfile } from '../ProfileContext';

const EnhancedProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { profile } = useProfile();
  const [isCurrentUser, setIsCurrentUser] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [profileData, setProfileData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('portfolio');
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchProfileData = async () => {
      setIsLoading(true);
      setError('');

      try {
        // Si c'est le profil de l'utilisateur connecté
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (id === user.id?.toString()) {
          setIsCurrentUser(true);
          if (profile && profile.profile_data) {
            setProfileData(profile.profile_data);
            setIsLoading(false);
            return;
          }
        }

        // Sinon, récupérer le profil depuis l'API
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/api/profile/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': token ? `Bearer ${token}` : '',
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Impossible de récupérer les données du profil');
        }

        const data = await response.json();
        setProfileData(data.profile_data);
      } catch (err) {
        console.error('Erreur lors de la récupération du profil:', err);
        setError('Impossible de charger le profil. Veuillez réessayer plus tard.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfileData();
  }, [id, profile]);

  // Fonction pour formater les compétences en badges
  const renderSkillBadges = (skills: string[] | string) => {
    if (!skills) return null;

    const skillsArray = Array.isArray(skills) ? skills : skills.split(',').map(skill => skill.trim());

    return (
      <div className="flex flex-wrap gap-2 mt-2">
        {skillsArray.map((skill, index) => (
          <span
            key={index}
            className="px-3 py-1 bg-primary-50 text-primary-700 rounded-full text-sm font-medium"
          >
            {skill}
          </span>
        ))}
      </div>
    );
  };

  // Fonction pour afficher le statut de disponibilité
  const renderAvailabilityStatus = (status: string, time?: string) => {
    if (!status) return null;

    const statusColors = {
      available: 'bg-green-100 text-green-800',
      unavailable: 'bg-red-100 text-red-800',
      busy: 'bg-yellow-100 text-yellow-800',
    };

    const statusLabels = {
      available: 'Disponible',
      unavailable: 'Non disponible',
      busy: 'Occupé',
    };

    const statusKey = status as keyof typeof statusColors;

    return (
      <div className="flex items-center mt-4">
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusColors[statusKey] || 'bg-gray-100 text-gray-800'}`}>
          <span className={`w-2 h-2 rounded-full mr-2 ${status === 'available' ? 'bg-green-500' : status === 'busy' ? 'bg-yellow-500' : 'bg-red-500'}`}></span>
          {statusLabels[statusKey] || status}
        </span>
        {time && status === 'unavailable' && (
          <span className="ml-2 text-sm text-gray-500">
            Disponible à partir du {new Date(time).toLocaleDateString('fr-FR')}
          </span>
        )}
      </div>
    );
  };

  // Données fictives pour les projets
  const projects = [
    {
      id: 1,
      title: 'Modélisation 3D pour jeu vidéo',
      description: 'Création de personnages et d\'environnements 3D pour un jeu d\'aventure.',
      image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      category: 'Modélisation 3D',
      date: '2023-05-15',
    },
    {
      id: 2,
      title: 'Animation de personnage',
      description: 'Animation d\'un personnage pour une série animée.',
      image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      category: 'Animation',
      date: '2023-03-22',
    },
    {
      id: 3,
      title: 'Rendu architectural',
      description: 'Visualisation 3D d\'un projet architectural moderne.',
      image: 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      category: 'Rendu',
      date: '2023-01-10',
    },
  ];

  // Données fictives pour les expériences
  const experiences = [
    {
      id: 1,
      title: 'Artiste 3D Senior',
      company: 'Studio Créatif XYZ',
      location: 'Paris, France',
      startDate: '2020-01',
      endDate: 'Present',
      description: 'Création de modèles 3D et d\'animations pour des projets de jeux vidéo et de films.',
    },
    {
      id: 2,
      title: 'Animateur 3D',
      company: 'Animation Studio',
      location: 'Lyon, France',
      startDate: '2017-06',
      endDate: '2019-12',
      description: 'Animation de personnages et d\'objets pour des séries animées et des publicités.',
    },
    {
      id: 3,
      title: 'Modeleur 3D Junior',
      company: 'GameDev Inc.',
      location: 'Bordeaux, France',
      startDate: '2015-03',
      endDate: '2017-05',
      description: 'Modélisation de personnages, d\'objets et d\'environnements pour des jeux vidéo.',
    },
  ];

  // Données fictives pour les avis
  const reviews = [
    {
      id: 1,
      author: 'Sophie Martin',
      company: 'Studio Créatif',
      rating: 5,
      date: '2023-06-15',
      content: 'Excellent travail ! Les modèles 3D étaient exactement ce que nous recherchions. Communication fluide et respect des délais.',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
    {
      id: 2,
      author: 'Thomas Dubois',
      company: 'GameStudio',
      rating: 4,
      date: '2023-04-22',
      content: 'Très bon travail sur les animations. Quelques ajustements ont été nécessaires, mais le résultat final est très satisfaisant.',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  ];

  // Fonction pour afficher les étoiles de notation
  const renderRatingStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${star <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
          />
        ))}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Erreur</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button variant="primary" onClick={() => navigate('/')}>
              Retour à l'accueil
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header />

      {/* Bannière du profil */}
      <div className="relative w-full bg-gradient-to-r from-primary-600 to-primary-800 h-64">
        <div className="absolute inset-0 bg-cover bg-center opacity-20" style={{ backgroundImage: "url('https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')" }}></div>

        {isCurrentUser && (
          <button
            className="absolute top-4 right-4 bg-white bg-opacity-80 p-2 rounded-full hover:bg-opacity-100 transition-all"
            onClick={() => navigate('/dashboard/profile/edit')}
          >
            <Edit className="h-5 w-5 text-primary-700" />
          </button>
        )}
      </div>

      <Container className="-mt-20 pb-16">
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="p-6 sm:p-8">
            <div className="flex flex-col md:flex-row">
              {/* Avatar et informations principales */}
              <div className="md:w-1/3 flex flex-col items-center md:items-start">
                <div className="w-32 h-32 rounded-full border-4 border-white shadow-lg overflow-hidden mb-4">
                  <img
                    src="https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                    alt="Avatar"
                    className="w-full h-full object-cover"
                  />
                </div>

                <h1 className="text-2xl font-bold text-gray-900 mb-1 text-center md:text-left">
                  {profileData?.first_name} {profileData?.last_name}
                </h1>

                <p className="text-lg text-primary-600 font-medium mb-4 text-center md:text-left">
                  {profileData?.title || 'Artiste 3D'}
                </p>

                {renderAvailabilityStatus(profileData?.availability_status, profileData?.estimated_response_time)}

                <div className="mt-6 space-y-3 w-full">
                  {profileData?.city && profileData?.country && (
                    <div className="flex items-center text-gray-600">
                      <MapPin className="h-5 w-5 text-gray-400 mr-2" />
                      <span>{profileData.city}, {profileData.country}</span>
                    </div>
                  )}

                  {profileData?.email && (
                    <div className="flex items-center text-gray-600">
                      <Mail className="h-5 w-5 text-gray-400 mr-2" />
                      <span>{profileData.email}</span>
                    </div>
                  )}

                  {profileData?.phone && (
                    <div className="flex items-center text-gray-600">
                      <Phone className="h-5 w-5 text-gray-400 mr-2" />
                      <span>{profileData.phone}</span>
                    </div>
                  )}

                  {profileData?.created_at && (
                    <div className="flex items-center text-gray-600">
                      <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                      <span>Membre depuis {new Date(profileData.created_at).toLocaleDateString('fr-FR', { year: 'numeric', month: 'long' })}</span>
                    </div>
                  )}
                </div>

                {!isCurrentUser && (
                  <div className="mt-6 w-full space-y-3">
                    <Button
                      variant="primary"
                      fullWidth
                      leftIcon={<MessageSquare className="h-4 w-4" />}
                      onClick={() => navigate(`/messages/new?recipient=${id}`)}
                    >
                      Contacter
                    </Button>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        className="flex-1"
                        leftIcon={<Heart className="h-4 w-4" />}
                      >
                        Favoris
                      </Button>

                      <Button
                        variant="outline"
                        className="flex-1"
                        leftIcon={<Share2 className="h-4 w-4" />}
                      >
                        Partager
                      </Button>
                    </div>
                  </div>
                )}

                {isCurrentUser && (
                  <div className="mt-6 w-full">
                    <Button
                      variant="primary"
                      fullWidth
                      onClick={() => navigate('/dashboard/profile/edit')}
                    >
                      Modifier le profil
                    </Button>
                  </div>
                )}
              </div>

              {/* Informations détaillées */}
              <div className="md:w-2/3 md:pl-8 mt-8 md:mt-0">
                <div className="mb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-3">À propos</h2>
                  <p className="text-gray-600">
                    {profileData?.bio || 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D, l\'animation et le rendu pour des jeux vidéo, des films et des projets architecturaux. Spécialisé dans la modélisation de personnages et d\'environnements réalistes.'}
                  </p>
                </div>

                {(profileData?.skills || profileData?.services_offered || profileData?.languages) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {profileData?.skills && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                          <Award className="h-5 w-5 text-primary-600 mr-2" />
                          Compétences
                        </h3>
                        {renderSkillBadges(profileData.skills)}
                      </div>
                    )}

                    {profileData?.services_offered && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                          <Briefcase className="h-5 w-5 text-primary-600 mr-2" />
                          Services
                        </h3>
                        {renderSkillBadges(profileData.services_offered)}
                      </div>
                    )}

                    {profileData?.languages && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                          <Globe className="h-5 w-5 text-primary-600 mr-2" />
                          Langues
                        </h3>
                        {renderSkillBadges(profileData.languages)}
                      </div>
                    )}
                  </div>
                )}

                {/* Statistiques */}
                <div className="bg-gray-50 rounded-lg p-4 grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary-600">117</div>
                    <div className="text-sm text-gray-500">Vues du profil</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary-600">24</div>
                    <div className="text-sm text-gray-500">Projets réalisés</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary-600">4.8</div>
                    <div className="text-sm text-gray-500">Note moyenne</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary-600">3</div>
                    <div className="text-sm text-gray-500">Années d'expérience</div>
                  </div>
                </div>

                {/* Onglets */}
                <div className="border-b border-gray-200">
                  <nav className="flex space-x-8">
                    <button
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'portfolio'
                          ? 'border-primary-600 text-primary-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('portfolio')}
                    >
                      Portfolio
                    </button>
                    <button
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'experience'
                          ? 'border-primary-600 text-primary-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('experience')}
                    >
                      Expérience
                    </button>
                    <button
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'reviews'
                          ? 'border-primary-600 text-primary-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('reviews')}
                    >
                      Avis
                    </button>
                  </nav>
                </div>

                {/* Contenu des onglets */}
                <div className="mt-6">
                  {activeTab === 'portfolio' && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {projects.map((project) => (
                        <div key={project.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                          <div className="aspect-video overflow-hidden">
                            <img
                              src={project.image}
                              alt={project.title}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">{project.title}</h3>
                              <span className="text-xs font-medium text-gray-500">{new Date(project.date).toLocaleDateString('fr-FR')}</span>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{project.description}</p>
                            <span className="inline-block px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs font-medium">
                              {project.category}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {activeTab === 'experience' && (
                    <div className="space-y-6">
                      {experiences.map((experience) => (
                        <div key={experience.id} className="relative pl-8 pb-6 border-l-2 border-gray-200">
                          <div className="absolute left-[-9px] top-0 w-4 h-4 rounded-full bg-primary-600"></div>
                          <div className="mb-1">
                            <h3 className="text-lg font-semibold text-gray-900">{experience.title}</h3>
                            <p className="text-primary-600 font-medium">{experience.company}</p>
                          </div>
                          <div className="flex items-center text-sm text-gray-500 mb-2">
                            <MapPin className="h-4 w-4 mr-1" />
                            <span>{experience.location}</span>
                            <span className="mx-2">•</span>
                            <Clock className="h-4 w-4 mr-1" />
                            <span>
                              {new Date(experience.startDate).toLocaleDateString('fr-FR', { year: 'numeric', month: 'short' })} -
                              {experience.endDate === 'Present' ? ' Présent' : new Date(experience.endDate).toLocaleDateString('fr-FR', { year: 'numeric', month: 'short' })}
                            </span>
                          </div>
                          <p className="text-gray-600">{experience.description}</p>
                        </div>
                      ))}
                    </div>
                  )}

                  {activeTab === 'reviews' && (
                    <div className="space-y-6">
                      {reviews.length > 0 ? (
                        reviews.map((review) => (
                          <div key={review.id} className="bg-white border border-gray-200 rounded-lg p-4">
                            <div className="flex items-start mb-4">
                              <img
                                src={review.avatar}
                                alt={review.author}
                                className="w-10 h-10 rounded-full mr-4"
                              />
                              <div>
                                <h3 className="font-semibold text-gray-900">{review.author}</h3>
                                <p className="text-sm text-gray-500">{review.company}</p>
                                <div className="flex items-center mt-1">
                                  {renderRatingStars(review.rating)}
                                  <span className="ml-2 text-sm text-gray-500">
                                    {new Date(review.date).toLocaleDateString('fr-FR')}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <p className="text-gray-600">{review.content}</p>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <p className="text-gray-500">Aucun avis pour le moment.</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>

      <Footer />
    </div>
  );
};

export default EnhancedProfile;
