import React, { useState } from 'react';
import { Star, ThumbsUp, ChevronDown, ChevronUp } from 'lucide-react';
import Avatar from '../ui/Avatar';
import Button from '../ui/Button';

interface Review {
  id: string | number;
  author: {
    name: string;
    avatarUrl?: string;
  };
  rating: number;
  date: string;
  content: string;
  helpful?: number;
}

interface ReviewsSectionProps {
  reviews?: Review[];
  averageRating?: number;
  totalReviews?: number;
}

const ReviewsSection: React.FC<ReviewsSectionProps> = ({
  reviews = [],
  averageRating = 4.8,
  totalReviews = 0,
}) => {
  const [expandedReviews, setExpandedReviews] = useState<(string | number)[]>([]);
  const [showAllReviews, setShowAllReviews] = useState(false);

  // Mock data if no reviews are provided
  const mockReviews: Review[] = [
    {
      id: 1,
      author: {
        name: '<PERSON>',
        avatarUrl: 'https://randomuser.me/api/portraits/women/44.jpg',
      },
      rating: 5,
      date: '15 mars 2023',
      content: "J'ai travaillé avec ce professionnel sur un projet de visualisation 3D pour notre entreprise. Le résultat était exactement ce que nous recherchions. Communication claire et respect des délais. Je recommande vivement !",
      helpful: 12,
    },
    {
      id: 2,
      author: {
        name: 'Thomas Dubois',
        avatarUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
      },
      rating: 4,
      date: '28 janvier 2023',
      content: "Très bon travail sur notre modélisation de produit. Quelques retouches ont été nécessaires, mais dans l'ensemble, le résultat est satisfaisant. Bonne communication tout au long du projet.",
      helpful: 5,
    },
    {
      id: 3,
      author: {
        name: 'Emma Leroy',
        avatarUrl: 'https://randomuser.me/api/portraits/women/68.jpg',
      },
      rating: 5,
      date: '10 décembre 2022',
      content: "Excellent travail ! Les animations 3D créées pour notre site web ont dépassé nos attentes. Professionnel, créatif et très réactif. Nous avons déjà prévu de collaborer sur d'autres projets.",
      helpful: 8,
    },
  ];

  const displayReviews = reviews.length > 0 ? reviews : mockReviews;
  const displayedReviews = showAllReviews ? displayReviews : displayReviews.slice(0, 3);
  const actualTotalReviews = totalReviews > 0 ? totalReviews : displayReviews.length;

  const toggleExpandReview = (reviewId: string | number) => {
    setExpandedReviews(prev => 
      prev.includes(reviewId) 
        ? prev.filter(id => id !== reviewId) 
        : [...prev, reviewId]
    );
  };

  const isReviewExpanded = (reviewId: string | number) => {
    return expandedReviews.includes(reviewId);
  };

  // Generate star rating display
  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star 
            key={star} 
            className={`h-4 w-4 ${star <= rating ? 'text-yellow-500 fill-yellow-500' : 'text-neutral-300'}`} 
          />
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">Avis ({actualTotalReviews})</h2>
          <div className="flex items-center">
            {renderStars(averageRating)}
            <span className="ml-2 font-medium">{averageRating.toFixed(1)}</span>
          </div>
        </div>
        
        {displayedReviews.length === 0 ? (
          <div className="text-center py-8 text-neutral-500">
            Aucun avis pour le moment.
          </div>
        ) : (
          <div className="space-y-6">
            {displayedReviews.map((review) => {
              const isExpanded = isReviewExpanded(review.id);
              const isLongContent = review.content.length > 200;
              
              return (
                <div key={review.id} className="border-b border-neutral-200 pb-6 last:border-b-0 last:pb-0">
                  <div className="flex items-start">
                    <Avatar 
                      size="md" 
                      src={review.author.avatarUrl} 
                      fallback={review.author.name.charAt(0)} 
                      className="mr-4"
                    />
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <h3 className="font-medium">{review.author.name}</h3>
                        <span className="text-sm text-neutral-500">{review.date}</span>
                      </div>
                      <div className="mt-1 mb-3">
                        {renderStars(review.rating)}
                      </div>
                      <div className="text-neutral-700">
                        {isLongContent && !isExpanded ? (
                          <>
                            <p>{review.content.substring(0, 200)}...</p>
                            <button 
                              className="text-primary-600 text-sm font-medium flex items-center mt-2"
                              onClick={() => toggleExpandReview(review.id)}
                            >
                              Voir plus <ChevronDown className="h-4 w-4 ml-1" />
                            </button>
                          </>
                        ) : (
                          <>
                            <p>{review.content}</p>
                            {isLongContent && (
                              <button 
                                className="text-primary-600 text-sm font-medium flex items-center mt-2"
                                onClick={() => toggleExpandReview(review.id)}
                              >
                                Voir moins <ChevronUp className="h-4 w-4 ml-1" />
                              </button>
                            )}
                          </>
                        )}
                      </div>
                      {review.helpful !== undefined && (
                        <div className="mt-3 flex items-center text-sm text-neutral-500">
                          <button className="flex items-center text-neutral-500 hover:text-primary-600">
                            <ThumbsUp className="h-4 w-4 mr-1" />
                            Utile ({review.helpful})
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
        
        {displayReviews.length > 3 && !showAllReviews && (
          <div className="mt-6 text-center">
            <Button 
              variant="outline"
              onClick={() => setShowAllReviews(true)}
            >
              Voir tous les avis ({displayReviews.length})
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewsSection;
