import React, { useState } from 'react';
import { X } from 'lucide-react';

interface PortfolioItem {
  id: string | number;
  title: string;
  description?: string;
  imageUrl: string;
  tags?: string[];
}

interface PortfolioGalleryProps {
  items: PortfolioItem[];
}

const PortfolioGallery: React.FC<PortfolioGalleryProps> = ({ 
  items = [] 
}) => {
  const [selectedItem, setSelectedItem] = useState<PortfolioItem | null>(null);

  // Mock data if no items are provided
  const mockItems: PortfolioItem[] = [
    {
      id: 1,
      title: 'Character Design',
      description: 'Character design for an animated short film. Created using <PERSON>len<PERSON> and Substance Painter.',
      imageUrl: 'https://mir-s3-cdn-cf.behance.net/project_modules/fs/96bef2182510483.652efbbf698a2.png',
      tags: ['Character', 'Blender', '3D Modeling']
    },
    {
      id: 2,
      title: 'Environment Design',
      description: 'Sci-fi environment created for a video game project.',
      imageUrl: 'https://mir-s3-cdn-cf.behance.net/project_modules/1400/56c6a5152228177.631a0d192ad00.png',
      tags: ['Environment', 'Sci-fi', 'Game Art']
    },
    {
      id: 3,
      title: 'Product Visualization',
      description: 'Realistic product visualization for a marketing campaign.',
      imageUrl: 'https://mir-s3-cdn-cf.behance.net/project_modules/fs/b07abb152228483.631a0df261254.png',
      tags: ['Product', 'Visualization', 'Commercial']
    },
    {
      id: 4,
      title: 'Architectural Visualization',
      description: 'Interior design visualization for a modern apartment.',
      imageUrl: 'https://mir-s3-cdn-cf.behance.net/project_modules/max_1200/d87997152315713.631b961cd3e4b.png',
      tags: ['Architecture', 'Interior', 'Visualization']
    }
  ];

  const displayItems = items.length > 0 ? items : mockItems;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-6">Portfolio</h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {displayItems.map((item) => (
            <div 
              key={item.id} 
              className="group relative overflow-hidden rounded-lg cursor-pointer"
              onClick={() => setSelectedItem(item)}
            >
              <div className="aspect-square overflow-hidden">
                <img 
                  src={item.imageUrl} 
                  alt={item.title} 
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                <h3 className="text-white font-medium">{item.title}</h3>
                {item.tags && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {item.tags.slice(0, 2).map((tag, index) => (
                      <span key={index} className="text-xs bg-white/20 text-white px-2 py-0.5 rounded-full">
                        {tag}
                      </span>
                    ))}
                    {item.tags.length > 2 && (
                      <span className="text-xs bg-white/20 text-white px-2 py-0.5 rounded-full">
                        +{item.tags.length - 2}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Modal for selected item */}
      {selectedItem && (
        <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
          <div className="relative bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <button 
              className="absolute top-4 right-4 text-neutral-500 hover:text-neutral-700 bg-white rounded-full p-1"
              onClick={() => setSelectedItem(null)}
            >
              <X className="h-6 w-6" />
            </button>
            
            <div className="p-6">
              <div className="mb-4">
                <h2 className="text-2xl font-semibold">{selectedItem.title}</h2>
                {selectedItem.description && (
                  <p className="text-neutral-600 mt-2">{selectedItem.description}</p>
                )}
                {selectedItem.tags && (
                  <div className="flex flex-wrap gap-1 mt-4">
                    {selectedItem.tags.map((tag, index) => (
                      <span key={index} className="text-xs bg-neutral-100 text-neutral-700 px-2 py-1 rounded-full">
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="mt-6">
                <img 
                  src={selectedItem.imageUrl} 
                  alt={selectedItem.title} 
                  className="w-full h-auto rounded-lg"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PortfolioGallery;
