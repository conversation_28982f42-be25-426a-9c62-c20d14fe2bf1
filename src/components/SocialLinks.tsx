import React, { useState } from "react";
import { API_BASE_URL } from '../config';
import { useProfile } from "./ProfileContext"; 

const SocialLinks = () => {
  // Récupérer les données du localStorage
  const { profile, setProfile } = useProfile();
  const storedProfile = JSON.parse(localStorage.getItem("userProfile") || "{}");
  const initialEducation = storedProfile?.profile_data?.education || "";
  const initialDiplomas = storedProfile?.profile_data?.diplomas || "";

  const [education, setEducation] = useState<string>(initialEducation);
  const [diplomas, setDiplomas] = useState<string>(initialDiplomas);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  // Mise à jour des champs
  const handleEducationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEducation(e.target.value);
  };

  const handleDiplomasChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDiplomas(e.target.value);
  };

  // Envoi des données à l'API
  const handleSubmit = async () => {
    setLoading(true);
    setMessage("");

    const token = localStorage.getItem("token");

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/profile/completion/education`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ education, diplomas }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        setMessage("✅ Formation mise à jour avec succès !");
        
        // Mettre à jour le localStorage
        const updatedProfile = {
          ...storedProfile,
          profile_data: {
            ...storedProfile.profile_data,
            education: education,
            diplomas: diplomas,
          },
        };

        const updateJiab = {
          ...profile,
          profile_data: {
            ...storedProfile.profile_data,
            education: education,
            diplomas: diplomas,
          },
        };
        localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
        setProfile(updateJiab);
      } else {
        setMessage("❌ Erreur : " + (data.message || "Impossible d'enregistrer la formation."));
      }
    } catch (error) {
      setMessage("❌ Erreur réseau, veuillez réessayer.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Formation & Diplôme</h2>

      {message && (
        <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
          {message}
        </div>
      )}

      {/* Diplôme le plus élevé */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-4">Votre diplôme le plus élevé</h3>
        <input
          type="text"
          placeholder="Diplôme"
          value={diplomas}
          onChange={handleDiplomasChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Études */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-4">Votre Étude</h3>
        <input
          type="text"
          placeholder="Étude"
          value={education}
          onChange={handleEducationChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Bouton pour envoyer les données */}
      <div className="mt-6">
        <button
          onClick={handleSubmit}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition"
          disabled={loading}
        >
          {loading ? "Envoi en cours..." : "Enregistrer ma formation"}
        </button>
      </div>
    </div>
  );
};

export default SocialLinks;
