import React, { useState, useEffect } from "react";
import SearchBar from "./SearchBar";

interface HeroProps {
  onSearch: (query: string, type: string) => void;
}

const Hero: React.FC<HeroProps> = ({ onSearch }) => {
  const [selectedType, setSelectedType] = useState("Services"); // default = Services
  const [searchValue, setSearchValue] = useState("");

  useEffect(() => {
    onSearch(searchValue, selectedType);
  }, [searchValue, selectedType]);

  const handleTypeClick = (type: string) => {
    setSelectedType(type);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.trim() === "") {
      setSearchValue("");
      onSearch("", selectedType);
    } else {
      setSearchValue(value);
      onSearch(value, selectedType);
    }
  };

  return (
    <div className="bg-white w-full px-[40px]">
      <div className="w-full mx-auto grid grid-cols-1 md:grid-cols-[1fr_auto] gap-10 items-end min-h-[500px]">
        {/* Left column - content */}
        <div className="flex flex-col justify-end text-left h-full">
          <h1
            className="text-3xl md:text-5xl font-normal mb-6"
            style={{
              fontSize: "48px",
              fontFamily: "'Inter', sans-serif",
              fontWeight: 600,
              lineHeight: "1em",
              letterSpacing: "-3px",
              color: "#0D0C22",
            }}
          >
            Connect your vision
            <br />
            with top 3D artists
          </h1>

          <p
            className="text-base md:text-lg mb-10 font-normal"
            style={{
              fontSize: "18px",
              fontFamily: "'Inter', sans-serif",
              fontWeight: 300,
              lineHeight: "18px",
              color: "#0D0C22",
            }}
          >
            Explore work from the most talented and accomplished 3D artists
            ready to
            <br />
            take on your next project.
          </p>

          <div className="flex items-center space-x-4 mb-6">
            <button
              type="button"
              className={`border-none rounded-full py-2 px-6 font-sans font-medium text-sm h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm
                ${
                  selectedType === "Services"
                    ? "bg-black text-white hover:bg-[#F5F5F5] hover:text-gray-500"
                    : "bg-[#F5F5F5] text-gray-500 hover:bg-black hover:text-white"
                }`}
              style={{ fontFamily: "Arial, sans-serif", fontSize: "16px" }}
              onClick={() => handleTypeClick("Services")}
            >
              Services
            </button>
            <button
              type="button"
              className={`border-none rounded-full py-2 px-6 font-sans font-medium text-sm h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm
                ${
                  selectedType === "3D Artiste"
                    ? "bg-black text-white hover:bg-[#F5F5F5] hover:text-gray-500"
                    : "bg-[#F5F5F5] text-gray-500 hover:bg-black hover:text-white"
                }`}
              style={{ fontFamily: "Arial, sans-serif", fontSize: "16px" }}
              onClick={() => handleTypeClick("3D Artiste")}
            >
              Artistes 3D
            </button>
          </div>

          <SearchBar
            width={640}
            height={60}
            iconSize={50}
            value={searchValue}
            onChange={handleSearchChange}
          />
        </div>

        {/* Right column - image */}
        <div className="flex justify-end w-[650px] h-[500px]">
          <img
            src="https://hi-3d.com/wp-content/uploads/2025/08/ependes-salle-sport-double-polyvalente-4-pavillon-du-rialet-visu-2-1024x855.jpg"
            alt="3D Art"
            className="rounded-lg shadow-lg object-cover w-full h-full"
          />
        </div>
      </div>
    </div>
  );
};

export default Hero;
