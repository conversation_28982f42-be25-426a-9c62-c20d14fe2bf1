import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import Header from "./Header";
import Footer from "./Footer";
import Gallery from "./Gallery";
import CategoryList from "./CategoryList";

interface GalleryItem {
  id: number;
  title: string;
  author: string;
  authorAvatar: string;
  isPro?: boolean;
  likes: number;
  views: string;
  image: string;
  category?: string;
}

const SearchGlobal = () => {
  const location = useLocation();
  const [searchResults, setSearchResults] = useState<GalleryItem[]>([]);
  const [filteredResults, setFilteredResults] = useState<GalleryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  const mockResults: GalleryItem[] = [
    {
      id: 1,
      title: "Projet Architectural Moderne",
      author: "<PERSON>",
      authorAvatar: "https://randomuser.me/api/portraits/men/1.jpg",
      isPro: true,
      likes: 42,
      views: "1.2k",
      image:
        "https://hi-3d.com/wp-content/uploads/2025/08/Filipo_1-1024x870.jpg",
      category: "Architecture 3D",
    },
    {
      id: 2,
      title: "Design Intérieur Contemporain",
      author: "Marie Martin",
      authorAvatar: "https://randomuser.me/api/portraits/women/1.jpg",
      isPro: true,
      likes: 35,
      views: "890",
      image: "https://hi-3d.com/wp-content/uploads/2025/08/project2.jpg",
      category: "Architecture 3D",
    },
    {
      id: 3,
      title: "Animation Personnage 3D",
      author: "Pierre Lambert",
      authorAvatar: "https://randomuser.me/api/portraits/men/22.jpg",
      isPro: true,
      likes: 28,
      views: "750",
      image: "https://hi-3d.com/wp-content/uploads/2025/08/project3.jpg",
      category: "Animation 3D",
    },
    {
      id: 4,
      title: "Modèle 3D Produit",
      author: "Sophie Leroy",
      authorAvatar: "https://randomuser.me/api/portraits/women/33.jpg",
      isPro: false,
      likes: 19,
      views: "520",
      image: "https://hi-3d.com/wp-content/uploads/2025/08/project4.jpg",
      category: "Produit 3D & Industriel",
    },
    {
      id: 5,
      title: "Environnement Jeu Vidéo",
      author: "Thomas Garnier",
      authorAvatar: "https://randomuser.me/api/portraits/men/45.jpg",
      isPro: true,
      likes: 87,
      views: "2.5k",
      image: "https://hi-3d.com/wp-content/uploads/2025/08/project5.jpg",
      category: "Jeux Vidéo 3D",
    },
    {
      id: 6,
      title: "Rendu 3D Photoréaliste",
      author: "Élodie Petit",
      authorAvatar: "https://randomuser.me/api/portraits/women/63.jpg",
      isPro: true,
      likes: 54,
      views: "1.1k",
      image: "https://hi-3d.com/wp-content/uploads/2025/08/project6.jpg",
      category: "Rendu 3D",
    },
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchResults(mockResults);
      setFilteredResults(mockResults);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [location]);

  useEffect(() => {
    if (!selectedCategory) {
      setFilteredResults(searchResults);
    } else {
      setFilteredResults(
        searchResults.filter((item) =>
          item.category?.toLowerCase().includes(selectedCategory.toLowerCase())
        )
      );
    }
  }, [selectedCategory, searchResults]);

  const handleDislike = (id: number) => {
    const updatedResults = searchResults.filter((item) => item.id !== id);
    setSearchResults(updatedResults);
    setFilteredResults(updatedResults);
  };

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
  };

  return (
    <>
      <Header />
      <div className="flex flex-col min-h-screen">
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="mb-8">
            <CategoryList
              onCategorySelect={handleCategorySelect}
              selectedCategory={selectedCategory}
            />
          </div>

          {isLoading ? (
            <div className="text-center py-20">
              <p>Chargement des résultats...</p>
            </div>
          ) : (
            <Gallery
              items={filteredResults}
              onDislike={handleDislike}
              marginBottom="4rem"
            />
          )}
        </main>
      </div>
      <Footer />
    </>
  );
};

export default SearchGlobal;
