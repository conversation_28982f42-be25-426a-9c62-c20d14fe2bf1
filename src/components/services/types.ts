export interface ServiceOffer {
  id: number;
  user_id?: number; // Ajout de user_id
  title: string;
  description: string;
  price: number | string;
  price_unit: string;
  execution_time: string;
  associated_project?: string;
  concepts: string;
  revisions: string;
  categories: string[];
  is_private: boolean;
  status: string;
  created_at: string;
  updated_at: string;
  user?: {
    id: number;
    first_name: string;
    last_name: string;
    profile_picture_path?: string;
  } | null;
  files?: Array<{
    path: string;
    original_name: string;
    mime_type: string;
    size: number;
  }>;
  file_urls?:string[];
  imageUrl?: string; // Propriété calculée à partir de files[0].path
  likes?: number;
  views?: number;
  rating?: number;
  category?: string;
  client_name?: string;
  professional_name?: string;
  professional_id?: number;
  date_create?: string;
  avatar?: string;
}

export interface ServiceOfferFormData {
  title: string;
  description: string;
  price: number | string;
  price_unit: string;
  execution_time: string;
  associated_project?: string;
  concepts: string;
  revisions: string;
  categories: string[];
  is_private: boolean;
  status: string;
  image?: File | null;
  images?:File[]|[];
}
