import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Filter, Briefcase, Star, Clock, DollarSign, Tag } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import ServiceOfferCard from './ServiceOfferCard';
import type { ServiceOffer } from './types';

const ClientServicesExplore: React.FC = () => {
  const navigate = useNavigate();
  const [services, setServices] = useState<ServiceOffer[]>([]);
  const [filteredServices, setFilteredServices] = useState<ServiceOffer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 5000]);
  const [minRating, setMinRating] = useState(0);
  const [executionTimeFilter, setExecutionTimeFilter] = useState('all');

  // Options pour les catégories
  const categoryOptions = [
    { value: 'all', label: 'Toutes les catégories' },
    { value: 'modeling', label: 'Modélisation 3D' },
    { value: 'animation', label: 'Animation' },
    { value: 'rigging', label: 'Rigging' },
    { value: 'texturing', label: 'Texturing' },
    { value: 'rendering', label: 'Rendering' },
    { value: 'vfx', label: 'VFX' },
    { value: 'game_art', label: 'Game Art' },
    { value: 'architectural', label: 'Architecture 3D' },
    { value: 'product', label: 'Produit 3D' },
    { value: 'character', label: 'Personnage 3D' },
    { value: 'environment', label: 'Environnement 3D' },
  ];

  // Options pour le temps d'exécution
  const executionTimeOptions = [
    { value: 'all', label: 'Tous les délais' },
    { value: '1-3', label: '1-3 jours' },
    { value: '3-7', label: '3-7 jours' },
    { value: '7-14', label: '1-2 semaines' },
    { value: '14-30', label: '2-4 semaines' },
    { value: '30+', label: 'Plus d\'un mois' },
  ];

  // Récupérer le token d'authentification
  const token = localStorage.getItem('token');

  // Récupérer les services depuis l'API
  useEffect(() => {
    const fetchServices = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/service-offers`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Erreur HTTP : ${response.status}`);
        }

        const data = await response.json();
        console.log('Services data:', data);

        // Formater les données pour correspondre à notre structure
        const formattedServices = data.map((service: any) => ({
          avatar : service.user.professional_details.avatar? `${API_BASE_URL}${service.user.professional_details.avatar}` : "https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop",
          id: service.id,
          title: service.title,
          description: service.description,
          price: service.price,
          execution_time: service.execution_time,
          concepts: service.concepts,
          revisions: service.revisions,
          categories: Array.isArray(service.categories) ? service.categories :
                     (typeof service.categories === 'string' ?
                      (() => {
                        try {
                          return JSON.parse(service.categories || '[]');
                        } catch (e) {
                          console.error('Erreur de parsing des catégories:', e);
                          return [];
                        }
                      })() : []),
          is_private: service.is_private,
          status: service.status,
          created_at: service.created_at,
          updated_at: service.updated_at,
          user: service.user,
          files: service.files || [],
          imageUrl: service.files && service.files.length > 0
            ? `${API_BASE_URL}/storage/${service.files[0].path}`
            : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          image_url : Array.isArray(service.files) && service.files.length > 0
                  ? `${API_BASE_URL}/storage/${service.files[0].path}`
                  : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
          file_urls: Array.isArray(service.files)
            ? service.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
            : [],
          likes: service.likes || 0,
          views: service.views || 0,
          rating: service.rating || 0,
          category: service.categories ? service.categories.join(" - ") : "",
          client_name: service.execution_time,
          professional_name: service.user.first_name + ' ' + service.user.last_name,
          professional_id: service.user.professional_details.id || 1,
          date_create: service.created_at,
          user_id: service.user.id,
        }));

        setServices(formattedServices);
        setFilteredServices(formattedServices);
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les services');
      } finally {
        setIsLoading(false);
      }
    };

    fetchServices();
  }, [token]);

  // Filtrer les services en fonction des critères
  useEffect(() => {
    const applyFilters = () => {
      const filtered = services.filter(service => {
        // Filtre par recherche
        const matchesSearch = service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                             service.description.toLowerCase().includes(searchQuery.toLowerCase());

        // Filtre par catégorie
        const matchesCategory = categoryFilter === 'all' ||
                               (service.categories && service.categories.includes(categoryFilter));

        // Filtre par prix
        const servicePrice = typeof service.price === 'string' ? parseFloat(service.price) : service.price;
        const matchesPrice = servicePrice >= priceRange[0] && servicePrice <= priceRange[1];

        // Filtre par note
        const serviceRating = service.rating || 0; // Utiliser 0 comme valeur par défaut si rating est undefined
        const matchesRating = serviceRating >= minRating;

        // Filtre par temps d'exécution
        let matchesExecutionTime = true;
        if (executionTimeFilter !== 'all') {
          const [min, max] = executionTimeFilter.split('-').map(Number);
          const executionDays = parseExecutionTime(service.execution_time);

          if (max) {
            matchesExecutionTime = executionDays >= min && executionDays <= max;
          } else {
            // Pour les options comme "30+"
            matchesExecutionTime = executionDays >= min;
          }
        }

        return matchesSearch && matchesCategory && matchesPrice &&
               matchesRating && matchesExecutionTime;
      });

      setFilteredServices(filtered);
    };

    applyFilters();
  }, [searchQuery, categoryFilter, priceRange, minRating, executionTimeFilter, services]);

  // Fonction pour extraire le nombre de jours à partir du temps d'exécution
  const parseExecutionTime = (executionTime: string): number => {
    if (!executionTime) return 0;

    // Extraire les chiffres de la chaîne
    const match = executionTime.match(/\d+/);
    if (match) {
      return parseInt(match[0], 10);
    }

    return 0;
  };

  // Gérer le clic sur un service
  // const handleServiceClick = (serviceId: number) => {
  const handleServiceClick = (service: ServiceOffer) => {
    // navigate(`/services/${serviceId}`);
    navigate('/details-search', {
      state: { service }
    });
  };

  // Gérer la recherche
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // La recherche est déjà gérée par l'effet useEffect
  };

  // Réinitialiser les filtres
  const handleResetFilters = () => {
    setSearchQuery('');
    setCategoryFilter('all');
    setPriceRange([0, 5000]);
    setMinRating(0);
    setExecutionTimeFilter('all');
  };

  return (
    <DashboardLayout
      title="Explorer les services"
      subtitle="Découvrez les services proposés par nos professionnels"
    >
      {/* Barre de recherche et filtres */}
      <div className="mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Barre de recherche */}
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Rechercher un service..."
                className="w-full px-4 py-2 pl-10 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400" />
            </div>
          </form>

          {/* Bouton de filtres */}
          <Button
            variant={showFilters ? "primary" : "outline"}
            leftIcon={<Filter className="h-5 w-5" />}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filtres
          </Button>
        </div>

        {/* Panneau de filtres */}
        {showFilters && (
          <div className="mt-4 p-4 bg-white rounded-lg border border-neutral-200 shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Filtre par catégorie */}
              <div>
                <label htmlFor="category-filter" className="block text-sm font-medium text-neutral-700 mb-1">Catégorie</label>
                <select
                  id="category-filter"
                  className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  aria-label="Filtrer par catégorie"
                >
                  {categoryOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              {/* Filtre par prix */}
              <div>
                <label htmlFor="price-range" className="block text-sm font-medium text-neutral-700 mb-1">Prix maximum</label>
                <input
                  id="price-range"
                  type="range"
                  min="0"
                  max="5000"
                  step="100"
                  value={priceRange[1]}
                  onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                  className="w-full"
                  aria-label="Définir le prix maximum"
                />
                <div className="flex justify-between text-xs text-neutral-500">
                  <span>{priceRange[0]} €</span>
                  <span>{priceRange[1]} €</span>
                </div>
              </div>

              {/* Filtre par note */}
              <div>
                <label id="rating-label" className="block text-sm font-medium text-neutral-700 mb-1">Note minimum</label>
                <div className="flex items-center" role="group" aria-labelledby="rating-label">
                  {[1, 2, 3, 4, 5].map(rating => (
                    <button
                      key={rating}
                      type="button"
                      onClick={() => setMinRating(rating)}
                      className={`h-8 w-8 flex items-center justify-center rounded-full mr-1 ${
                        rating <= minRating ? 'bg-yellow-100 text-yellow-600' : 'bg-neutral-100 text-neutral-400'
                      }`}
                      aria-label={`${rating} étoile${rating > 1 ? 's' : ''}`}
                      aria-pressed={rating <= minRating ? 'true' : 'false'}
                    >
                      <Star className="h-5 w-5" fill={rating <= minRating ? 'currentColor' : 'none'} />
                    </button>
                  ))}
                  <span className="ml-2 text-sm text-neutral-500">{minRating > 0 ? `${minRating}+ étoiles` : 'Toutes les notes'}</span>
                </div>
              </div>

              {/* Filtre par temps d'exécution */}
              <div>
                <label htmlFor="execution-time-filter" className="block text-sm font-medium text-neutral-700 mb-1">Temps d'exécution</label>
                <select
                  id="execution-time-filter"
                  className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={executionTimeFilter}
                  onChange={(e) => setExecutionTimeFilter(e.target.value)}
                  aria-label="Filtrer par temps d'exécution"
                >
                  {executionTimeOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Bouton de réinitialisation */}
            <div className="mt-4 flex justify-end">
              <Button
                variant="outline"
                onClick={handleResetFilters}
              >
                Réinitialiser les filtres
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Afficher les erreurs */}
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {/* Contenu principal */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : (
        <>
          {/* Nombre de résultats */}
          <div className="mb-4">
            <p className="text-neutral-500">
              {filteredServices.length} service{filteredServices.length !== 1 ? 's' : ''} trouvé{filteredServices.length !== 1 ? 's' : ''}
            </p>
          </div>

          {/* Liste des services */}
          {filteredServices.length === 0 ? (
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
              <div className="mx-auto w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mb-4">
                <Briefcase className="h-8 w-8 text-neutral-400" />
              </div>
              <h3 className="text-lg font-medium text-neutral-700 mb-2">Aucun service trouvé</h3>
              <p className="text-neutral-500 mb-6">Essayez de modifier vos filtres ou votre recherche</p>
              <Button
                variant="outline"
                onClick={handleResetFilters}
              >
                Réinitialiser les filtres
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredServices.map(service => (
                <ServiceOfferCard
                  key={service.id}
                  service={service}
                  onClick={() => handleServiceClick(service)}
                //   onClick={() => {
                //   navigate('/details-search', {
                //     state: { service }
                //   });
                // }}
                />
              ))}
            </div>
          )}
        </>
      )}
    </DashboardLayout>
  );
};

export default ClientServicesExplore;
