import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Star, ArrowLeft } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import Alert from '../ui/Alert';

interface ServiceDetails {
  id: number;
  title: string;
  professional_name: string;
  professional_id: number;
  imageUrl?: string;
}

const ServiceReviewForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [serviceDetails, setServiceDetails] = useState<ServiceDetails | null>(null);

  const token = localStorage.getItem('token');

  // Récupérer les détails du service
  useEffect(() => {
    const fetchServiceDetails = async () => {
      if (!token || !id) return;

      setIsLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/service-offers/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des détails du service');
        }

        const data = await response.json();

        setServiceDetails({
          id: data.id,
          title: data.title,
          professional_name: `${data.user.first_name} ${data.user.last_name}`,
          professional_id: data.user.id,
          imageUrl: data.files && data.files.length > 0 ? data.files[0] : undefined,
        });

        // Vérifier si l'utilisateur a déjà laissé un avis
        const reviewResponse = await fetch(`${API_BASE_URL}/api/service-offers/${id}/reviews/check`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (reviewResponse.ok) {
          const reviewData = await reviewResponse.json();

          if (reviewData.has_reviewed) {
            setError('Vous avez déjà laissé un avis pour ce service');
          }
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les détails du service');
      } finally {
        setIsLoading(false);
      }
    };

    fetchServiceDetails();
  }, [id, token]);

  // Soumettre l'avis
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === 0) {
      setError('Veuillez sélectionner une note');
      return;
    }

    if (!comment.trim()) {
      setError('Veuillez laisser un commentaire');
      return;
    }

    if (!token || !id) {
      setError('Vous devez être connecté pour laisser un avis');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/api/service-offers/${id}/reviews`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rating,
          comment,
        }),
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la soumission de l\'avis');
      }

      setSuccess('Votre avis a été soumis avec succès');

      // Rediriger vers la page du service après 2 secondes
      setTimeout(() => {
        navigate(`/services/${id}`);
      }, 2000);
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de soumettre l\'avis');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Laisser un avis"
      subtitle={serviceDetails ? `Pour le service: ${serviceDetails.title}` : undefined}
      actions={
        <Button
          variant="outline"
          leftIcon={<ArrowLeft className="h-5 w-5" />}
          onClick={() => navigate(`/services/${id}`)}
        >
          Retour au service
        </Button>
      }
    >
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {success && (
        <Alert
          type="success"
          title="Succès"
          onClose={() => setSuccess(null)}
          className="mb-6"
        >
          {success}
        </Alert>
      )}

      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
        {serviceDetails && (
          <div className="px-6 py-4 bg-neutral-50 border-b border-neutral-200">
            <div className="flex items-center">
              {serviceDetails.imageUrl && (
                <img
                  src={serviceDetails.imageUrl}
                  alt={serviceDetails.title}
                  className="h-16 w-16 object-cover rounded-md mr-4"
                />
              )}
              <div>
                <h3 className="text-lg font-semibold text-neutral-900">{serviceDetails.title}</h3>
                <p className="text-neutral-600">
                  Par {serviceDetails.professional_name}
                </p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Note */}
          <div>
            <label className="block text-lg font-medium text-neutral-900 mb-2">
              Votre note
            </label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoverRating(star)}
                  onMouseLeave={() => setHoverRating(0)}
                  className="focus:outline-none"
                  title={`Noter ${star} étoile${star > 1 ? 's' : ''}`}
                  aria-label={`Noter ${star} étoile${star > 1 ? 's' : ''}`}
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= (hoverRating || rating)
                        ? 'text-yellow-500 fill-yellow-500'
                        : 'text-neutral-300'
                    }`}
                  />
                </button>
              ))}
              <span className="ml-2 text-neutral-600">
                {rating > 0 ? `${rating}/5` : 'Sélectionnez une note'}
              </span>
            </div>
          </div>

          {/* Commentaire */}
          <div>
            <label htmlFor="comment" className="block text-lg font-medium text-neutral-900 mb-2">
              Votre commentaire
            </label>
            <textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Partagez votre expérience avec ce service..."
              className="w-full border border-neutral-300 rounded-md p-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              rows={6}
              required
            ></textarea>
            <p className="mt-1 text-sm text-neutral-500">
              Votre avis aidera d'autres utilisateurs à prendre une décision éclairée.
            </p>
          </div>

          {/* Conseils pour un bon avis */}
          <div className="bg-neutral-50 rounded-md p-4 border border-neutral-200">
            <h4 className="text-sm font-medium text-neutral-900 mb-2">Conseils pour un avis utile</h4>
            <ul className="text-sm text-neutral-600 space-y-1 list-disc pl-5">
              <li>Soyez spécifique sur ce que vous avez aimé ou non</li>
              <li>Mentionnez la qualité du travail et le respect des délais</li>
              <li>Parlez de la communication avec le professionnel</li>
              <li>Évitez les commentaires offensants ou inappropriés</li>
            </ul>
          </div>

          {/* Boutons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(`/services/${id}`)}
              disabled={isSubmitting}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={isSubmitting}
              disabled={rating === 0 || !comment.trim() || isSubmitting}
              style={{ backgroundColor: '#2980b9', color: 'white' }}
            >
              Soumettre l'avis
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default ServiceReviewForm;
