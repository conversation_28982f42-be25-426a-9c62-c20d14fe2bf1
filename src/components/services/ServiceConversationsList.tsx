import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { MessageSquare, Search, User, Clock, Calendar, Check, X } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import Avatar from '../ui/Avatar';

interface Conversation {
  id: number;
  service_id?: number;
  service_title?: string;
  other_user: {
    id: number;
    first_name: string;
    last_name: string;
    profile_picture_path?: string;
  };
  last_message?: {
    content: string;
    created_at: string;
    is_read: boolean;
    sender_id: number;
  };
  unread_count: number;
  status: 'active' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

const ServiceConversationsList: React.FC = () => {
  const navigate = useNavigate();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [filteredConversations, setFilteredConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const token = localStorage.getItem('token');
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');

  // Charger les conversations
  useEffect(() => {
    const fetchConversations = async () => {
      if (!token) return;

      setIsLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/conversations`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des conversations');
        }

        const data = await response.json();
        setConversations(data);
        setFilteredConversations(data);
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les conversations');
      } finally {
        setIsLoading(false);
      }
    };

    fetchConversations();

    // Mettre en place un polling pour récupérer les nouvelles conversations
    const intervalId = setInterval(() => {
      fetchConversations();
    }, 30000); // Toutes les 30 secondes

    return () => clearInterval(intervalId);
  }, [token]);

  // Filtrer les conversations
  useEffect(() => {
    if (!conversations) return;

    const filtered = conversations.filter(conversation => {
      // Filtre de recherche
      const matchesSearch =
        (conversation.other_user.first_name + ' ' + conversation.other_user.last_name).toLowerCase().includes(searchQuery.toLowerCase()) ||
        (conversation.service_title || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
        (conversation.last_message?.content || '').toLowerCase().includes(searchQuery.toLowerCase());

      // Filtre de statut
      const matchesStatus = statusFilter === 'all' || conversation.status === statusFilter;

      return matchesSearch && matchesStatus;
    });

    setFilteredConversations(filtered);
  }, [searchQuery, statusFilter, conversations]);

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);

    // Si c'est aujourd'hui, afficher l'heure
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
    }

    // Si c'est hier, afficher "Hier"
    if (date.toDateString() === yesterday.toDateString()) {
      return 'Hier';
    }

    // Si c'est cette année, afficher le jour et le mois
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' });
    }

    // Sinon, afficher la date complète
    return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short', year: 'numeric' });
  };

  // Tronquer le texte
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Messages"
      subtitle="Gérez vos conversations avec les clients et professionnels"
    >
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
        {/* En-tête avec recherche et filtres */}
        <div className="px-6 py-4 border-b border-neutral-200">
          <div className="flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-4">
            {/* Recherche */}
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-neutral-400" />
              </div>
              <input
                type="text"
                placeholder="Rechercher dans les messages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Filtre de statut */}
            <div className="w-full md:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                title="Filtrer par statut"
                aria-label="Filtrer par statut"
              >
                <option value="all">Tous les statuts</option>
                <option value="active">Actifs</option>
                <option value="completed">Terminés</option>
                <option value="cancelled">Annulés</option>
              </select>
            </div>
          </div>
        </div>

        {/* Liste des conversations */}
        <div className="divide-y divide-neutral-200">
          {filteredConversations.length === 0 ? (
            <div className="text-center py-12">
              <div className="mx-auto w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mb-4">
                <MessageSquare className="h-8 w-8 text-neutral-400" />
              </div>
              <h3 className="text-lg font-medium text-neutral-700 mb-1">Aucune conversation</h3>
              <p className="text-neutral-500 mb-4">Vous n'avez pas encore de conversations</p>
            </div>
          ) : (
            filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                className={`p-4 hover:bg-neutral-50 cursor-pointer transition-colors ${
                  conversation.unread_count > 0 ? 'bg-blue-50 hover:bg-blue-100' : ''
                }`}
                onClick={() => navigate(`/dashboard/messages/${conversation.id}`)}
              >
                <div className="flex items-start">
                  <Avatar
                    size="md"
                    src={conversation.other_user.profile_picture_path}
                    fallback={(conversation.other_user.first_name || 'U').charAt(0)}
                    className="mr-3 flex-shrink-0"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-sm font-medium text-neutral-900">
                          {conversation.other_user.first_name} {conversation.other_user.last_name}
                        </h4>
                        {conversation.service_title && (
                          <p className="text-xs text-neutral-500">
                            Re: {conversation.service_title}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center">
                        <span className="text-xs text-neutral-500">
                          {conversation.last_message ? formatDate(conversation.last_message.created_at) : formatDate(conversation.created_at)}
                        </span>
                        {conversation.status === 'completed' && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            <Check className="h-3 w-3 mr-1" />
                            Terminé
                          </span>
                        )}
                        {conversation.status === 'cancelled' && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            <X className="h-3 w-3 mr-1" />
                            Annulé
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="mt-1 flex justify-between items-center">
                      <p className={`text-sm ${
                        conversation.unread_count > 0 && conversation.last_message?.sender_id !== currentUser.id
                          ? 'font-medium text-neutral-900'
                          : 'text-neutral-600'
                      } truncate`}>
                        {conversation.last_message
                          ? truncateText(conversation.last_message.content, 60)
                          : 'Aucun message'}
                      </p>
                      {conversation.unread_count > 0 && conversation.last_message?.sender_id !== currentUser.id && (
                        <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary-600 text-white text-xs font-medium">
                          {conversation.unread_count}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

// Utiliser le MessageSquare importé de lucide-react

export default ServiceConversationsList;
