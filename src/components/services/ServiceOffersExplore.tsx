import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Search, Filter, Briefcase, Star, Clock, DollarSign, Tag } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import Layout from '../layout/Layout';
import Container from '../layout/Container';
import Button from '../ui/Button';
import ServiceOfferCard from './ServiceOfferCard';
import type { ServiceOffer } from './types';
import { MAIN_CATEGORIES, ARCHITECTURE_3D_CATEGORIES, getCategoryOptions } from '../../data/categories';

const ServiceOffersExplore: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [services, setServices] = useState<ServiceOffer[]>([]);
  const [filteredServices, setFilteredServices] = useState<ServiceOffer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Récupérer le paramètre professional de l'URL
  const searchParams = new URLSearchParams(location.search);
  const professionalIdParam = searchParams.get('professional');

  // Filtres
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 5000]);
  const [minRating, setMinRating] = useState(0);
  const [executionTimeFilter, setExecutionTimeFilter] = useState('all');

  // Options pour les catégories
  const allCategoryOptions = [
    { value: 'all', label: 'Toutes les catégories' },
    ...getCategoryOptions(MAIN_CATEGORIES),
  ];

  // Options pour les sous-catégories d'Architecture 3D
  const architecturalSubcategoryOptions = getCategoryOptions(ARCHITECTURE_3D_CATEGORIES);

  // Options pour le temps d'exécution
  const executionTimeOptions = [
    { value: 'all', label: 'Tous les délais' },
    { value: 'Moins d\'une semaine', label: 'Moins d\'une semaine' },
    { value: 'D\'ici 1 à 2 semaines', label: 'D\'ici 1 à 2 semaines' },
    { value: 'Dans le mois', label: 'Dans le mois' },
    { value: 'Dans les 2 mois', label: 'Dans les 2 mois' },
    { value: 'Dans les 3 mois', label: 'Dans les 3 mois' },
  ];

  // Récupérer le token d'authentification
  const token = localStorage.getItem('token');

  // Charger les services au chargement du composant
  useEffect(() => {
    const fetchServices = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/service-offers`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : '',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des services');
        }

        const data = await response.json();
        const apiServices = data.data || [];

        // Transformer les données de l'API
        const formattedServices = apiServices
          .filter((service: any) => service.status === 'published')
          .map((service: any) => ({
            id: service.id,
            title: service.title,
            description: service.description,
            price: service.price,
            execution_time: service.execution_time,
            concepts: service.concepts,
            revisions: service.revisions,
            categories: typeof service.categories === 'string' ? JSON.parse(service.categories) : (service.categories || []),
            is_private: false,
            status: 'published',
            created_at: service.created_at,
            updated_at: service.updated_at,
            user: service.user ? {
              id: service.user.id,
              first_name: service.user.first_name,
              last_name: service.user.last_name,
              profile_picture_path: service.user.profile_picture_path,
            } : null,
            files: service.files || [],
            imageUrl: service.files && service.files.length > 0
              ? `${API_BASE_URL}/storage/${service.files[0].path}`
              : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            likes: service.likes || 0,
            views: service.views || 0,
            rating: service.rating || 0,
          }));

        setServices(formattedServices);
        setFilteredServices(formattedServices);
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les services');
      } finally {
        setIsLoading(false);
      }
    };

    fetchServices();
  }, [token]);

  // Appliquer les filtres lorsqu'ils changent
  useEffect(() => {
    const applyFilters = () => {
      const filtered = services.filter(service => {
        // Filtre de recherche
        const matchesSearch =
          service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          service.description.toLowerCase().includes(searchQuery.toLowerCase());

        // Filtre de catégorie
        const matchesCategory = categoryFilter === 'all' ||
          (service.categories && service.categories.includes(categoryFilter));

        // Filtre de prix
        const matchesPrice =
          service.price >= priceRange[0] && service.price <= priceRange[1];

        // Filtre d'évaluation
        const matchesRating = (service.rating || 0) >= minRating;

        // Filtre de temps d'exécution
        const matchesExecutionTime = executionTimeFilter === 'all' ||
          service.execution_time === executionTimeFilter;

        // Filtre par professionnel (si le paramètre est présent dans l'URL)
        const matchesProfessional = !professionalIdParam ||
          (service.user && service.user.id.toString() === professionalIdParam);

        // Log pour déboguer le filtre par professionnel
        if (professionalIdParam) {
          console.log("Filtering by professional ID:", professionalIdParam);
          console.log("Service user ID:", service.user?.id);
          console.log("Matches professional:", matchesProfessional);
        }

        return matchesSearch && matchesCategory && matchesPrice &&
               matchesRating && matchesExecutionTime && matchesProfessional;
      });

      setFilteredServices(filtered);
    };

    applyFilters();
  }, [searchQuery, categoryFilter, priceRange, minRating, executionTimeFilter, services, professionalIdParam]);

  // Gérer le clic sur un service
  const handleServiceClick = (serviceId: number) => {
    navigate(`/services/${serviceId}`);
  };

  // Titre et sous-titre personnalisés en fonction du paramètre professional
  const getPageTitle = () => {
    if (professionalIdParam) {
      // Si on filtre par professionnel, afficher un titre personnalisé
      const professionalName = services.find(service =>
        service.user && service.user.id.toString() === professionalIdParam
      )?.user?.first_name || "ce professionnel";

      return {
        title: `Services proposés par ${professionalName}`,
        subtitle: `Découvrez tous les services offerts par ce professionnel`
      };
    }

    // Titre par défaut
    return {
      title: "Découvrez nos services 3D",
      subtitle: "Trouvez le service parfait pour votre projet parmi notre sélection de professionnels qualifiés"
    };
  };

  const pageTitle = getPageTitle();

  return (
    <Layout>
      <Container className="py-12">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">{pageTitle.title}</h1>
          <p className="text-lg text-neutral-600">
            {pageTitle.subtitle}
          </p>
        </div>

        {/* Barre de recherche et filtres */}
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden mb-8">
          <div className="p-4">
            <div className="flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-4">
              {/* Recherche */}
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-neutral-400" />
                </div>
                <input
                  type="text"
                  placeholder="Rechercher un service..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              {/* Bouton de filtre */}
              <Button
                variant="outline"
                leftIcon={<Filter className="h-5 w-5" />}
                onClick={() => setShowFilters(!showFilters)}
                className="md:w-auto"
              >
                Filtres
                {showFilters && <span className="ml-1 text-xs">▲</span>}
                {!showFilters && <span className="ml-1 text-xs">▼</span>}
              </Button>
            </div>

            {/* Filtres étendus */}
            {showFilters && (
              <div className="mt-4 pt-4 border-t border-neutral-200 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Filtre de catégorie */}
                <div>
                  <label htmlFor="categoryFilter" className="block text-sm font-medium text-neutral-700 mb-1">
                    Catégorie
                  </label>
                  <select
                    id="categoryFilter"
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="block w-full pl-3 pr-10 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    {[
                      { value: 'all', label: 'Toutes les catégories' },
                      ...getCategoryOptions(MAIN_CATEGORIES),
                      ...getCategoryOptions(ARCHITECTURE_3D_CATEGORIES)
                    ].map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Filtre de prix */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Prix (€)
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      min="0"
                      value={priceRange[0]}
                      onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                      className="block w-full pl-3 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Min"
                    />
                    <span>-</span>
                    <input
                      type="number"
                      min={priceRange[0]}
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                      className="block w-full pl-3 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Max"
                    />
                  </div>
                </div>

                {/* Filtre d'évaluation */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Évaluation minimale
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="0"
                      max="5"
                      step="0.5"
                      value={minRating}
                      onChange={(e) => setMinRating(parseFloat(e.target.value))}
                      className="w-full"
                      title="Évaluation minimale"
                      aria-label="Évaluation minimale"
                    />
                    <div className="flex items-center">
                      <span className="mr-1">{minRating}</span>
                      <Star className="h-4 w-4 text-yellow-500" />
                    </div>
                  </div>
                </div>

                {/* Filtre de temps d'exécution */}
                <div>
                  <label htmlFor="executionTimeFilter" className="block text-sm font-medium text-neutral-700 mb-1">
                    Délai d'exécution
                  </label>
                  <select
                    id="executionTimeFilter"
                    value={executionTimeFilter}
                    onChange={(e) => setExecutionTimeFilter(e.target.value)}
                    className="block w-full pl-3 pr-10 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    {executionTimeOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* État de chargement */}
        {isLoading && (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
        )}

        {/* Message d'erreur */}
        {error && !isLoading && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
            <p>{error}</p>
          </div>
        )}

        {/* Résultats de recherche */}
        {!isLoading && !error && (
          <>
            {/* Nombre de résultats */}
            <div className="mb-6">
              <p className="text-neutral-600">
                {filteredServices.length} service{filteredServices.length !== 1 ? 's' : ''} trouvé{filteredServices.length !== 1 ? 's' : ''}
              </p>
            </div>

            {/* Liste des services */}
            {filteredServices.length === 0 ? (
              <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
                <div className="mx-auto w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mb-4">
                  <Briefcase className="h-8 w-8 text-neutral-400" />
                </div>
                <h3 className="text-lg font-medium text-neutral-700 mb-2">Aucun service trouvé</h3>
                <p className="text-neutral-500 mb-6">Essayez de modifier vos filtres ou votre recherche</p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setCategoryFilter('all');
                    setPriceRange([0, 5000]);
                    setMinRating(0);
                    setExecutionTimeFilter('all');
                  }}
                >
                  Réinitialiser les filtres
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredServices.map(service => (
                  <ServiceOfferCard
                    key={service.id}
                    service={service}
                    onClick={() => handleServiceClick(service.id)}
                  />
                ))}
              </div>
            )}
          </>
        )}
      </Container>
    </Layout>
  );
};

export default ServiceOffersExplore;
