import React, { useState, useEffect } from 'react';
import { Briefcase, DollarSign, Clock, Image, Tag, Check, Eye, EyeOff } from 'lucide-react';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';
import FormTextarea from '../ui/FormTextarea';
import FormSelect from '../ui/FormSelect';
import FormMultiSelect from '../ui/FormMultiSelect';
import Checkbox from '../ui/Checkbox';
import type { ServiceOffer, ServiceOfferFormData } from './types';
import { MAIN_CATEGORIES, ARCHITECTURE_3D_CATEGORIES, getCategoryOptions } from '../../data/categories';

interface ServiceOfferFormProps {
  initialData?: ServiceOffer;
  onSubmit: (data: ServiceOfferFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ServiceOfferForm: React.FC<ServiceOfferFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const isEditMode = !!initialData?.id;

  // Options pour les catégories principales
  const categoryOptions = getCategoryOptions(MAIN_CATEGORIES);

  // Options pour le temps d'exécution
  const executionTimeOptions = [
    { value: '', label: 'Sélectionnée une période' },
    { value: 'Moins de une semaine', label: 'Moins de une semaine' },
    { value: 'Une à deux semaine', label: 'Une à deux semaine' },
    { value: 'Un mois', label: 'Un mois' },
    { value: 'Deux mois', label: 'Deux mois' },
    { value: 'Trois mois', label: 'Trois mois' },
  ];

  // Form state
  const [formData, setFormData] = useState<ServiceOfferFormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    price: initialData?.price || '',
    price_unit: initialData?.price_unit || 'par projet',
    execution_time: initialData?.execution_time || '',
    associated_project: initialData?.associated_project || '',
    concepts: initialData?.concepts || '',
    revisions: initialData?.revisions || '',
    categories: initialData?.categories || [],
    is_private: initialData?.is_private ?? false,
    status: initialData?.status || 'published',
    image: null,
    images: [],
  });

  // Image preview
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (initialData?.file_urls && Array.isArray(initialData.file_urls)) {
      setImagePreviews(initialData.file_urls);
      setFormData(prev => ({ ...prev, images: [] })); // vide pour les nouveaux ajouts
    }
  }, [initialData]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle multi-select changes
  const handleMultiSelectChange = (name: string, values: string[]): void => {
    setFormData(prev => ({ ...prev, [name]: values }));
  };

  // Handle image change
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const fileList = Array.from(files);
      setFormData(prev => ({ ...prev, images: fileList }));

      // Créer des URLs de prévisualisation pour toutes les images
      const previews = fileList
        .filter(file => file.type.startsWith('image/'))
        .map(file => URL.createObjectURL(file));
      setImagePreviews(previews);
    }
  };

  // Form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Le titre est requis';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est requise';
    }

    if (!formData.concepts) {
      newErrors.concepts = 'Le nombre de concepts est requis';
    }

    if (!formData.revisions) {
      newErrors.revisions = 'Le nombre de révisions est requis';
    }

    if (!formData.price) {
      newErrors.price = 'Le prix est requis';
    }

    if (!formData.execution_time) {
      newErrors.execution_time = 'Le temps d\'exécution est requis';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Submit form
    onSubmit(formData);
  };

  return (
    <div className="w-full bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-neutral-200">
        <h2 className="text-xl font-semibold text-neutral-900">
          {isEditMode ? 'Modifier le service' : 'Créer un nouveau service'}
        </h2>
        <p className="text-neutral-600 text-sm mt-1">
          {isEditMode
            ? 'Mettez à jour les détails de votre service'
            : 'Remplissez les détails de votre service pour le proposer aux clients'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Col-1 */}
          <div className="space-y-6">
            {/* Nom du service */}
            <FormInput
              label="Nom du service"
              id="title"
              name="title"
              placeholder="Placeholder"
              value={formData.title}
              onChange={handleInputChange}
              error={errors.title}
              required
            />

            {/* Description */}
            <FormTextarea
              label="Description"
              id="description"
              name="description"
              placeholder="Texte avec possibilité de créer une simple mise en page. exemple caractère gras. liste à point,...."
              value={formData.description}
              onChange={handleInputChange}
              error={errors.description}
              rows={4}
              required
            />

            {/* Durée moyen de réalisation */}
            <FormSelect
              label="Durée moyen de réalisation"
              id="execution_time"
              name="execution_time"
              options={executionTimeOptions}
              value={formData.execution_time}
              onChange={handleInputChange}
              error={errors.execution_time}
              required
            />

            {/* Concepts */}
            <FormInput
              label="Nombre de concepts inclus"
              id="concepts"
              name="concepts"
              type="number"
              min="0"
              placeholder="Ex: 3"
              value={formData.concepts}
              onChange={handleInputChange}
              error={errors.concepts}
              required
            />

            {/* Révisions */}
            <FormInput
              label="Nombre de révisions incluses"
              id="revisions"
              name="revisions"
              type="number"
              min="0"
              placeholder="Ex: 2"
              value={formData.revisions}
              onChange={handleInputChange}
              error={errors.revisions}
              required
            />
          </div>
          
          {/* Col-2 */}
          <div className="space-y-6">
            {/* Catégorie */}
            <FormMultiSelect
              label="Catégorie"
              id="categories"
              options={categoryOptions}
              value={formData.categories.filter(cat => !cat.startsWith('arch_') && !cat.startsWith('viz_') && !cat.startsWith('bim_') && !cat.startsWith('interior_') && !cat.startsWith('vr_arch_'))}
              onChange={(values: string[]) => {
                handleMultiSelectChange('categories', values);
              }}
              placeholder="Selection parmi les catégories de services à définir"
            />

            {/* Price */}
            <div className="flex space-x-2">
              <FormInput
                label="Prix"
                id="price"
                name="price"
                type="number"
                min="0"
                step="0.01"
                placeholder="dès X USD"
                value={formData.price.toString()}
                onChange={handleInputChange}
                error={errors.price}
                required
              />
              <FormSelect
                label=" "
                id="price_unit"
                name="price_unit"
                options={[
                  { value: 'par image', label: 'par image' },
                  { value: 'par m2', label: 'par m2' },
                  { value: 'par projet', label: 'par projet' },
                ]}
                value={formData.price_unit}
                onChange={handleInputChange}
              />
            </div>
            
            {/* Image de couverture */}
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">
                Image de couverture
              </label>
              <div className="mt-1 flex items-center">
                <div className="flex items-center justify-center w-full h-20 border-2 border-dashed border-neutral-300 rounded-md relative">
                  <div className="text-center w-full">
                    <span className="mt-1 block text-sm font-medium text-neutral-500">
                      Selection parmi les catégories de services à définir
                    </span>
                    <button
                      type="button"
                      className="mt-2 px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
                      onClick={() => document.getElementById('images')?.click()}
                    >
                      Ajouter une image
                    </button>
                  </div>
                  <input
                    type="file"
                    id="images"
                    name="images"
                    accept="image/*"
                    multiple
                    onChange={handleImageChange}
                    className="hidden"
                    title="Sélectionner des fichiers"
                  />
                </div>
              </div>
              {imagePreviews.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {imagePreviews.map((preview, index) => (
                    <div key={index} className="relative">
                      <img
                        src={preview}
                        alt={`Aperçu ${index + 1}`}
                        className="w-20 h-20 object-cover rounded-md border border-neutral-300"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Projet associé */}
            <FormInput
              label="Projet associé"
              id="associated_project"
              name="associated_project"
              placeholder="Relation avec projet publié"
              value={formData.associated_project || ''}
              onChange={handleInputChange}
            />
          </div>
        </div>
        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Annuler
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
            style={{ backgroundColor: '#2980b9', color: 'black' }}
          >
            {isEditMode ? 'Mettre à jour le service' : 'Créer le service'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ServiceOfferForm;
