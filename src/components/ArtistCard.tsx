import React from 'react';
import { useNavigate } from 'react-router-dom';
import { MapPin, Star } from 'lucide-react';
import Card, { CardImage, CardBody, CardTitle } from './ui/Card';
import Avatar from './ui/Avatar';
import Badge from './ui/Badge';
import { API_BASE_URL } from '../config';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  is_professional: boolean;
  image?: string;
  cover_photo?: string;
  // Additional properties that might be available
  city?: string;
  country?: string;
  skills?: string[];
  availability_status?: string;
  hourly_rate?: string;
  rating?: number;
  review_count?: number;
}

interface ArtistCardProps {
  artist: User;
  compact?: boolean;
}

function ArtistCard({ artist, compact = false }: ArtistCardProps) {
  const navigate = useNavigate();

  console.log("Pro Zio :",artist)
  const defaultImage =
    'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';

  const coverImage =
    'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D';

  // Formater l'URL de l'image
  const getImageUrl = (imagePath: string | undefined) => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  // Utiliser les données réelles ou des valeurs par défaut
  const skills = artist.skills && artist.skills.length > 0 ? artist.skills : ['Animation 3D', 'Modelisation', 'Blender'];
  const rating = artist.rating || 4.8;
  const reviewCount = artist.review_count || Math.floor(Math.random() * 50) + 5;
  const location = (artist.city || 'Paris') + ', ' + (artist.country || 'France');
  const availability = artist.availability_status || 'available';

  // Déterminer l'URL de navigation
  const handleNavigate = () => {
    // Rediriger vers la page de détails du professionnel
    navigate(`/professionals/${artist.id}`);
  };

  if (compact) {
    return (
      <div
        className="flex items-center p-4 border border-neutral-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer"
        onClick={handleNavigate}
      >
        <Avatar
          size="lg"
          src={getImageUrl(artist.image)}
          fallback={`${artist.first_name[0]}${artist.last_name[0]}`}
          className="mr-4"
        />
        <div className="flex-1">
          <h3 className="font-semibold text-lg">{artist.first_name} {artist.last_name}</h3>
          <p className="text-neutral-600 text-sm">Artist 3D & Modelisation</p>
          <div className="flex items-center text-sm text-neutral-500 mt-1">
            <MapPin className="h-3 w-3 mr-1" />
            <span>{artist.city || 'Paris'}, {artist.country || 'France'}</span>
            <div className="mx-2 h-1 w-1 rounded-full bg-neutral-300"></div>
            <div className="flex items-center">
              <Star className="h-3 w-3 text-yellow-500 mr-1" />
              <span>{rating}</span>
              <span className="text-xs ml-1">({reviewCount})</span>
            </div>
          </div>
        </div>
        <Badge
          variant={availability === 'available' ? 'success' : availability === 'busy' ? 'warning' : 'error'}
          size="sm"
        >
          {availability === 'available' ? 'Disponible' : availability === 'busy' ? 'Occupé' : 'Indisponible'}
        </Badge>
      </div>
    );
  }

  return (
    <Card
      hoverable
      onClick={handleNavigate}
    >
      <CardImage
        src={getImageUrl(artist.cover_photo)}//{coverImage}
        alt={`${artist.first_name} ${artist.last_name}`}
        aspectRatio="video"
      />
      <CardBody className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center">
            <Avatar
              size="md"
              src={getImageUrl(artist.image)}
              fallback={`${artist.first_name[0]}${artist.last_name[0]}`}
              className="mr-3 border-2 border-white shadow-sm"
            />
            <div>
              <CardTitle className="text-base">{artist.first_name} {artist.last_name}</CardTitle>
              <p className="text-sm text-neutral-600">Artist 3D & Modelisation</p>
            </div>
          </div>
          <div className="flex items-center">
            <span className="text-yellow-500 mr-1">★</span>
            <span className="text-sm font-medium">{rating}</span>
            <span className="text-xs text-neutral-500 ml-1">({reviewCount})</span>
          </div>
        </div>

        <div className="flex flex-wrap gap-1 mb-3">
          {skills.slice(0, 3).map((skill, index) => (
            <Badge key={index} variant="neutral" size="sm">
              {skill}
            </Badge>
          ))}
          {skills.length > 3 && (
            <Badge variant="neutral" size="sm">
              +{skills.length - 3}
            </Badge>
          )}
        </div>

        <div className="flex justify-between items-center mt-2 text-sm">
          <span className="text-neutral-600 flex items-center">
            <MapPin className="h-3 w-3 mr-1" />
            {location}
          </span>
          <Badge
            variant={availability === 'available' ? 'success' : availability === 'busy' ? 'warning' : 'error'}
            size="sm"
          >
            {availability === 'available' ? 'Disponible' : availability === 'busy' ? 'Occupé' : 'Indisponible'}
          </Badge>
        </div>
      </CardBody>
    </Card>
  );
}

export default ArtistCard;