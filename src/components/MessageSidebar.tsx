import React, { useState } from 'react';
import { API_BASE_URL } from './../config';
import Avatar from './ui/Avatar';
import Modal from './ui/Modal';
import Button from './ui/Button';
import { Search } from "lucide-react";

type MessageSidebarProps = {
  offerDetail?: any;
  setSelectedApplication?: any;
  onSelectProfessional?: (id: number) => void;
};
const MessageSidebar:React.FC<MessageSidebarProps>  = ({offerDetail, setSelectedApplication, onSelectProfessional})=> {
  type ApplicationStatus = "all" | "invited" | "accepted";
  // type ApplicationStatus = "all" | "pending" | "invited" | "accepted" | "declined";

  const [activeFilter, setActiveFilter] = useState<ApplicationStatus>("all");

  const [showApplications, setShowApplications] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [professionals, setProfessionals] = useState<any[]>([]);
  const [loadingProfessionals, setLoadingProfessionals] = useState(false);
  const [selectedProfessionalId, setSelectedProfessionalId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [inviteError, setInviteError] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const token = localStorage.getItem('token');
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const isClient = currentUser.role === 'client' || !currentUser.is_professional;

  // Ouvrir la modal d'invitation
  const handleOpenInviteModal = async () => {
    console.log("Ouverture de la modal d'invitation");
    // alert("Ouverture de la modal d'invitation"); // Ajouter une alerte pour vérifier si la fonction est appelée
    setInviteError(null);
    setSelectedProfessionalId(null);
    setSearchQuery('');
    setShowInviteModal(true);
    await fetchProfessionals();
  };
  // Fermer la modal d'invitation
  const handleCloseInviteModal = () => {
    setShowInviteModal(false);
  };

  // Récupérer la liste des professionnels
    const fetchProfessionals = async () => {
      if (!token) return;
  
      setLoadingProfessionals(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/professionals`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
  
        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des professionnels');
        }
  
        const data = await response.json();
        console.log('Professionnels récupérés:', data);
  
        // Adapter la structure des données selon la réponse de l'API
        let professionalsList = [];
        if (data.users) {
          professionalsList = data.users;
        } else if (data.professionals) {
          professionalsList = data.professionals;
        } else if (Array.isArray(data)) {
          professionalsList = data;
        } else {
          professionalsList = [];
        }
        // S'assurer que chaque professionnel a un champ user_id
        const normalizedProfessionals = professionalsList.map((pro: any) => ({
          ...pro,
          user_id: pro.user_id || pro.id, // si l'API renvoie id = user_id
        }));
        setProfessionals(normalizedProfessionals);
      } catch (err) {
        console.error('Erreur:', err);
        setInviteError('Impossible de récupérer la liste des professionnels');
        // Utiliser des données de secours
        setProfessionals([
          { id: 1, user_id: 1, first_name: 'Jean', last_name: 'Dupont', avatar: 'https://randomuser.me/api/portraits/men/41.jpg' },
          { id: 2, user_id: 2, first_name: 'Marie', last_name: 'Martin', avatar: 'https://randomuser.me/api/portraits/women/22.jpg' },
          { id: 3, user_id: 3, first_name: 'Lucas', last_name: 'Bernard', avatar: 'https://randomuser.me/api/portraits/men/67.jpg' },
        ]);
      } finally {
        setLoadingProfessionals(false);
      }
    };

  // Filtrer les professionnels en fonction de la recherche
  const filteredProfessionals = professionals.filter(pro => {
    const fullName = `${pro.first_name || ''} ${pro.last_name || ''}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  // Envoyer une invitation à un professionnel
    const handleInviteProfessional = async () => {
      if (!token || !offerDetail.id || !selectedProfessionalId) {
        setInviteError('Veuillez sélectionner un professionnel');
        return;
      }
  
      try {
         console.log('Liste Pro', filteredProfessionals);
        const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerDetail.id}/invite`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ professional_id: selectedProfessionalId }),
        });
  
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Erreur lors de l\'invitation du professionnel');
        }
  
        const data = await response.json();
        console.log('Réponse de l\'invitation:', data);
  
        // Fermer la modal et afficher un message de succès
        setShowInviteModal(false);
        setSuccessMessage('Invitation envoyée avec succès au professionnel');
        window.location.reload();
      } catch (err) {
        console.error('Erreur:', err);
        setInviteError(err instanceof Error ? err.message : 'Erreur lors de l\'invitation du professionnel');
      }
    };

  const statusLabels: Record<ApplicationStatus, string> = {
    all: "All",
    // pending: "Pending",
    invited: "Invited",
    accepted: "Approved",
    // declined: "Declined",
  };

  const getUrlProlfil = (path : string)  => {
        return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
    };

  // Formater la date en style "il y a x minutes"
  function formatRelativeDate(dateString: string) {
    const date = new Date(dateString);
    const diffMs = Date.now() - date.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    if (diffMinutes < 1) return "À l'instant";
    if (diffMinutes < 60) return `Il y a ${diffMinutes} minute${diffMinutes > 1 ? "s" : ""}`;
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `Il y a ${diffHours} heure${diffHours > 1 ? "s" : ""}`;
    const diffDays = Math.floor(diffHours / 24);
    return `Il y a ${diffDays} jour${diffDays > 1 ? "s" : ""}`;
  }

  // Raccourcir une proposition
  function truncateText(text: string, maxLength: number) {
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
  }

   const getFilteredApplications = () => {
    if (!offerDetail?.applications) return [];
    if (activeFilter === "all") return offerDetail.applications;
    return offerDetail.applications.filter((app:any) => app.status === activeFilter);
  };

  const countByStatus = (status:any) => {
    if (!offerDetail?.applications) return 0;
    if (status === "all") return offerDetail.applications.length;
    return offerDetail.applications.filter((app:any) => app.status === status).length;
  };

  const filteredApplications = getFilteredApplications();

  return(
  <>
  <div className="shadow-none" style={{ flex: '0 0 360px', background: '#fff', minHeight: 500, padding: 24 }}>
    {/* Onglets filtres */}
      <div className="flex gap-4 mb-6 border-b pb-3 border-gray-200">
        {Object.entries(statusLabels).map(([statusKey, label]) => {
            const key = statusKey as ApplicationStatus;
            return (
              <button
                key={key}
                className={`py-2 px-4 text-sm font-semibold ${
                  activeFilter === key ? "text-black font-bold border-b-2 border-black" : "text-gray-400"
                }`}
                onClick={() => setActiveFilter(key)}
              >
                {label} ({countByStatus(key)})
              </button>
            );
          })}
      </div>
    

    {filteredApplications.length > 0 ? (
        <div style={{ marginBottom: 24 }}>
          <div className="space-y-1">
            {filteredApplications.map((application : any) => {
              const freelance = application.freelance_profile;
              return (
                <div
                  key={application.id}
                  className={`flex items-center rounded-lg px-2 py-2 cursor-pointer ${
                    selectedProfessionalId === freelance.user_id ? "bg-gray-100" : "hover:bg-gray-100"
                  }`}
                  onClick={() => {
                    setSelectedApplication && setSelectedApplication(application);
                    onSelectProfessional && onSelectProfessional(freelance.user_id);
                  }}
                >
                  <input
                    type="checkbox"
                    className="mr-3 w-4 h-4"
                    checked={selectedProfessionalId === freelance.user_id}
                    readOnly
                  />
                  <img
                    src={getUrlProlfil(freelance.avatar)}
                    alt={`${freelance.first_name} ${freelance.last_name}`}
                    className="w-10 h-10 rounded-full object-cover mr-3"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="font-bold text-sm text-[#1a1a1a]">
                        {freelance.first_name} {freelance.last_name}
                      </span>
                      <span className="text-xs text-gray-400 whitespace-nowrap ml-2">
                        {formatRelativeDate(application.created_at)}
                      </span>
                    </div>
                    <div className="flex items-center text-[11px] text-gray-400 truncate">
                      <svg
                        className="w-4 h-4 mr-1"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M3 10v4a1 1 0 001 1h3m10-5l-5-5m0 0l-5 5m5-5v12"
                        />
                      </svg>
                      {application.proposal
                        ? `${freelance.first_name} a postulé : ${truncateText(application.proposal, 30)}`
                        : `Candidature reçue de ${freelance.first_name}`}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ): (
        <div className="text-center text-gray-400 text-sm py-12">
          {/* <img
            src="https://www.svgrepo.com/show/530366/no-data.svg"
            alt="Aucune candidature"
            className="w-24 h-24 mx-auto mb-4 opacity-50"
          /> */}
          <div className="w-24 h-24 mx-auto mb-4 flex items-center justify-center bg-gray-100 rounded-full text-4xl opacity-50">
            📭
          </div>
          <p>Aucune candidature trouvée pour le filtre <strong>{statusLabels[activeFilter]}</strong></p>
          <p className="text-xs mt-2">Essayez un autre filtre ou invitez de nouveaux artistes !</p>
        </div>
      )}

    <button 
      style={{ width: '100%', padding: 12, borderRadius: 24, background: '#fff', border: '1px solid #ddd', fontWeight: 600, cursor: 'pointer'}}
      onClick={handleOpenInviteModal}
    >
      Invite a new artiste
    </button>
  </div>


   {/* 📦 Modal */}
      <Modal
        isOpen={showInviteModal}
        onClose={handleCloseInviteModal}
        title="Invite a professional"
      >
        <div className="p-4">
          {inviteError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {inviteError}
            </div>
          )}

          {/* 🔍 Search */}
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search a professional..."
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* 👥 List */}
          <div className="max-h-60 overflow-y-auto mb-4">
            {loadingProfessionals ? (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary-600"></div>
              </div>
            ) : filteredProfessionals.length === 0 ? (
              <div className="text-center py-4 text-neutral-500">
                No professional found.
              </div>
            ) : (
              <div className="space-y-2">
                {filteredProfessionals.map((professional) => (
                  <div
                    key={professional.user_id}
                    className={`flex items-center p-3 rounded-lg cursor-pointer ${
                      selectedProfessionalId === professional.user_id
                        ? "bg-primary-50 border border-primary-200"
                        : "hover:bg-neutral-50 border border-transparent"
                    }`}
                    onClick={() => setSelectedProfessionalId(professional.user_id)}
                  >
                    <Avatar
                      src={getUrlProlfil(professional.avatar)}
                      fallback={
                        (professional.first_name?.[0] || "") + (professional.last_name?.[0] || "")
                      }
                      size="md"
                      className="mr-3"
                    />
                    <div>
                      <h4 className="font-medium text-neutral-900">
                        {professional.first_name} {professional.last_name}
                      </h4>
                      {professional.title && (
                        <p className="text-sm text-neutral-500">{professional.title}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* ✅ Actions */}
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCloseInviteModal}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleInviteProfessional}
              disabled={!selectedProfessionalId || loadingProfessionals}
            >
              Invite
            </Button>
          </div>
        </div>
      </Modal>
  </>
);
};

export default MessageSidebar; 