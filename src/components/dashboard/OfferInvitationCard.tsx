import React from 'react';
import { Calendar, Clock, DollarSign, ThumbsUp, ThumbsDown } from 'lucide-react';
import Avatar from '../ui/Avatar';
import Badge from '../ui/Badge';
import Button from '../ui/Button';
import { API_BASE_URL } from '../../config';

interface OfferInvitationCardProps {
  id: number;
  id_offer: number;
  title: string;
  description: string;
  budget: string;
  deadline: string;
  status: string;
  client: {
    id: number;
    name: string;
    avatar?: string;
  };
  createdAt: string;
  isInvited?: boolean;
  onAccept: (id: number) => void;
  onDecline: (id: number) => void;
  onView: (id: number) => void;
}

const OfferInvitationCard: React.FC<OfferInvitationCardProps> = ({
  id,
  id_offer,
  title,
  description,
  budget,
  deadline,
  client,
  createdAt,
  status,
  isInvited = false,
  onAccept,
  onDecline,
  onView,
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (dateString: string) => {
    const deadline = new Date(dateString);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Délai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  const getUrlProlfil = (path : string)  => {
      return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  };

  return (
    <div className={`bg-white rounded-lg border ${isInvited ? 'border-blue-200' : 'border-neutral-200'} shadow-sm overflow-hidden`}>
      {isInvited && (
        <div className="bg-blue-50 px-4 py-2 border-b border-blue-200">
          <Badge color="info">Invitation personnalisée</Badge>
        </div>
      )}

      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-lg font-semibold text-neutral-900 line-clamp-1">{title}</h3>
          <span className="text-xs text-neutral-500">Reçu le {formatDate(createdAt)}</span>
        </div>

        <p className="text-neutral-600 text-sm mb-4 line-clamp-2">{description}</p>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center">
            <DollarSign className="h-4 w-4 text-neutral-500 mr-2" />
            <span className="text-sm text-neutral-700 font-medium">{budget}</span>
          </div>

          <div className="flex items-center">
            <Calendar className="h-4 w-4 text-neutral-500 mr-2" />
            <span className="text-sm text-neutral-700">{formatDate(deadline)}</span>
          </div>
        </div>

        <div className="flex items-center text-sm text-neutral-500 mb-4">
          <Clock className="h-4 w-4 mr-1" />
          <span>{getDaysRemaining(deadline)}</span>
        </div>

        <div className="flex items-center mb-4">
          <Avatar
            src={getUrlProlfil(String(client.avatar))}
            fallback={client.name.charAt(0)}
            size="sm"
            className="mr-2"
          />
          <span className="text-sm text-neutral-700">{client.name}</span>
        </div>

        <div className="flex space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onView(id_offer)}
          >
            Voir les détails
          </Button>

          {status === 'invited' && (
            <div className="flex gap-2">
              <Button
                variant="danger"
                size="sm"
                leftIcon={<ThumbsDown className="h-4 w-4" />}
                onClick={() => onDecline(id)}
              >
                Pas disponible
              </Button>

              <Button
                variant="primary"
                size="sm"
                leftIcon={<ThumbsUp className="h-4 w-4" />}
                onClick={() => onAccept(id)}
                style={{ backgroundColor: '#28a745', color: 'black' }}
              >
                Intéressé
              </Button>
            </div>
          )}

          {status === 'rejected' && (
            <p className="text-red-600 font-medium">Vous avez déjà décliné cette offre.</p>
          )}

          {status === 'accepted' && (
            <p className="text-green-700 font-medium">Vous avez déjà accepté cette opportunité.</p>
          )}

          {status === 'pending' && (
            <p className="text-blue-700 font-medium">Votre candidature pour cette offre est en cours de traitement.</p>
          )}

          {/* <Button
            variant="danger"
            size="sm"
            leftIcon={<ThumbsDown className="h-4 w-4" />}
            onClick={() => onDecline(id)}
          >
            Pas disponible
          </Button>

          <Button
            variant="primary"
            size="sm"
            leftIcon={<ThumbsUp className="h-4 w-4" />}
            onClick={() => onAccept(id)}
            style={{ backgroundColor: '#28a745', color: 'black' }}
          >
            Intéressé
          </Button> */}
        </div>
      </div>
    </div>
  );
};

export default OfferInvitationCard;
