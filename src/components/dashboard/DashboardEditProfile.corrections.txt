// Correction pour handleAddLanguage
const handleAddLanguage = () => {
  if (newLanguage.trim() && profileData && profileData.languages && !profileData.languages.includes(newLanguage.trim())) {
    setProfileData(prev => ({
      ...prev,
      languages: [...(prev?.languages || []), newLanguage.trim()]
    }));
    setNewLanguage('');
  }
};

// Correction pour handleRemoveLanguage
const handleRemoveLanguage = (languageToRemove: string) => {
  setProfileData(prev => ({
    ...prev,
    languages: prev?.languages?.filter(language => language !== languageToRemove) || []
  }));
};

// Correction pour handleAddService
const handleAddService = () => {
  if (newService.trim() && profileData && profileData.services_offered && !profileData.services_offered.includes(newService.trim())) {
    setProfileData(prev => ({
      ...prev,
      services_offered: [...(prev?.services_offered || []), newService.trim()]
    }));
    setNewService('');
  }
};

// Correction pour handleRemoveService
const handleRemoveService = (serviceToRemove: string) => {
  setProfileData(prev => ({
    ...prev,
    services_offered: prev?.services_offered?.filter(service => service !== serviceToRemove) || []
  }));
};

// Correction pour handleSubmit
const handleSubmit = async (e?: React.FormEvent) => {
  if (e) e.preventDefault();
  setSaving(true);
  setError(null);
  setSuccess(null);

  try {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
      return;
    }

    // Vérifier si profileData existe
    if (!profileData) {
      throw new Error('Profile data is not available');
    }

    const formData = new FormData();
    formData.append('first_name', profileData.first_name || '');
    formData.append('last_name', profileData.last_name || '');
    formData.append('phone', profileData.phone || '');
    formData.append('address', profileData.address || '');
    formData.append('city', profileData.city || '');
    formData.append('country', profileData.country || '');
    formData.append('bio', profileData.bio || '');

    if (profileData.title) {
      formData.append('title', profileData.title);
    }

    if (profileData.hourly_rate) {
      formData.append('hourly_rate', profileData.hourly_rate.toString());
    }

    // Ajouter les compétences individuellement
    if (profileData.skills && profileData.skills.length > 0) {
      profileData.skills.forEach((skill, index) => {
        formData.append(`skills[${index}]`, skill);
      });
    }

    // Ajouter les langues individuellement
    if (profileData.languages && profileData.languages.length > 0) {
      profileData.languages.forEach((language, index) => {
        formData.append(`languages[${index}]`, language);
      });
    }

    // Ajouter les services offerts individuellement
    if (profileData.services_offered && profileData.services_offered.length > 0) {
      profileData.services_offered.forEach((service, index) => {
        formData.append(`services_offered[${index}]`, service);
      });
    }

    if (avatarFile) {
      formData.append('profile_picture', avatarFile);
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/profile/complete`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      const responseText = await response.text();
      console.log('Raw response:', responseText);

      let responseData: { message?: string; profile?: any; completion_percentage?: number };
      try {
        responseData = JSON.parse(responseText);
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        throw new Error('Invalid response from server');
      }

      if (!response.ok) {
        throw new Error(responseData.message || 'Failed to update profile');
      }

      console.log('Profile update response:', responseData);
    } catch (apiError) {
      console.error('API error:', apiError);
      throw apiError;
    }

    setSuccess('Profile updated successfully!');

    // Redirect to profile page after a short delay
    setTimeout(() => {
      navigate('/dashboard/profile');
    }, 2000);

  } catch (err) {
    console.error('Error updating profile:', err);
    setError(err instanceof Error ? err.message : 'An unknown error occurred');
  } finally {
    setSaving(false);
  }
};
