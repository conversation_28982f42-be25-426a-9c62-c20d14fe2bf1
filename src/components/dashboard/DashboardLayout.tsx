import React, { ReactNode } from 'react';
import Header from '../Header';
import Footer from '../Footer';

interface DashboardLayoutProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  actions?: ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  title,
  subtitle,
  actions,
}) => {
  return (
    <div className="min-h-screen flex flex-col bg-neutral-50">
      <Header />

      <main className="flex-1">
        {(title || subtitle || actions) && (
          <div className="bg-white border-b border-neutral-200 shadow-sm">
            <div className="w-full max-w-[1512px] mx-auto px-4 md:px-20 py-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                  {title && (
                    <h1 className="text-2xl font-bold text-neutral-900">{title}</h1>
                  )}
                  {subtitle && (
                    <p className="mt-1 text-sm text-neutral-600">{subtitle}</p>
                  )}
                </div>

                {actions && (
                  <div className="mt-4 md:mt-0 flex-shrink-0 flex space-x-3" style={{ display: 'flex', gap: '0.75rem' }}>
                    {actions}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="w-full max-w-[1512px] mx-auto px-4 md:px-20 py-8">
          {children}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default DashboardLayout;
