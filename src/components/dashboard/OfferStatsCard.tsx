import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip, PieLabelRenderProps } from 'recharts';

interface OfferStatsCardProps {
  title: string;
  stats: {
    total: number;
    interested: number;
    notAvailable: number;
    pending: number;
    selected: number;
  };
  className?: string;
}

const OfferStatsCard: React.FC<OfferStatsCardProps> = ({
  title,
  stats,
  className = '',
}) => {
  // Calculer les pourcentages
  const responseRate = stats.total > 0
    ? Math.round(((stats.interested + stats.notAvailable) / stats.total) * 100)
    : 0;

  const acceptanceRate = (stats.interested + stats.notAvailable) > 0
    ? Math.round((stats.interested / (stats.interested + stats.notAvailable)) * 100)
    : 0;

  // Données pour le graphique en camembert
  const pieData = [
    { name: 'Intéressés', value: stats.interested, color: '#4ade80' },
    { name: 'Non disponibles', value: stats.notAvailable, color: '#f87171' },
    { name: 'En attente', value: stats.pending, color: '#fbbf24' },
    { name: 'Sélectionnés', value: stats.selected, color: '#60a5fa' },
  ].filter(item => item.value > 0);

  return (
    <div className={`bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden ${className}`}>
      <div className="px-6 py-4 border-b border-neutral-200">
        <h3 className="text-lg font-semibold text-neutral-900">{title}</h3>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Statistiques */}
          <div className="space-y-6">
            <div>
              <h4 className="text-sm font-medium text-neutral-500 mb-1">Total des appels d'offre</h4>
              <p className="text-3xl font-bold text-neutral-900">{stats.total}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-neutral-500 mb-1">Taux de réponse</h4>
              <p className="text-3xl font-bold text-neutral-900">{responseRate}%</p>
              <p className="text-sm text-neutral-500">
                {stats.interested + stats.notAvailable} réponses sur {stats.total} appels d'offre
              </p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-neutral-500 mb-1">Taux d'acceptation</h4>
              <p className="text-3xl font-bold text-neutral-900">{acceptanceRate}%</p>
              <p className="text-sm text-neutral-500">
                {stats.interested} intéressés sur {stats.interested + stats.notAvailable} réponses
              </p>
            </div>
          </div>

          {/* Graphique */}
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }: PieLabelRenderProps) => `${name}: ${(percent ? (percent * 100).toFixed(0) : 0)}%`}
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => [`${value} appels d'offre`, '']}
                  labelFormatter={(name: string) => name}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Légende */}
        <div className="mt-6 grid grid-cols-2 gap-2">
          {pieData.map((entry, index) => (
            <div key={index} className="flex items-center">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-neutral-700">
                {entry.name}: {entry.value}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OfferStatsCard;
