/* Styles pour le composant ProfileDashboard */

.edit-profile-button {
  background-color: #2980b9;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.edit-profile-button:hover {
  background-color: #2471a3;
}

.edit-profile-button svg {
  margin-right: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
}

.completion-progress-bar {
  background-color: var(--primary-600);
  height: 0.5rem;
  border-radius: 9999px;
}

.portfolio-action-button {
  background-color: #2980b9;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.portfolio-action-button:hover {
  background-color: #2471a3;
}

.services-action-button {
  background-color: #2980b9;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
