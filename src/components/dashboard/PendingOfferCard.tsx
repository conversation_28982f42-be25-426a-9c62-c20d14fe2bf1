import React from 'react';
import { Clock, Users, Filter, Send, Trash2 } from 'lucide-react';
import Badge from '../ui/Badge';
import Button from '../ui/Button';

interface PendingOfferCardProps {
  id: number;
  title: string;
  description: string;
  createdAt: string;
  user_id?: number; // ID de l'utilisateur propriétaire de l'offre
  filters: {
    location?: string;
    languages?: string[];
    skills?: string[];
  };
  maxProposals?: number;
  onPublish: (id: number) => void;
  onEdit: (id: number) => void;
  onDelete?: (id: number) => void;
}

const PendingOfferCard: React.FC<PendingOfferCardProps> = ({
  id,
  title,
  description,
  createdAt,
  user_id,
  filters,
  maxProposals,
  onPublish,
  onEdit,
  onDelete,
}) => {
  // Récupérer l'utilisateur connecté
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // Vérifier si l'utilisateur est le propriétaire de l'offre
  const isOwner = user_id ? user_id === user.id : true; // Par défaut, considérer que l'utilisateur est propriétaire si user_id n'est pas défini

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <div className="bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden">
      <div className="bg-blue-50 px-4 py-2 border-b border-blue-200">
        <div className="flex justify-between items-center">
          <Badge color="info">Offre ouverte</Badge>
          <span className="text-xs text-neutral-500">Créé le {formatDate(createdAt)}</span>
        </div>
      </div>

      <div className="p-6">
        <h3 className="text-lg font-semibold text-neutral-900 mb-2">{title}</h3>
        <p className="text-neutral-600 text-sm mb-4 line-clamp-2">{description}</p>

        <div className="space-y-3 mb-4">
          {filters.location && (
            <div className="flex items-center text-sm">
              <Filter className="h-4 w-4 text-neutral-500 mr-2" />
              <span className="text-neutral-700">Localité: <span className="font-medium">{filters.location}</span></span>
            </div>
          )}

          {filters.languages && filters.languages.length > 0 && (
            <div className="flex items-center text-sm">
              <Filter className="h-4 w-4 text-neutral-500 mr-2" />
              <span className="text-neutral-700">Langues: <span className="font-medium">{filters.languages.join(', ')}</span></span>
            </div>
          )}

          {filters.skills && filters.skills.length > 0 && (
            <div className="flex items-center text-sm">
              <Filter className="h-4 w-4 text-neutral-500 mr-2" />
              <span className="text-neutral-700">Compétences: <span className="font-medium">{filters.skills.join(', ')}</span></span>
            </div>
          )}

          {maxProposals && (
            <div className="flex items-center text-sm">
              <Users className="h-4 w-4 text-neutral-500 mr-2" />
              <span className="text-neutral-700">Limite: <span className="font-medium">{maxProposals} propositions</span></span>
            </div>
          )}
        </div>

        <div className="flex space-x-3">
          {isOwner && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(id)}
            >
              Modifier
            </Button>
          )}

          <Button
            variant="primary"
            size="sm"
            leftIcon={<Send className="h-4 w-4" />}
            onClick={() => onPublish(id)}
            style={{ backgroundColor: '#2980b9', color: 'white' }}
          >
            Voir les détails
          </Button>

          {isOwner && onDelete && (
            <Button
              variant="danger"
              size="sm"
              leftIcon={<Trash2 className="h-4 w-4" />}
              onClick={() => onDelete(id)}
            >
              Supprimer
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PendingOfferCard;
