import React from 'react';
import Header from '../Header';
import Footer from '../Footer';

interface ClientDashboardProps {
  completionPercentage: number;
}

const ClientDashboard: React.FC<ClientDashboardProps> = ({ completionPercentage }) => {
  return (
    <div>
      <Header />
      <div className="flex justify-center items-center h-screen">
        <h1 className="text-3xl font-bold text-gray-800">Client Dashboard</h1>
      </div>
      <Footer />
    </div>
  );
};

export default ClientDashboard;
