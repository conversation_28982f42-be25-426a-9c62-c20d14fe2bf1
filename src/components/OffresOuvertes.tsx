// import React, { useState, useEffect } from 'react';
// import { MoreHorizontal } from 'lucide-react';
// import CreatProjetClient from './CreatProjetClient';
// import { API_BASE_URL } from '../config';
// import { useNavigate } from 'react-router-dom';
// interface Filters{
//   languages: string[];
//   skills: string[];
//   location: string;
//   experience_years: number,
//   availability_status: string;
// }

// interface Offer {
//   id: number;
//   title: string;
//   categories: string[];
//   budget: string;
//   deadline: string;
//   company: string;
//   website: string;
//   description: string;
//   files: File[];
//   recruitmentType: 'company' | 'personal';
//   openToApplications: boolean;
//   autoInvite: boolean;
//   status:string;
//   filters?: Filters;
// }

// const OffresOuvertes = () => {
//   const [offers, setOffers] = useState<Offer[]>([]);
//   const [offersClosed, setOffersClosed] = useState<Offer[]>([]);
//   const [isModalOpen, setIsModalOpen] = useState(false);
//   const [selectedOffer, setSelectedOffer] = useState<Offer | undefined>(undefined);
//   const [openMenuId, setOpenMenuId] = useState<number | null>(null);

//   useEffect(() => {
//     const fetchOffers = async () => {
//       const token = localStorage.getItem("token");
//       try {
//         const response = await fetch(`${API_BASE_URL}/api/client/open-offers`, {
//           method: 'GET',
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (!response.ok) {
//           throw new Error(`Erreur HTTP : ${response.status}`);
//         }

//         const data = await response.json();
//         const apiOffers = data.open_offers.map((offer: any) => ({
//           id: offer.id,
//           title: offer.title,
//           categories: typeof offer.categories === "string" ? JSON.parse(offer.categories) : [], // ✅ Convertir en tableau
//           budget: offer.budget,
//           deadline: offer.deadline,
//           company: offer.company,
//           website: offer.website,
//           description: offer.description,
//           files: [],
//           recruitmentType: 'company',
//           openToApplications: true,
//           autoInvite: false,
//           status : offer.status,
//           filters: offer.filters || { languages: [], skills: [], location: "", experience_years: 0, availability_status: "available" },
//         }));

//         setOffers(apiOffers);
//         console.log('✅ Offres récupérées depuis l\'API :', apiOffers);
//       } catch (error) {
//         console.error('❌ Erreur lors de la récupération des offres :', error);
//       }
//     };

//     // const fetchOffersClosed = async () => {
//     //   const token = localStorage.getItem("token");
//     //   try {
//     //     const response = await fetch(`${API_BASE_URL}/api/client/closed-completed-offers`, {
//     //       method: 'GET',
//     //       headers: {
//     //         'Authorization': `Bearer ${token}`,
//     //         'Content-Type': 'application/json',
//     //       },
//     //     });

//     //     if (!response.ok) {
//     //       throw new Error(`Erreur HTTP : ${response.status}`);
//     //     }

//     //     const data = await response.json();
//     //     const apiOffers = data.open_offers.map((offer: any) => ({
//     //       id: offer.id,
//     //       title: offer.title,
//     //       categories: typeof offer.categories === "string" ? JSON.parse(offer.categories) : [], // ✅ Convertir en tableau
//     //       budget: offer.budget,
//     //       deadline: offer.deadline,
//     //       company: offer.company,
//     //       website: offer.website,
//     //       description: offer.description,
//     //       files: [],
//     //       recruitmentType: 'company',
//     //       openToApplications: true,
//     //       autoInvite: false,
//     //       status : offer.status,
//     //       filters: offer.filters || { languages: [], skills: [], location: "", experience_years: 0, availability_status: "available" },
//     //     }));

//     //     setOffersClosed(apiOffers);
//     //     console.log('✅ Offres récupérées depuis l\'API :', apiOffers);
//     //   } catch (error) {
//     //     console.error('❌ Erreur lors de la récupération des offres :', error);
//     //   }
//     // };



//     fetchOffers();
//     // fetchOffersClosed();
//   }, []);

//   const handleAddOffer = (offer: Offer) => {
//     if (selectedOffer) {
//       setOffers((prevOffers) =>
//         prevOffers.map((o) => (o.id === offer.id ? offer : o))
//       );
//     } else {
//       setOffers((prevOffers) => [...prevOffers, offer]);
//     }
//     setSelectedOffer(undefined);
//     setIsModalOpen(false);
//   };

//   const confirmDelete = (id: number) => {
//     if (window.confirm("Êtes-vous sûr de vouloir supprimer cette offre ?")) {
//       handleDelete(id);
//     }
//   };

//   const handleDelete = async (id: number) => {
//     const token = localStorage.getItem("token");
//     try {
//       const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
//         method: 'DELETE',
//         headers: {
//           Authorization: `Bearer ${token}`,
//           'Content-Type': 'application/json',
//         },
//       });

//       if (!response.ok) {
//         throw new Error(`Erreur lors de la suppression : ${response.status}`);
//       }

//       const result = await response.json();
//       console.log('Suppression réussie :', result);

//       const updatedOffers = offers.filter((offer) => offer.id !== id);
//       setOffers(updatedOffers);
//     } catch (error) {
//       console.error('Erreur lors de la suppression de l\'offre :', error);
//     }
//   };

//   const handleEdit = (offer: Offer) => {
//     console.log("🔄 Modification de l'offre :", offer);
//     setSelectedOffer(offer);

//     // ✅ Mettre à jour les catégories dans le composant parent avant d'ouvrir la modale
//     setTimeout(() => {
//       setIsModalOpen(true);
//       setOpenMenuId(null);
//     }, 100);
//   };

//   // const handleEdit = (offer: Offer) => {
//   //   setSelectedOffer(offer);
//   //   setIsModalOpen(true);
//   //   setOpenMenuId(null); // Fermer le menu contextuel lors de l'édition
//   // };

//   const handleCreateOffer = () => {
//     setSelectedOffer(undefined);
//     setIsModalOpen(true);
//   };

//   const toggleMenu = (id: number) => {
//     setOpenMenuId(openMenuId === id ? null : id);
//   };

//   const navigate = useNavigate();
// import React, { useState, useEffect } from 'react';
// import { MoreHorizontal } from 'lucide-react';
// import CreatProjetClient from './CreatProjetClient';
// import { API_BASE_URL } from '../config';
// import { useNavigate } from 'react-router-dom';

// interface Filters {
//   languages: string[];
//   skills: string[];
//   location: string;
//   experience_years: number;
//   availability_status: string;
// }

// interface Offer {
//   id: number;
//   title: string;
//   categories: string[];
//   budget: string;
//   deadline: string;
//   company: string;
//   website: string;
//   description: string;
//   files: File[];
//   recruitmentType: 'company' | 'personal';
//   openToApplications: boolean;
//   autoInvite: boolean;
//   status: string;
//   filters?: Filters;
// }

// const OffresOuvertes = () => {
//   const [offers, setOffers] = useState<Offer[]>([]);
//   const [offersClosed, setOffersClosed] = useState<Offer[]>([]);
//   const [isModalOpen, setIsModalOpen] = useState(false);
//   const [selectedOffer, setSelectedOffer] = useState<Offer | undefined>(undefined);
//   const [openMenuId, setOpenMenuId] = useState<number | null>(null);
//   const navigate = useNavigate();

//   useEffect(() => {
//     const fetchOffers = async () => {
//       const token = localStorage.getItem("token");
//       try {
//         const response = await fetch(`${API_BASE_URL}/api/client/open-offers`, {
//           method: 'GET',
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (!response.ok) {
//           throw new Error(`Erreur HTTP : ${response.status}`);
//         }

//         const data = await response.json();
//         const apiOffers = data.open_offers.map((offer: any) => ({
//           id: offer.id,
//           title: offer.title,
//           categories: typeof offer.categories === "string" ? JSON.parse(offer.categories) : [],
//           budget: offer.budget,
//           deadline: offer.deadline,
//           company: offer.company,
//           website: offer.website,
//           description: offer.description,
//           files: [],
//           recruitmentType: 'company',
//           openToApplications: true,
//           autoInvite: false,
//           status: offer.status,
//           filters: offer.filters || { languages: [], skills: [], location: "", experience_years: 0, availability_status: "available" },
//         }));

//         setOffers(apiOffers);
//       } catch (error) {
//         console.error('❌ Erreur lors de la récupération des offres :', error);
//       }
//     };

//     const fetchOffersClosed = async () => {
//       const token = localStorage.getItem("token");
//       try {
//         const response = await fetch(`${API_BASE_URL}/api/client/closed-completed-offers`, {
//           method: 'GET',
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (!response.ok) {
//           throw new Error(`Erreur HTTP : ${response.status}`);
//         }

//         const data = await response.json();
//         const apiOffers = data.client_closed_completed_offers.map((offer: any) => ({
//           id: offer.id,
//           title: offer.title,
//           categories: typeof offer.categories === "string" ? JSON.parse(offer.categories) : [],
//           budget: offer.budget,
//           deadline: offer.deadline,
//           company: offer.company,
//           website: offer.website,
//           description: offer.description,
//           files: [],
//           recruitmentType: 'company',
//           openToApplications: true,
//           autoInvite: false,
//           status: offer.status,
//           filters: offer.filters || { languages: [], skills: [], location: "", experience_years: 0, availability_status: "available" },
//         }));

//         setOffersClosed(apiOffers);
//       } catch (error) {
//         console.error('❌ Erreur lors de la récupération des offres fermées/terminées :', error);
//       }
//     };

//     fetchOffers();
//     fetchOffersClosed();
//   }, []);

//   const handleAddOffer = (offer: Offer) => {
//     if (selectedOffer) {
//       setOffers((prevOffers) =>
//         prevOffers.map((o) => (o.id === offer.id ? offer : o))
//       );
//     } else {
//       setOffers((prevOffers) => [...prevOffers, offer]);
//     }
//     setSelectedOffer(undefined);
//     setIsModalOpen(false);
//   };

//   const confirmDelete = (id: number) => {
//     if (window.confirm("Êtes-vous sûr de vouloir supprimer cette offre ?")) {
//       handleDelete(id);
//     }
//   };

//   const handleDelete = async (id: number) => {
//     const token = localStorage.getItem("token");
//     try {
//       const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
//         method: 'DELETE',
//         headers: {
//           Authorization: `Bearer ${token}`,
//           'Content-Type': 'application/json',
//         },
//       });

//       if (!response.ok) {
//         throw new Error(`Erreur lors de la suppression : ${response.status}`);
//       }

//       const updatedOffers = offers.filter((offer) => offer.id !== id);
//       setOffers(updatedOffers);
//     } catch (error) {
//       console.error('Erreur lors de la suppression de l\'offre :', error);
//     }
//   };

//   const handleEdit = (offer: Offer) => {
//     setSelectedOffer(offer);
//     setTimeout(() => {
//       setIsModalOpen(true);
//       setOpenMenuId(null);
//     }, 100);
//   };

//   const handleCreateOffer = () => {
//     setSelectedOffer(undefined);
//     setIsModalOpen(true);
//   };

//   const toggleMenu = (id: number) => {
//     setOpenMenuId(openMenuId === id ? null : id);
//   };
import React, { useState, useEffect } from 'react';
import { MoreHorizontal } from 'lucide-react';
import CreatProjetClient from './CreatProjetClient';
import { API_BASE_URL } from '../config';
import { useNavigate } from 'react-router-dom';

interface Filters {
  languages: string[];
  skills: string[];
  location: string;
  experience_years: number;
  availability_status: string;
}

interface Offer {
  id: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company: string;
  website: string;
  description: string;
  files: File[];
  recruitmentType: 'company' | 'personal';
  openToApplications: boolean;
  autoInvite: boolean;
  status: string;
  filters?: Filters;
}

const OffresOuvertes = () => {
  const [offers, setOffers] = useState<Offer[]>([]);
  const [offersClosed, setOffersClosed] = useState<Offer[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState<Offer | undefined>(undefined);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchOffers = async () => {
      const token = localStorage.getItem("token");
      try {
        const response = await fetch(`${API_BASE_URL}/api/client/open-offers`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Erreur HTTP : ${response.status}`);
        }

        const data = await response.json();
        const apiOffers = data.client_open_offers.map((offer: any) => ({
          id: offer.id,
          title: offer.title,
          categories: typeof offer.categories === "string" ? JSON.parse(offer.categories) : [],
          budget: offer.budget,
          deadline: offer.deadline,
          company: offer.company,
          website: offer.website,
          description: offer.description,
          files: [],
          recruitmentType: offer.recruitment_type,
          openToApplications: offer.open_to_applications,
          autoInvite: offer.auto_invite,
          status: offer.status,
          filters: offer.filters || { languages: [], skills: [], location: "", experience_years: 0, availability_status: "available" },
        }));

        setOffers(apiOffers);
      } catch (error) {
        console.error('❌ Erreur lors de la récupération des offres :', error);
      }
    };

    const fetchOffersClosed = async () => {
      const token = localStorage.getItem("token");
      try {
        const response = await fetch(`${API_BASE_URL}/api/client/closed-completed-offers`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Erreur HTTP : ${response.status}`);
        }

        const data = await response.json();
        const apiOffers = data.client_closed_completed_offers.map((offer: any) => ({
          id: offer.id,
          title: offer.title,
          categories: typeof offer.categories === "string" ? JSON.parse(offer.categories) : [],
          budget: offer.budget,
          deadline: offer.deadline,
          company: offer.company,
          website: offer.website,
          description: offer.description,
          files: [],
          recruitmentType: offer.recruitment_type,
          openToApplications: offer.open_to_applications,
          autoInvite: offer.auto_invite,
          status: offer.status,
          filters: offer.filters || { languages: [], skills: [], location: "", experience_years: 0, availability_status: "available" },
        }));

        setOffersClosed(apiOffers);
      } catch (error) {
        console.error('❌ Erreur lors de la récupération des offres fermées/terminées :', error);
      }
    };

    fetchOffers();
    fetchOffersClosed();
  }, []);

  const handleAddOffer = (offer: Offer) => {
    if (selectedOffer) {
      setOffers((prevOffers) =>
        prevOffers.map((o) => (o.id === offer.id ? offer : o))
      );
    } else {
      setOffers((prevOffers) => [...prevOffers, offer]);
    }
    setSelectedOffer(undefined);
    setIsModalOpen(false);
  };

  const confirmDelete = (id: number) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer cette offre ?")) {
      handleDelete(id);
    }
  };

  const handleDelete = async (id: number) => {
    const token = localStorage.getItem("token");
    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Erreur lors de la suppression : ${response.status}`);
      }

      const updatedOffers = offers.filter((offer) => offer.id !== id);
      setOffers(updatedOffers);
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'offre :', error);
    }
  };

  const handleEdit = (offer: Offer) => {
    setSelectedOffer(offer);
    setTimeout(() => {
      setIsModalOpen(true);
      setOpenMenuId(null);
    }, 100);
  };

  const handleCreateOffer = () => {
    // Rediriger vers la page de création d'offre avec le paramètre create=true
    navigate('/dashboard/projects?create=true');
  };

  const toggleMenu = (id: number) => {
    setOpenMenuId(openMenuId === id ? null : id);
  };
  return (
    <>
      {/* Section "Mes Projets" */}
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Mes Projets</h1>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {offers
          .filter((offer) => offer.status === 'open')
          .map((offer) => (
            <div
              key={offer.id}
              className="relative bg-white shadow-md rounded-md overflow-hidden"
            >
              <img
                src="https://mir-s3-cdn-cf.behance.net/project_modules/fs/96bef2182510483.652efbbf698a2.png"
                alt={offer.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-4">
                <h2 className="text-lg font-semibold">{offer.title}</h2>
                <p className="text-gray-500">{offer.company}</p>
                <p className="text-gray-500">{offer.description}</p>
                <p className="text-gray-500">Budget: {offer.budget}</p>
                {/* <p className="text-gray-500">Deadline: {offer.deadline}</p> */}
                <p className="text-gray-500">
                  Deadline: {new Date(offer.deadline).toLocaleDateString('fr-FR', { day: 'numeric', month: 'long', year: 'numeric' })}
                </p>

              </div>
              <div className="absolute top-2 right-2 z-10">
                <div className="relative">
                  <button
                    className="p-2 bg-white rounded-full shadow"
                    onClick={() => toggleMenu(offer.id)}
                  >
                    <MoreHorizontal className="w-5 h-5 text-gray-700" />
                  </button>
                  {openMenuId === offer.id && (
                    <div className="absolute right-0 mt-2 w-40 bg-white shadow-lg rounded-md">
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100"
                        onClick={() => navigate(`/offre/${offer.id}`)}
                      >
                        Détails
                      </button>

                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100"
                        onClick={() => handleEdit(offer)}
                      >
                        Modifier
                      </button>


                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                        onClick={() => confirmDelete(offer.id)}
                      >
                        Supprimer
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}

          <div
            className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 p-8 rounded-md cursor-pointer"
            onClick={handleCreateOffer}
          >
            <div className="bg-blue-100 text-blue-500 rounded-full p-4 mb-2">
              <span className="text-3xl">+</span>
            </div>
            <h2 className="text-lg font-semibold">Créer une offre ouverte</h2>
          </div>
        </div>

        {isModalOpen && (
          <CreatProjetClient
            onClose={() => setIsModalOpen(false)}
            onAddOffer={handleAddOffer}
            existingOffer={selectedOffer}
          />
        )}
      </div>

      {/* Section "Encours" */}
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Encours</h1>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {offers
          .filter((offer) => offer.status === 'in_progress') // 👈 filtrage ici
          .map((offer) => (
            <div
              key={offer.id}
              className="relative bg-white shadow-md rounded-md overflow-hidden"
            >
              <img
                src="https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D"
                alt={offer.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-4">
                <h2 className="text-lg font-semibold">{offer.title}</h2>
                <p className="text-gray-500">{offer.company}</p>
                <p className="text-gray-500">{offer.description}</p>
                <p className="text-gray-500">Budget: {offer.budget}</p>
                <p className="text-gray-500">Deadline: {offer.deadline}</p>
              </div>
              <div className="absolute top-2 right-2 z-10">
                <div className="relative">
                  <button
                    className="p-2 bg-white rounded-full shadow"
                    onClick={() => toggleMenu(offer.id)}
                  >
                    <MoreHorizontal className="w-5 h-5 text-gray-700" />
                  </button>
                  {openMenuId === offer.id && (
                    <div className="absolute right-0 mt-2 w-40 bg-white shadow-lg rounded-md">
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100"
                        onClick={() => navigate(`/offre/${offer.id}`)}
                      >
                        Détails
                      </button>

                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100"
                        onClick={() => handleEdit(offer)}
                      >
                        Modifier
                      </button>

                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                        onClick={() => confirmDelete(offer.id)}
                      >
                        Supprimer
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Section "Terminer" */}
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Terminer</h1>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {offersClosed
          .map((offerClose) => (
            <div
              key={offerClose.id}
              className="relative bg-white shadow-md rounded-md overflow-hidden"
            >
              <img
                src="https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                alt={offerClose.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-4">
                <h2 className="text-lg font-semibold">{offerClose.title}</h2>
                <p className="text-gray-500">{offerClose.company}</p>
                <p className="text-gray-500">{offerClose.description}</p>
                <p className="text-gray-500">Budget: {offerClose.budget}</p>
                <p className="text-gray-500">Deadline: {offerClose.deadline}</p>
              </div>
              <div className="absolute top-2 right-2 z-10">
                <div className="relative">
                  <button
                    className="p-2 bg-white rounded-full shadow"
                    onClick={() => toggleMenu(offerClose.id)}
                  >
                    <MoreHorizontal className="w-5 h-5 text-gray-700" />
                  </button>
                  {openMenuId === offerClose.id && (
                    <div className="absolute right-0 mt-2 w-40 bg-white shadow-lg rounded-md">
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100"
                        onClick={() => navigate(`/offre/${offerClose.id}`)}
                      >
                        Détails
                      </button>

                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100"
                        onClick={() => handleEdit(offerClose)}
                      >
                        Modifier
                      </button>

                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                        onClick={() => confirmDelete(offerClose.id)}
                      >
                        Supprimer
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default OffresOuvertes;