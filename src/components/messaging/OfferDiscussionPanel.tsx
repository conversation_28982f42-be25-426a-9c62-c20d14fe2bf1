import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Send, Paperclip, ChevronLeft, Download, User, Clock } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import Avatar from '../ui/Avatar';
import Button from '../ui/Button';
import { useNotifications } from '../notifications/NotificationContext';


interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  email_verified_at: string;
  is_professional: boolean;
  created_at: string;
  updated_at: string;
  profile_completed: boolean;
}

interface Message {
  id: number;
  open_offer_id: number;
  sender_id: number;
  receiver_id: number;
  message_text: string;
  created_at: string;
  updated_at: string;
  sender: User;
  receiver: User;
}

// interface Message {
//   id: number;
//   sender_id: number;
//   sender_name: string;
//   sender_avatar?: string;
//   sender_type: 'client' | 'professional';
//   message_text: string;
//   attachments?: string[];
//   created_at: string;
// }

interface OfferDiscussionPanelProps {
  offerId: number;
  offerTitle: string;
  clientId?: number;
  clientName?: string;
  clientAvatar?: string;
  professionalId?: number;
  professionalName?: string;
  professionalAvatar?: string;
  isClient: boolean;
  onBack?: () => void;
}

const OfferDiscussionPanel: React.FC<OfferDiscussionPanelProps> = ({
  offerId,
  offerTitle,
  clientId,
  clientName = "Client",  // Valeur par défaut pour éviter les erreurs
  clientAvatar,
  professionalId,
  professionalName = "Professionnel",  // Valeur par défaut pour éviter les erreurs
  professionalAvatar,
  isClient,
  onBack,
}) => {
  const navigate = useNavigate();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { addOfferNotification } = useNotifications();
  const [lastMessageId, setLastMessageId] = useState<number | null>(null);

  const token = localStorage.getItem('token');
  const currentUserId = JSON.parse(localStorage.getItem('user') || '{}').id;

  // Récupérer les messages
  useEffect(() => {
    const fetchMessages = async (sinceId: number | null = null) => {
      if (!token || !offerId) return;

      setLoading(true);
      try {
        // isClient
        let url = `${API_BASE_URL}/api/open-offers/${offerId}/messages`;
        const params = new URLSearchParams();

        if (!isClient) {
          params.append('professional_id', String(currentUserId));
        }

        if (sinceId !== null) {
          params.append('since_id', String(sinceId));
        }

        if (params.toString()) {
          url += `?${params.toString()}`;
        }

        console.log("Lien : ", url);
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des messages');
        }
        console.log("Reponse : ",response);
        const data = await response.json();
        console.log("Message recuperer : ",data);
        if (data.messages && data.messages.length > 0) {
          setMessages(prevMessages => {
            // Prevent duplicates if polling fetches messages already optimistically added
            const existingMessageIds = new Set(prevMessages.map(msg => msg.id));
            const newUniqueMessages = data.messages.filter((msg: Message) => !existingMessageIds.has(msg.id));
            return [...prevMessages, ...newUniqueMessages];
          });
          setLastMessageId(data.messages[data.messages.length - 1].id);
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les messages');

        // Utiliser des données de secours
        // setMessages([
        //   {
        //     id: 1,
        //     sender_id: isClient ? (professionalId || 0) : (clientId || 0),
        //     sender_name: isClient ? (professionalName || 'Professionnel') : (clientName || 'Client'),
        //     sender_avatar: isClient ? professionalAvatar : clientAvatar,
        //     sender_type: isClient ? 'professional' : 'client',
        //     message_text: 'Bonjour, je suis intéressé par votre appel d\'offre. Pouvez-vous me donner plus de détails sur le projet ?',
        //     created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        //   },
        //   {
        //     id: 2,
        //     sender_id: isClient ? (clientId || 0) : (professionalId || 0),
        //     sender_name: isClient ? (clientName || 'Client') : (professionalName || 'Professionnel'),
        //     sender_avatar: isClient ? clientAvatar : professionalAvatar,
        //     sender_type: isClient ? 'client' : 'professional',
        //     message_text: 'Bien sûr ! Il s\'agit d\'un projet de modélisation 3D pour un jeu vidéo. Nous avons besoin de créer 5 personnages principaux avec des animations de base.',
        //     created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
        //   },
        // ]);
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();

    // Mettre en place un polling pour les nouveaux messages (toutes les 10 secondes)
    const intervalId = setInterval(() => {
      fetchMessages(lastMessageId);
    }, 5000); // Poll more frequently, e.g., every 5 seconds

    return () => clearInterval(intervalId);
  }, [offerId, token, isClient, clientId, clientName, clientAvatar, professionalId, professionalName, professionalAvatar]);

  // Faire défiler vers le bas lorsque de nouveaux messages sont ajoutés
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    }) + ' - ' + date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
    });
  };

  // Gérer l'envoi d'un message
  const handleSendMessage = async () => {
    if (!newMessage.trim() && attachments.length === 0) return;
    if (!token || !offerId) return;

    // Créer un message temporaire pour l'affichage optimiste
    const tempMessageId = Date.now(); // Utiliser le timestamp comme ID temporaire
    const optimisticMessage: Message = {
      id: tempMessageId,
      open_offer_id: offerId,
      sender_id: currentUserId,
      receiver_id: isClient ? (professionalId || 0) : (clientId || 0), // This might need refinement depending on actual receiver logic
      message_text: newMessage,
      created_at: new Date().toISOString(), // Utiliser l'heure locale pour l'affichage immédiat
      updated_at: new Date().toISOString(),
      sender: JSON.parse(localStorage.getItem('user') || '{}') as User, // Utiliser l'utilisateur actuel comme expéditeur
      receiver: {} as User, // Placeholder, will be replaced by server data
    };

    setSending(true);

    // Ajouter le message optimiste à la liste
    setMessages(prevMessages => [...prevMessages, optimisticMessage]);

    // Réinitialiser le formulaire immédiatement
    setNewMessage('');
    setAttachments([]);

    try {
      const formData = new FormData();
      formData.append('message_text', newMessage);

      if (isClient) {
        const receiverId = String(professionalId);
        if (!receiverId) {
          alert("ID du professionnel à contacter manquant.");
          return;
        }
        formData.append('receiver_id', receiverId);
        // bodyData.receiver_id = parseInt(receiverId);
      }

      // Ajouter les pièces jointes
      attachments.forEach(file => {
        formData.append('attachments[]', file);
      });

      const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Erreur lors de l\'envoi du message');
      }

      const data = await response.json();

      // Remplacer le message optimiste par le message reçu du serveur
      setMessages(prevMessages =>
        prevMessages.map(msg => (msg.id === tempMessageId ? data.message : msg))
      );

      // Envoyer une notification à l'autre partie
      addOfferNotification('offer_message', {
        offer_id: offerId,
        offer_title: offerTitle,
        message: newMessage,
        ...(isClient
          ? { client_id: currentUserId, client_name: clientName || 'Client', client_avatar: clientAvatar }
          : { professional_id: currentUserId, professional_name: professionalName || 'Professionnel', professional_avatar: professionalAvatar }
        ),
      });
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible d\'envoyer le message');

      // Gérer l'échec de l'envoi (par exemple, marquer le message en erreur ou le supprimer)
      setMessages(prevMessages => prevMessages.filter(msg => msg.id !== tempMessageId));
      // Vous pourriez ajouter ici une logique pour afficher un message d'erreur à l'utilisateur
      alert('Impossible d\'envoyer le message.');
    } finally {
      setSending(false);
    }
  };

  // Gérer l'ajout de pièces jointes
  const handleAttachmentClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setAttachments(prevAttachments => [...prevAttachments, ...newFiles]);
    }
  };

  // Gérer la suppression d'une pièce jointe
  const handleRemoveAttachment = (index: number) => {
    setAttachments(prevAttachments => prevAttachments.filter((_, i) => i !== index));
  };

  // Gérer l'envoi avec la touche Entrée
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
      {/* Header */}
      <div className="px-4 py-3 border-b border-neutral-200 flex items-center">
        {onBack && (
          <Button
            variant="ghost"
            size="sm"
            className="mr-2"
            onClick={onBack}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
        )}
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-neutral-900">{offerTitle}</h3>
          <p className="text-sm text-neutral-500">
            Discussion avec {isClient ? professionalName : clientName}
          </p>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 p-4 overflow-y-auto bg-neutral-50">
        {loading && messages.length === 0 ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
          </div>
        ) : error && messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <p className="text-red-500 mb-2">{error}</p>
            <Button
              variant="primary"
              onClick={() => window.location.reload()}
            >
              Réessayer
            </Button>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-neutral-500">
            <User className="h-12 w-12 text-neutral-300 mb-2" />
            <p>Aucun message pour le moment</p>
            <p className="text-sm">Commencez la conversation en envoyant un message</p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message, index) => {
              const previousMessage = index > 0 ? messages[index - 1] : null;
              const isConsecutiveMessageFromSameSender = previousMessage && previousMessage.sender_id === message.sender_id;

              return (
                <div
                  key={message.id}
                  className={`flex ${message.sender.is_professional ? 'justify-end' : 'justify-start'}`}
                >
                  <div className="flex max-w-[80%]">
                    {/* Check if the sender is the receiver (i.e., the other person) and not a consecutive message */}
                    {message.sender.is_professional === !isClient && !isConsecutiveMessageFromSameSender && (
                      <Avatar
                        src={isClient ? professionalAvatar : clientAvatar}
                        fallback={message.sender.first_name.charAt(0)}
                        size="sm"
                        className="mr-2 self-end mb-1"
                      />
                    )}
                    <div>
                      <div
                        className={`rounded-lg p-3 ${isConsecutiveMessageFromSameSender ? 'mt-1' : ''} ${ // Add margin-top for consecutive messages
                          message.sender.is_professional === !isClient
                            ? 'bg-primary-100 text-primary-900'
                            : 'bg-white border border-neutral-200 text-neutral-900'
                        }`}
                      >
                        <p className="whitespace-pre-wrap break-words">{message.message_text}</p>

                        {/* Attachments */}
                        {/* {message.attachments && message.attachments.length > 0 && (
                          <div className="mt-2 space-y-1">
                            {message.attachments.map((attachment, i) => (
                              <a
                                key={i}
                                href={attachment}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center text-sm text-primary-700 hover:text-primary-900"
                              >
                                <Paperclip className="h-4 w-4 mr-1" />
                                <span className="truncate">Pièce jointe {i + 1}</span>
                                <Download className="h-4 w-4 ml-1" />
                              </a>
                            ))}
                          </div>
                        )} */}
                      </div>
                      <div className="mt-1 flex items-center text-xs text-neutral-500">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{formatDate(message.created_at)}</span>
                      </div>
                    </div>
                    {/* Check if the sender is the current user and not a consecutive message */}
                    {message.sender.is_professional !== !isClient && !isConsecutiveMessageFromSameSender && (
                      <Avatar
                        src={isClient ? clientAvatar : professionalAvatar}
                        fallback={message.sender.first_name.charAt(0)}
                        size="sm"
                        className="ml-2 self-end mb-1"
                      />
                    )}
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Attachments preview */}
      {attachments.length > 0 && (
        <div className="px-4 py-2 border-t border-neutral-200 bg-neutral-50">
          <div className="flex flex-wrap gap-2">
            {attachments.map((file, index) => (
              <div
                key={index}
                className="flex items-center bg-white rounded-md border border-neutral-200 px-2 py-1"
              >
                <Paperclip className="h-4 w-4 text-neutral-500 mr-1" />
                <span className="text-sm truncate max-w-[150px]">{file.name}</span>
                <button
                  className="ml-1 text-neutral-500 hover:text-red-500"
                  onClick={() => handleRemoveAttachment(index)}
                >
                  &times;
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t border-neutral-200">
        <div className="flex items-end">
          <div className="flex-1 mr-2">
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Écrivez votre message..."
              className="w-full border border-neutral-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
              rows={2}
            />
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleAttachmentClick}
            >
              <Paperclip className="h-5 w-5" />
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={handleSendMessage}
              disabled={sending || (!newMessage.trim() && attachments.length === 0)}
              style={{ backgroundColor: '#2980b9', color: 'black' }}
            >
              <Send className="h-5 w-5" />
            </Button>
          </div>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            multiple
            className="hidden"
          />
        </div>
      </div>
    </div>
  );
};

export default OfferDiscussionPanel;
