import React, { useRef, useEffect } from 'react';
import <PERSON><PERSON><PERSON>eader from './ChatHeader';
import ChatMessage, { MessageProps } from './ChatMessage';
import MessageInput from './MessageInput';

interface ChatWindowProps {
  chatId: number;
  title: string;
  subtitle?: string;
  avatar?: string;
  status?: 'online' | 'offline' | 'away';
  messages: MessageProps[];
  isLoading?: boolean;
  onSendMessage: (message: string) => void;
  onBack?: () => void;
}

const ChatWindow: React.FC<ChatWindowProps> = ({
  chatId,
  title,
  subtitle,
  avatar,
  status,
  messages,
  isLoading = false,
  onSendMessage,
  onBack,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Group messages by date
  const groupedMessages = messages.reduce((groups, message) => {
    const date = new Date(message.timestamp).toLocaleDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(message);
    return groups;
  }, {} as Record<string, MessageProps[]>);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return "Aujourd'hui";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Hier';
    } else {
      return date.toLocaleDateString('fr-FR', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
      });
    }
  };

  return (
    <div className="flex flex-col h-full bg-white">
      <ChatHeader
        title={title}
        subtitle={subtitle}
        avatar={avatar}
        status={status}
        onBack={onBack}
      />
      
      <div className="flex-1 overflow-y-auto p-4 bg-neutral-50">
        {isLoading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-neutral-500 p-4 text-center">
            <p>Aucun message pour le moment</p>
            <p className="text-sm mt-1">Commencez la conversation en envoyant un message</p>
          </div>
        ) : (
          Object.entries(groupedMessages).map(([date, dateMessages]) => (
            <div key={date}>
              <div className="flex justify-center my-4">
                <span className="text-xs bg-neutral-200 text-neutral-600 px-3 py-1 rounded-full">
                  {formatDate(date)}
                </span>
              </div>
              
              {dateMessages.map((message) => (
                <ChatMessage
                  key={message.id}
                  {...message}
                />
              ))}
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
      
      <MessageInput
        onSendMessage={onSendMessage}
        isLoading={isLoading}
      />
    </div>
  );
};

export default ChatWindow;
