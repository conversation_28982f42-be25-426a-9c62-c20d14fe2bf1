import React, { useEffect, useState } from "react";
import { API_BASE_URL } from "../../config";
import Header from "../Header";
import Footer from "../Footer";
import { useLocation } from "react-router-dom";

export interface OpenOffer {
  id: number;
  user_id: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company: string;
  website: string;
  description: string;
  files: string | null;
  recruitment_type: "company" | "freelance" | string;
  open_to_applications: boolean;
  auto_invite: boolean;
  status: "open" | "closed" | "pending" | "completed" | string;
  views_count: number;
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string;
    is_professional: boolean;
    created_at: string;
    updated_at: string;
    profile_completed: boolean;
  };
  applications: Application[];
}

export interface Application {
  id: number;
  open_offer_id: number;
  proposal: string | null;
  status: "pending" | "accepted" | "rejected" | "invited" | string;
  created_at: string;
  updated_at: string;
  professional_profile_id: number;
  freelance_profile: FreelanceProfile;
}

export interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  avatar: string;
  portfolio_items: any[] | null;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  title: string | null;
  expertise: string | null;
  completion_percentage: number;
  profession: string;
  years_of_experience: number;
  hourly_rate: string;
  description: string | null;
  availability_status: "available" | "unavailable" | string;
  estimated_response_time: string | null;
  rating: string;
  skills: string[];
  languages: string[];
  services_offered: any[];
  portfolio: PortfolioItem[];
  social_links: any[];
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string;
    is_professional: boolean;
    created_at: string;
    updated_at: string;
    profile_completed: boolean;
  };
}

export interface PortfolioItem {
  id: string;
  path: string;
  name: string;
  type: string;
  created_at: string;
}

type User = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
};

type Message = {
  id: number;
  open_offer_id: number;
  sender_id: number;
  receiver_id: number;
  message_text: string;
  created_at: string;
  updated_at: string;
  sender: User;
  receiver: User;
};

const MessagesPage = () => {
  const { state } = useLocation();
  const offre = state?.offreEncours;
  const [conversations, setConversations] = useState<Message[][]>([]);
  const [selectedConversation, setSelectedConversation] = useState<
    Message[] | null
  >(null);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sending, setSending] = useState(false);
  const [showFreelanceList, setShowFreelanceList] = useState(false);
  const [lastMessageTimestamp, setLastMessageTimestamp] = useState<string>("");
  const [pendingMessageId, setPendingMessageId] = useState<number | null>(null);
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const token = localStorage.getItem("token");

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        const response = await fetch(
          `${API_BASE_URL}/api/messages/conversation/${user.id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error("Erreur lors de la récupération des conversations");
        }

        const result = await response.json();
        setConversations(result.data as Message[][]);

        if (result.data.length > 0) {
          setSelectedConversation(result.data[0]);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchConversations();
  }, [user.id]);

  const scrollToBottom = () => {
    const messagesContainer = document.querySelector('[data-messages-container]');
    if (messagesContainer) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  };

  // Défiler vers le bas quand de nouveaux messages arrivent
  useEffect(() => {
    scrollToBottom();
  }, [selectedConversation]);

  const handleSendMessage = async () => {
    if (!selectedConversation || newMessage.trim() === "" || isSending) return;

    const otherParticipant = selectedConversation.find(
      (msg) => msg && msg.sender_id !== user.id
    );

    if (!otherParticipant) {
      alert("Impossible de déterminer le destinataire.");
      return;
    }

    const lastConversation = selectedConversation[selectedConversation.length - 1];
    const offerId = lastConversation?.open_offer_id;

    if (!offerId) {
      alert("Erreur: Impossible d'identifier l'offre associée");
      return;
    }

    // Création du message optimiste
    const tempMessageId = Date.now();
    setPendingMessageId(tempMessageId);
    setIsSending(true);
    
    const optimisticMessage: Message = {
      id: tempMessageId,
      open_offer_id: offerId,
      sender_id: user.id,
      receiver_id: otherParticipant.sender_id,
      message_text: newMessage,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sender: user,
      receiver: otherParticipant.sender || {
        id: otherParticipant.sender_id,
        first_name: "Chargement...",
        last_name: "",
        email: "",
      },
    };

    // Mise à jour immédiate de l'état local
    const updatedConversation = [...selectedConversation, optimisticMessage];
    setSelectedConversation(updatedConversation);
    setConversations((prevConversations) =>
      prevConversations.map((conv) =>
        conv === selectedConversation ? updatedConversation : conv
      )
    );
    setNewMessage("");
    scrollToBottom();

    try {
      const formData = new FormData();
      formData.append("message_text", newMessage);
      if (!user.is_professional) {
        formData.append("receiver_id", String(otherParticipant.sender_id));
      }

      const response = await fetch(
        `${API_BASE_URL}/api/open-offers/${offerId}/messages`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        }
      );

      if (!response.ok) throw new Error("Erreur serveur");

      window.location.reload();

      const result = await response.json();
      const serverMessage = result.data;

      // Mise à jour avec le message du serveur tout en conservant le message optimiste
      setSelectedConversation((prev) => {
        if (!prev) return prev;
        const updatedMessages = prev.map((msg) => 
          msg.id === tempMessageId ? { ...serverMessage, isOptimistic: false } : msg
        );
        return updatedMessages;
      });

      setConversations((prevConversations) =>
        prevConversations.map((conv) => {
          if (!conv) return conv;
          return conv.map((msg) => 
            msg.id === tempMessageId ? { ...serverMessage, isOptimistic: false } : msg
          );
        })
      );
    } catch (error) {
      console.error("Échec de l'envoi:", error);
      // Marquage du message comme échoué
      setSelectedConversation((prev) => {
        if (!prev) return prev;
        return prev.map((msg) =>
          msg.id === tempMessageId ? { ...msg, failed: true } : msg
        );
      });
    } finally {
      setIsSending(false);
      setPendingMessageId(null);
    }
  };

  const handleNewConversation = () => {
    setShowFreelanceList(true);
    // alert('Rediriger vers une interface de création ou ouvrir un modal.');
    // TODO : Implémenter la redirection ou ouverture de modal
  };

  const handleSelectFreelance = (freelance: FreelanceProfile) => {
    const existing = conversations.find((conv) =>
      conv.some(
        (msg) =>
          (msg.sender_id === freelance.user_id &&
            msg.receiver_id === user.id) ||
          (msg.sender_id === user.id && msg.receiver_id === freelance.user_id)
      )
    );

    if (existing) {
      setSelectedConversation(existing);
      setShowFreelanceList(false);
    } else {
      // Créer une nouvelle conversation vide (dans l'état seulement, pas côté serveur)
      const newConv: Message[] = [
        {
          id: Date.now(), // juste un ID temporaire
          open_offer_id: offre.id,
          sender_id: freelance.user.id,
          receiver_id: user.id,
          message_text: "",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          sender: user,
          receiver: freelance.user,
        },
      ];

      setConversations((prev) => [...prev, newConv]);
      setSelectedConversation(newConv);
      setShowFreelanceList(false);
    }
  };

  // Fonction pour récupérer les nouveaux messages
  const fetchNewMessages = async () => {
    if (!selectedConversation || selectedConversation.length === 0 || isSending) return;

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/messages/conversation/${user.id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) return;

      const result = await response.json();
      const updatedConversations = result.data as Message[][];

      // Trouver la conversation actuelle dans les nouvelles données
      const currentUpdatedConversation = updatedConversations.find(
        (conv) =>
          conv &&
          conv.length > 0 &&
          conv[0]?.open_offer_id === selectedConversation[0]?.open_offer_id
      );

      if (currentUpdatedConversation) {
        setSelectedConversation((prev) => {
          if (!prev) return currentUpdatedConversation;

          // Garder les messages optimistes en cours d'envoi
          const optimisticMessages = prev.filter(
            (msg) => msg && msg.id > 1000000000 && msg.id === pendingMessageId
          );

          // Fusionner en gardant les messages optimistes et les messages du serveur
          const merged = [...currentUpdatedConversation, ...optimisticMessages].sort(
            (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          );

          return merged;
        });

        // Mettre à jour la liste des conversations de la même manière
        setConversations((prevConversations) =>
          prevConversations.map((conv) => {
            if (!conv || conv.length === 0) return conv;

            const matchingUpdatedConv = updatedConversations.find(
              (updatedConv) =>
                updatedConv &&
                updatedConv.length > 0 &&
                updatedConv[0]?.open_offer_id === conv[0]?.open_offer_id
            );

            if (!matchingUpdatedConv) return conv;

            // Garder les messages optimistes en cours d'envoi
            const optimisticMessages = conv.filter(
              (msg) => msg && msg.id > 1000000000 && msg.id === pendingMessageId
            );

            return [...matchingUpdatedConv, ...optimisticMessages].sort(
              (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
            );
          })
        );
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des nouveaux messages:", error);
    }
  };

  // Mettre à jour le timestamp du dernier message quand une conversation est sélectionnée
  useEffect(() => {
    if (selectedConversation && selectedConversation.length > 0) {
      const lastMessage = selectedConversation[selectedConversation.length - 1];
      if (lastMessage?.created_at) {
        setLastMessageTimestamp(lastMessage.created_at);
      }
    }
  }, [selectedConversation]);

  // Configurer le polling pour les nouveaux messages
  useEffect(() => {
    if (!selectedConversation) return;

    const pollInterval = setInterval(() => {
      if (!isSending) {
        fetchNewMessages();
      }
    }, 3000);

    return () => clearInterval(pollInterval);
  }, [selectedConversation, isSending]);

  if (loading)
    return (
      <div className="flex flex-col min-h-screen bg-neutral-50">
        <Header />
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
        <Footer />
      </div>
    );
  if (error) return <p style={{ color: "red" }}>{error}</p>;

  return (
    <div className="flex flex-col min-h-screen bg-neutral-50">
      <Header />
      <div style={styles.container}>
        <div style={styles.sidebar}>
          <h3>Conversations</h3>
          <div style={{ flex: 1, overflowY: "auto" }}>
            {conversations.length === 0 ? (
              <p>Aucune conversation</p>
            ) : (
              conversations.map((conv, idx) => {
                if (!conv || conv.length === 0) return null;
                const lastMsg = conv[conv.length - 1];
                if (!lastMsg) return null;

                const otherUser =
                  lastMsg.sender_id === user.id
                    ? lastMsg.receiver
                    : lastMsg.sender;
                if (!otherUser) return null;

                return (
                  <div
                    key={idx}
                    style={{
                      ...styles.conversationItem,
                      backgroundColor:
                        selectedConversation === conv ? "#e6f7ff" : "#fff",
                    }}
                    onClick={() => setSelectedConversation(conv)}
                  >
                    <strong>
                      {otherUser.first_name} {otherUser.last_name}
                    </strong>
                    <p style={{ margin: "5px 0" }}>
                      {lastMsg.message_text
                        ? lastMsg.message_text.slice(0, 30) + "..."
                        : ""}
                    </p>
                  </div>
                );
              })
            )}
          </div>

          {offre && (
            <button
              onClick={handleNewConversation}
              style={styles.newConversationButton}
            >
              + Nouvelle conversation
            </button>
          )}
          {/* <button onClick={handleNewConversation} style={styles.newConversationButton}>
        + Nouvelle conversation
        </button> */}
        </div>

        {/* {showFreelanceList && offre && (
      <div style={{ padding: 10, background: '#f9f9f9' }}>
        <h4>Choisissez un professionnel pour démarrer une conversation</h4>
        {offre.applications.map((app:Application) => (
          <div
            key={app.id}
            style={{ padding: 10, border: '1px solid #ccc', borderRadius: 5, marginBottom: 10, cursor: 'pointer' }}
            onClick={() => handleSelectFreelance(app.freelance_profile)}
          >
            <strong>{app.freelance_profile.first_name} {app.freelance_profile.last_name}</strong>
            <p>{app.freelance_profile.profession}</p>
          </div>
        ))}
      </div>
    )} */}

        <div style={styles.chatWindow}>
          {selectedConversation ? (
            <>
              <div 
                style={{
                  ...styles.messages,
                  overflowY: 'auto',
                  maxHeight: 'calc(100vh - 200px)',
                }}
                data-messages-container
              >
                {selectedConversation &&
                  selectedConversation.map((msg) => {
                    if (!msg || !msg.sender) return null;

                    return (
                      <div
                        key={msg.id}
                        style={{
                          ...styles.message,
                          alignSelf:
                            msg.sender_id === user.id
                              ? "flex-end"
                              : "flex-start",
                          backgroundColor:
                            msg.sender_id === user.id ? "#DCF8C6" : "#F1F0F0",
                        }}
                      >
                        <p style={styles.sender}>
                          {msg.sender.first_name} {msg.sender.last_name}
                        </p>
                        <p>{msg.message_text || ""}</p>
                        <span style={styles.timestamp}>
                          {msg.created_at
                            ? new Date(msg.created_at).toLocaleString()
                            : ""}
                        </span>
                      </div>
                    );
                  })}
              </div>
              <div style={styles.inputArea}>
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Écrivez un message..."
                  style={styles.input}
                />
                <button
                  onClick={handleSendMessage}
                  style={styles.sendButton}
                  disabled={isSending}
                >
                  {isSending ? "Envoi..." : "Envoyer"}
                </button>
              </div>
            </>
          ) : (
            <p>Sélectionnez une conversation</p>
          )}
        </div>
      </div>

      {showFreelanceList && offre && (
        <div style={styles.modalOverlay}>
          <div style={styles.modal}>
            <h3 style={{ marginBottom: 20 }}>Choisissez un professionnel</h3>
            <div style={{ maxHeight: "60vh", overflowY: "auto" }}>
              {offre.applications.map((app: Application) => (
                <div
                  key={app.id}
                  style={styles.freelancerCard}
                  onClick={() => handleSelectFreelance(app.freelance_profile)}
                >
                  <strong>
                    {app.freelance_profile.first_name}{" "}
                    {app.freelance_profile.last_name}
                  </strong>
                  <p style={{ margin: 0 }}>
                    {app.freelance_profile.profession}
                  </p>
                </div>
              ))}
            </div>
            <button
              onClick={() => setShowFreelanceList(false)}
              style={styles.closeButton}
            >
              Fermer
            </button>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};

const styles: { [key: string]: React.CSSProperties } = {
  container: {
    display: "flex",
    height: "80vh",
    fontFamily: "Arial, sans-serif",
  },
  sidebar: {
    width: "30%",
    borderRight: "1px solid #ccc",
    padding: 20,
    overflowY: "auto",
  },
  conversationItem: {
    padding: 10,
    marginBottom: 10,
    border: "1px solid #ddd",
    borderRadius: 5,
    cursor: "pointer",
  },
  chatWindow: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    padding: 20,
  },
  messages: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    overflowY: "auto",
    marginBottom: 20,
  },
  message: {
    maxWidth: "60%",
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
    boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
  },
  sender: {
    fontWeight: "bold",
    marginBottom: 5,
  },
  timestamp: {
    fontSize: "0.8em",
    color: "#888",
    marginTop: 5,
    display: "block",
  },
  inputArea: {
    display: "flex",
    borderTop: "1px solid #ccc",
    paddingTop: 10,
  },
  input: {
    flex: 1,
    padding: 10,
    borderRadius: 5,
    border: "1px solid #ccc",
    marginRight: 10,
  },
  sendButton: {
    padding: "10px 20px",
    backgroundColor: "#007bff",
    color: "#fff",
    border: "none",
    borderRadius: 5,
    cursor: "pointer",
  },
  newConversationButton: {
    marginTop: 20,
    padding: "10px 15px",
    fontSize: 14,
    borderRadius: 5,
    backgroundColor: "#007bff",
    color: "#fff",
    border: "none",
    cursor: "pointer",
    width: "100%",
  },
  inputContainer: {
    display: "flex",
    marginTop: 10,
    gap: 10,
  },

  modalOverlay: {
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },

  modal: {
    backgroundColor: "#fff",
    padding: 30,
    borderRadius: 10,
    width: "90%",
    maxWidth: 500,
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
    position: "relative",
  },

  freelancerCard: {
    padding: 12,
    border: "1px solid #ddd",
    borderRadius: 6,
    marginBottom: 10,
    cursor: "pointer",
    transition: "background-color 0.2s",
  },

  closeButton: {
    marginTop: 20,
    padding: "10px 15px",
    border: "none",
    backgroundColor: "#007bff",
    color: "#fff",
    borderRadius: 5,
    cursor: "pointer",
  },

  // input: {
  //   flex: 1,
  //   padding: 10,
  //   borderRadius: 5,
  //   border: '1px solid #ccc',
  // },
  // sendButton: {
  //   padding: '10px 20px',
  //   borderRadius: 5,
  //   border: 'none',
  //   backgroundColor: '#007bff',
  //   color: 'white',
  //   cursor: 'pointer',
  // },
};

export default MessagesPage;
