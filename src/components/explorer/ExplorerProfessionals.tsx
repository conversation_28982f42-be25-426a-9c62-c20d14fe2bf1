import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../../config';
import {
  Search,
  MapPin,
  Star,
  Filter,
  ChevronDown,
  ChevronUp,
  Award,
  Briefcase,
} from 'lucide-react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { CardBody } from '../ui/Card';
import Pagination from '../ui/Pagination';

interface Achievement {
  id: number;
  title: string;
  organization: string;
  date_obtained: string;
  description: string;
  file_path: string | null;
  achievement_url: string | null;
}

interface Service {
  id: number;
  title: string;
  description: string;
  price: number;
  execution_time: string;
  categories: string[];
  files?: Array<{
    path: string;
    original_name: string;
    mime_type: string;
    size: number;
  }>;
}

interface Professional {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string | null;
  city: string | null;
  country: string | null;
  skills: string[];
  availability_status: string;
  hourly_rate: number | null;
  avatar: string | null;
  profile_picture_path: string | null;
  rating: number | null;
  bio: string | null;
  title: string | null;
  achievements: Achievement[];
  services: Service[];
}

interface PaginationInfo {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
}

const ExplorerProfessionals: React.FC = () => {
  const navigate = useNavigate();
  const [professionals, setProfessionals] = useState<Professional[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    per_page: 10,
    current_page: 1,
    last_page: 1,
  });
  
  // Filtres
  const [search, setSearch] = useState<string>('');
  const [skills, setSkills] = useState<string>('');
  const [city, setCity] = useState<string>('');
  const [country, setCountry] = useState<string>('');
  const [minRate, setMinRate] = useState<string>('');
  const [maxRate, setMaxRate] = useState<string>('');
  const [availability, setAvailability] = useState<string>('');
  const [showFilters, setShowFilters] = useState<boolean>(false);
  
  useEffect(() => {
    fetchProfessionals(1);
  }, []);
  
  const fetchProfessionals = async (page: number) => {
    try {
      setLoading(true);
      
      // Construire l'URL avec les paramètres de filtrage
      let url = `${API_BASE_URL}/api/explorer/professionals?page=${page}`;
      
      if (search) url += `&search=${encodeURIComponent(search)}`;
      if (skills) url += `&skills=${encodeURIComponent(skills)}`;
      if (city) url += `&city=${encodeURIComponent(city)}`;
      if (country) url += `&country=${encodeURIComponent(country)}`;
      if (minRate) url += `&min_rate=${encodeURIComponent(minRate)}`;
      if (maxRate) url += `&max_rate=${encodeURIComponent(maxRate)}`;
      if (availability) url += `&availability=${encodeURIComponent(availability)}`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des professionnels');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setProfessionals(data.professionals);
        setPagination(data.pagination);
      } else {
        setError(data.message || 'Erreur lors de la récupération des professionnels');
      }
    } catch (error) {
      console.error('Erreur:', error);
      setError('Erreur lors de la récupération des professionnels. Veuillez réessayer plus tard.');
    } finally {
      setLoading(false);
    }
  };
  
  const handlePageChange = (page: number) => {
    fetchProfessionals(page);
  };
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchProfessionals(1);
  };
  
  const handleResetFilters = () => {
    setSearch('');
    setSkills('');
    setCity('');
    setCountry('');
    setMinRate('');
    setMaxRate('');
    setAvailability('');
    fetchProfessionals(1);
  };
  
  const handleViewProfile = (id: number) => {
    navigate(`/professionals/${id}`);
  };
  
  const getImageUrl = (path: string | null) => {
    if (!path) return '/placeholder-avatar.jpg';
    if (path.startsWith('http')) return path;
    return `${API_BASE_URL}/storage/${path}`;
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Explorateur de Professionnels</h1>
      
      {/* Barre de recherche */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="Rechercher un professionnel..."
              className="w-full px-4 py-2 border border-gray-300 rounded-md pl-10"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
          
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center"
          >
            <Filter className="h-5 w-5 mr-2" />
            Filtres
            {showFilters ? (
              <ChevronUp className="h-4 w-4 ml-2" />
            ) : (
              <ChevronDown className="h-4 w-4 ml-2" />
            )}
          </Button>
          
          <Button type="submit" variant="primary">
            Rechercher
          </Button>
        </form>
      </div>
      
      {/* Filtres avancés */}
      {showFilters && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Compétences
              </label>
              <input
                type="text"
                placeholder="ex: 3D, Animation, Modélisation"
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={skills}
                onChange={(e) => setSkills(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ville
              </label>
              <input
                type="text"
                placeholder="ex: Paris, Lyon, Marseille"
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={city}
                onChange={(e) => setCity(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Pays
              </label>
              <input
                type="text"
                placeholder="ex: France, Belgique, Suisse"
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={country}
                onChange={(e) => setCountry(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tarif horaire minimum
              </label>
              <input
                type="number"
                placeholder="ex: 20"
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={minRate}
                onChange={(e) => setMinRate(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tarif horaire maximum
              </label>
              <input
                type="number"
                placeholder="ex: 100"
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={maxRate}
                onChange={(e) => setMaxRate(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Disponibilité
              </label>
              <select
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={availability}
                onChange={(e) => setAvailability(e.target.value)}
              >
                <option value="">Tous</option>
                <option value="available">Disponible</option>
                <option value="unavailable">Non disponible</option>
                <option value="busy">Occupé</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={handleResetFilters}
              className="mr-2"
            >
              Réinitialiser
            </Button>
            <Button
              type="button"
              variant="primary"
              onClick={() => fetchProfessionals(1)}
            >
              Appliquer
            </Button>
          </div>
        </div>
      )}
      
      {/* Message d'erreur */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}
      
      {/* Chargement */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : (
        <>
          {/* Liste des professionnels */}
          {professionals.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-600">Aucun professionnel trouvé.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {professionals.map((professional) => (
                <Card key={professional.id} className="h-full">
                  <CardBody className="p-6 flex flex-col h-full">
                    <div className="flex items-start mb-4">
                      <img
                        src={getImageUrl(professional.avatar)}
                        alt={`${professional.first_name} ${professional.last_name}`}
                        className="w-16 h-16 rounded-full object-cover mr-4"
                      />
                      <div>
                        <h3 className="text-lg font-semibold">
                          {professional.first_name} {professional.last_name}
                        </h3>
                        <p className="text-gray-600">{professional.title || 'Professionnel'}</p>
                        {professional.city && professional.country && (
                          <div className="flex items-center text-gray-500 text-sm mt-1">
                            <MapPin className="h-4 w-4 mr-1" />
                            <span>
                              {professional.city}, {professional.country}
                            </span>
                          </div>
                        )}
                        {professional.rating && (
                          <div className="flex items-center text-yellow-500 mt-1">
                            <Star className="h-4 w-4 mr-1 fill-current" />
                            <span>{professional.rating.toFixed(1)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="mb-4 flex-1">
                      <p className="text-gray-700 line-clamp-3">
                        {professional.bio || 'Aucune biographie disponible.'}
                      </p>
                    </div>
                    
                    <div className="mb-4">
                      {professional.skills && professional.skills.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {professional.skills.slice(0, 3).map((skill, index) => (
                            <span
                              key={index}
                              className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"
                            >
                              {skill}
                            </span>
                          ))}
                          {professional.skills.length > 3 && (
                            <span className="text-gray-500 text-xs px-2 py-1">
                              +{professional.skills.length - 3}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                      <div className="flex items-center">
                        <Award className="h-4 w-4 mr-1" />
                        <span>{professional.achievements?.length || 0} réalisations</span>
                      </div>
                      <div className="flex items-center">
                        <Briefcase className="h-4 w-4 mr-1" />
                        <span>{professional.services?.length || 0} services</span>
                      </div>
                    </div>
                    
                    <div className="mt-auto">
                      <Button
                        variant="primary"
                        fullWidth
                        onClick={() => handleViewProfile(professional.id)}
                      >
                        Voir le profil
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
          
          {/* Pagination */}
          {pagination.last_page > 1 && (
            <Pagination
              currentPage={pagination.current_page}
              totalPages={pagination.last_page}
              onPageChange={handlePageChange}
            />
          )}
        </>
      )}
    </div>
  );
};

export default ExplorerProfessionals;
