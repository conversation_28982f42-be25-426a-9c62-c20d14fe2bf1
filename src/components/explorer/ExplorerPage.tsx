import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '../ui/TabsNew';
import { Users, Briefcase } from 'lucide-react';
import Button from '../ui/Button';

const ExplorerPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('professionals');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Explorateur</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Découvrez les meilleurs professionnels 3D et leurs services pour réaliser vos projets
        </p>
      </div>

      <Tabs defaultValue="professionals" onValueChange={setActiveTab}>
        <div className="flex justify-center mb-8">
          <TabsList className="grid grid-cols-2 w-full max-w-md">
            <TabsTrigger value="professionals" className="flex items-center justify-center">
              <Users className="h-5 w-5 mr-2" />
              Professionnels
            </TabsTrigger>
            <TabsTrigger value="services" className="flex items-center justify-center">
              <Briefcase className="h-5 w-5 mr-2" />
              Services
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="professionals">
          <div className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Explorateur de Professionnels</h2>
            <p className="text-gray-600 mb-4">
              Cette fonctionnalité est en cours de développement. Vous pourrez bientôt explorer tous les professionnels disponibles.
            </p>
            <div className="flex justify-center">
              <Button variant="primary">Voir tous les professionnels</Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="services">
          <div className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Explorateur de Services</h2>
            <p className="text-gray-600 mb-4">
              Cette fonctionnalité est en cours de développement. Vous pourrez bientôt explorer tous les services disponibles.
            </p>
            <div className="flex justify-center">
              <Button variant="primary">Voir tous les services</Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ExplorerPage;
