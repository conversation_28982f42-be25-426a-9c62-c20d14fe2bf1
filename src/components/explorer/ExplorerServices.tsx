import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../../config';
import {
  Search,
  Filter,
  ChevronDown,
  ChevronUp,
  Clock,
  DollarSign,
  Star,
  User,
} from 'lucide-react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { CardBody } from '../ui/Card';
import Pagination from '../ui/Pagination';

interface Professional {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  avatar: string | null;
  title: string | null;
  rating: number | null;
  achievements_count: number;
}

interface Service {
  id: number;
  title: string;
  description: string;
  price: number;
  execution_time: string;
  concepts: string;
  revisions: string;
  categories: string[];
  files?: Array<{
    path: string;
    original_name: string;
    mime_type: string;
    size: number;
  }>;
  views: number;
  likes: number;
  rating: number | null;
  created_at: string;
  updated_at: string;
  professional: Professional | null;
}

interface PaginationInfo {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
}

const ExplorerServices: React.FC = () => {
  const navigate = useNavigate();
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    per_page: 10,
    current_page: 1,
    last_page: 1,
  });
  
  // Filtres
  const [search, setSearch] = useState<string>('');
  const [category, setCategory] = useState<string>('');
  const [minPrice, setMinPrice] = useState<string>('');
  const [maxPrice, setMaxPrice] = useState<string>('');
  const [executionTime, setExecutionTime] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('newest');
  const [showFilters, setShowFilters] = useState<boolean>(false);
  
  useEffect(() => {
    fetchServices(1);
  }, []);
  
  const fetchServices = async (page: number) => {
    try {
      setLoading(true);
      
      // Construire l'URL avec les paramètres de filtrage
      let url = `${API_BASE_URL}/api/explorer/services?page=${page}`;
      
      if (search) url += `&search=${encodeURIComponent(search)}`;
      if (category) url += `&category=${encodeURIComponent(category)}`;
      if (minPrice) url += `&min_price=${encodeURIComponent(minPrice)}`;
      if (maxPrice) url += `&max_price=${encodeURIComponent(maxPrice)}`;
      if (executionTime) url += `&execution_time=${encodeURIComponent(executionTime)}`;
      if (sortBy) url += `&sort_by=${encodeURIComponent(sortBy)}`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des services');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setServices(data.services);
        setPagination(data.pagination);
      } else {
        setError(data.message || 'Erreur lors de la récupération des services');
      }
    } catch (error) {
      console.error('Erreur:', error);
      setError('Erreur lors de la récupération des services. Veuillez réessayer plus tard.');
    } finally {
      setLoading(false);
    }
  };
  
  const handlePageChange = (page: number) => {
    fetchServices(page);
  };
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchServices(1);
  };
  
  const handleResetFilters = () => {
    setSearch('');
    setCategory('');
    setMinPrice('');
    setMaxPrice('');
    setExecutionTime('');
    setSortBy('newest');
    fetchServices(1);
  };
  
  const handleViewService = (id: number) => {
    navigate(`/services/${id}`);
  };
  
  const handleViewProfessional = (id: number) => {
    navigate(`/professionals/${id}`);
  };
  
  const getImageUrl = (service: Service) => {
    if (service.files && service.files.length > 0) {
      const file = service.files[0];
      if (file.mime_type && file.mime_type.startsWith('image/')) {
        return `${API_BASE_URL}/storage/${file.path}`;
      }
    }
    return '/placeholder-service.jpg';
  };
  
  const getAvatarUrl = (path: string | null) => {
    if (!path) return '/placeholder-avatar.jpg';
    if (path.startsWith('http')) return path;
    return `${API_BASE_URL}/storage/${path}`;
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Explorateur de Services</h1>
      
      {/* Barre de recherche */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="Rechercher un service..."
              className="w-full px-4 py-2 border border-gray-300 rounded-md pl-10"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
          
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center"
          >
            <Filter className="h-5 w-5 mr-2" />
            Filtres
            {showFilters ? (
              <ChevronUp className="h-4 w-4 ml-2" />
            ) : (
              <ChevronDown className="h-4 w-4 ml-2" />
            )}
          </Button>
          
          <Button type="submit" variant="primary">
            Rechercher
          </Button>
        </form>
      </div>
      
      {/* Filtres avancés */}
      {showFilters && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Catégorie
              </label>
              <select
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
              >
                <option value="">Toutes les catégories</option>
                <option value="3D">3D</option>
                <option value="Animation">Animation</option>
                <option value="Modélisation">Modélisation</option>
                <option value="Rendu">Rendu</option>
                <option value="Texturing">Texturing</option>
                <option value="Rigging">Rigging</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Prix minimum
              </label>
              <input
                type="number"
                placeholder="ex: 50"
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={minPrice}
                onChange={(e) => setMinPrice(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Prix maximum
              </label>
              <input
                type="number"
                placeholder="ex: 500"
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={maxPrice}
                onChange={(e) => setMaxPrice(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Temps d'exécution
              </label>
              <select
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={executionTime}
                onChange={(e) => setExecutionTime(e.target.value)}
              >
                <option value="">Tous</option>
                <option value="express">Express (1-3 jours)</option>
                <option value="standard">Standard (1-2 semaines)</option>
                <option value="extended">Étendu (plus de 2 semaines)</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Trier par
              </label>
              <select
                className="w-full px-4 py-2 border border-gray-300 rounded-md"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="newest">Plus récents</option>
                <option value="rating">Mieux notés</option>
                <option value="price_asc">Prix croissant</option>
                <option value="price_desc">Prix décroissant</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={handleResetFilters}
              className="mr-2"
            >
              Réinitialiser
            </Button>
            <Button
              type="button"
              variant="primary"
              onClick={() => fetchServices(1)}
            >
              Appliquer
            </Button>
          </div>
        </div>
      )}
      
      {/* Message d'erreur */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}
      
      {/* Chargement */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : (
        <>
          {/* Liste des services */}
          {services.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-600">Aucun service trouvé.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {services.map((service) => (
                <Card key={service.id} className="h-full">
                  <div className="h-48 overflow-hidden">
                    <img
                      src={getImageUrl(service)}
                      alt={service.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardBody className="p-6 flex flex-col h-full">
                    <h3 className="text-lg font-semibold mb-2">{service.title}</h3>
                    
                    <div className="mb-4 flex-1">
                      <p className="text-gray-700 line-clamp-3">
                        {service.description}
                      </p>
                    </div>
                    
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center text-primary-600 font-semibold">
                        <DollarSign className="h-4 w-4 mr-1" />
                        <span>{service.price} €</span>
                      </div>
                      <div className="flex items-center text-gray-600 text-sm">
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{service.execution_time}</span>
                      </div>
                    </div>
                    
                    {service.professional && (
                      <div 
                        className="flex items-center mb-4 cursor-pointer hover:bg-gray-50 p-2 rounded-md"
                        onClick={() => handleViewProfessional(service.professional!.id)}
                      >
                        <img
                          src={getAvatarUrl(service.professional.avatar)}
                          alt={`${service.professional.first_name} ${service.professional.last_name}`}
                          className="w-8 h-8 rounded-full object-cover mr-2"
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {service.professional.first_name} {service.professional.last_name}
                          </p>
                          {service.professional.rating && (
                            <div className="flex items-center text-yellow-500 text-xs">
                              <Star className="h-3 w-3 mr-1 fill-current" />
                              <span>{service.professional.rating.toFixed(1)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    
                    <div className="mt-auto">
                      <Button
                        variant="primary"
                        fullWidth
                        onClick={() => handleViewService(service.id)}
                      >
                        Voir le service
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
          
          {/* Pagination */}
          {pagination.last_page > 1 && (
            <Pagination
              currentPage={pagination.current_page}
              totalPages={pagination.last_page}
              onPageChange={handlePageChange}
            />
          )}
        </>
      )}
    </div>
  );
};

export default ExplorerServices;
