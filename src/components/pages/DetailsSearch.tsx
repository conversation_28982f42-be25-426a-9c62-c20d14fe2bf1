import React, { useState, useEffect } from "react";
import { useLocation  } from "react-router-dom";
import Header from '../Header';
import Footer from '../Footer';
import {
  Briefcase
} from 'lucide-react';
import Button from '../ui/Button';
import { useParams, useNavigate } from 'react-router-dom';
import {getAllCategories} from '../../data/categories'

const DetailsSearch = () => {
  const { state } = useLocation();
  const project = state?.project||state?.service;
  const navigate = useNavigate();
    // const [searchParams] = useSearchParams();
    // const id = searchParams.get("id");
    // const type = searchParams.get("type");

    const [loading, setLoading] = useState(true);
    const [images, setImages] = useState([
      project?.image_url || "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=600&auto=format&fit=crop",
      ...(project?.file_urls?.length ? project.file_urls : []),
      // "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=600&auto=format&fit=crop"
    ]);

  // const [mainImage, setMainImage] = useState(images[0]);

  const [mainImage, setMainImage] = useState(project?.image_url || "");

  useEffect(() => {
    if (project) {

      setLoading(false);
    }
  }, [project]);

  const handleCreateOpenOffer = () => {
    const token = localStorage.getItem('token');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if(token){
      if(user && !user.is_professional) {
          navigate(`/dashboard/projects?invite=${project?.user_id}&create=true`);
        }else{
          alert("Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer.");
        }
      
    }else{
      navigate('/login');
    }

  };

  const getCategoryLabel = (category: any) => {
  // Si c'est un chiffre, on cherche dans la liste
  if (!isNaN(category)) {
    const found = getAllCategories().find((item:any) => item.id === Number(category));
    return found ? found.label : category;
  }
  // Sinon on retourne directement la valeur (ex: texte personnalisé)
  return category || "SMARTEK";
};

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    // <div className="min-h-screen bg-white">
    //   <Header />
    //   <div className="flex flex-col lg:flex-row gap-8 p-6 lg:p-16 bg-white text-black">
    //     {/* Left Side - Main Image and Title */}
    //     <div className="flex-1">
    //       <h1 className="text-2xl lg:text-3xl font-bold mb-4">
    //         Logo Design Creation Sketch To Vector
    //       </h1>
    //       <div className="mb-4 text-sm text-gray-500 flex items-center gap-2">
    //         <img
    //           src="https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop"
    //           alt="Breno"
    //           className="w-6 h-6 rounded-full"
    //         />
    //         <span className="font-semibold">Breno Bitencourt</span>
    //         <span className="bg-gray-200 text-xs px-2 py-1 rounded">PRO</span>
    //       </div>
    //       <div className="rounded-xl overflow-hidden mb-6">
    //         <img
    //           src={mainImage}
    //           alt="Ibex Logo"
    //           className="w-full h-[600px] object-cover"
    //         />
    //       </div>
    //       <div className="flex gap-2 mb-8">
    //         {images.map((img, index) => (
    //           <div
    //             key={index}
    //             className="w-20 h-14 rounded overflow-hidden cursor-pointer border hover:border-pink-500"
    //             onClick={() => setMainImage(img)}
    //           >
    //             <img
    //               src={img}
    //               alt={`Thumbnail ${index + 1}`}
    //               className="w-full h-full object-cover"
    //             />
    //           </div>
    //         ))}
    //       </div>
    //       <div>
    //         <h2 className="text-lg font-semibold mb-2">About this Service</h2>
    //         <p className="text-sm text-gray-700">
    //           Symbol + Type Logo from Sketch to Vector format in a simple process.
    //         </p>
    //       </div>
    //     </div>

    //     {/* Right Side - Service Card */}
    //     <div className="w-full lg:w-96 p-6  rounded-xl shadow-sm">
    //       <div className="text-2xl font-bold mb-2">
    //         $1,600 <span className="text-sm text-yellow-500">Quick Hire</span>
    //       </div>
    //       <div className="text-sm text-gray-600 mb-4">
    //         <p><strong>Concepts and revisions:</strong> 3 concepts, 2 revisions</p>
    //         <p><strong>Project Duration:</strong> 2 weeks</p>
    //       </div>
    //       <div className="flex gap-4 mb-6">
    //         <button className="flex-1 bg-pink-500 text-white py-2 rounded-md font-semibold hover:bg-pink-600 transition">
    //           Hire Now
    //         </button>
    //         <button className="flex-1 border border-gray-300 py-2 rounded-md font-semibold hover:bg-gray-100 transition">
    //           Request Service
    //         </button>
    //       </div>
    //       <div className="flex items-center gap-3 border-t pt-4">
    //         <img
    //           src="https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop"
    //           alt="Breno"
    //           className="w-12 h-12 rounded-full"
    //         />
    //         <div>
    //           <div className="font-semibold">Breno Bitencourt</div>
    //           <div className="text-xs text-gray-500">5.0 ★ - 16 projects completed</div>
    //           <div className="text-xs text-gray-500">São Paulo, Brazil</div>
    //           <div className="text-xs text-gray-500">Responds in about 1 hour</div>
    //           <div className="text-xs text-gray-500">6 Services available</div>
    //         </div>
    //       </div>
    //     </div>
    //   </div>
    //   <Footer />
    // </div>
     <div className="min-h-screen bg-white">
      <Header />
      <div className="flex flex-col lg:flex-row gap-8 p-6 lg:p-16 bg-white text-black">
        {/* Left Side - Main Image and Title */}
        <div className="flex-1">
          <h1 className="text-2xl lg:text-3xl font-bold mb-4">{project?.title||"TEST"}</h1>
          <div className="mb-4 text-sm text-gray-500 flex items-center gap-2">
            <img
              src={project?.avatar || "https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop"}
              alt={project?.organization || "Organization"}
              className="w-6 h-6 rounded-full"
            />
            <span className="font-semibold">{project?.professional_name||"TEST"}</span>
            <span className="bg-gray-200 text-xs px-2 py-1 rounded">PRO</span>
          </div>
          <div className="rounded-xl overflow-hidden mb-6">
            <img
              src={mainImage}
              alt={project?.title}
              className="w-full h-[600px] object-cover"
              onError={(e) => {
                e.currentTarget.onerror = null; // empêche les boucles infinies
                e.currentTarget.src = 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // chemin de l'image par défaut
              }}
            />
          </div>
          <div className="flex gap-2 mb-8">
             {images.map((img, index) => (
              <div
                key={index}
                className="w-20 h-14 rounded overflow-hidden cursor-pointer border hover:border-pink-500"
                onClick={() => setMainImage(img)}
              >
                <img
                  src={img}
                  alt={`Thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
          <div>
            <h2 className="text-lg font-semibold mb-2">Description</h2>
            <p className="text-sm text-gray-700"><div dangerouslySetInnerHTML={{ __html: project?.description }} /></p>
          </div>
        </div>

        {/* Right Side - Achievement Details */}
        <div className="w-full lg:w-96 p-6 rounded-xl shadow-sm">
          <div className="text-2xl font-bold mb-2">
           {project?.price ? `€${Number(project.price).toFixed(2)}` : ""}
          </div>
          <div className="text-2xl font-bold mb-2">
           {project?.date_create
            ? `Du : ${new Date(project.date_create).toLocaleDateString("fr-FR")}`
            : "Date non disponible"}

          </div>
          <div className="text-sm text-gray-600 mb-4">
            <p><strong>Professional:</strong> {project?.professional_name||"TEST"}</p>
            {/* <p><strong></strong> {project?.category||"SMARTEK"}</p> */}
            <p><strong>Catégorie :</strong> {getCategoryLabel(project?.category)}</p>

          </div>
          <div className="flex gap-4 mb-6">
            {/* <button className="flex-1 bg-pink-500 text-white py-2 rounded-md font-semibold hover:bg-pink-600 transition">
              View More
            </button> */}
            <Button
              variant="primary"
              fullWidth
              leftIcon={<Briefcase className="h-5 w-5" />}
              onClick={handleCreateOpenOffer}
              style={{
                backgroundColor: '#2980b9',
                color: 'white',
                marginTop: '12px',
                marginBottom: '12px',
                padding: '12px',
                fontSize: '1rem',
                fontWeight: 'bold',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              <span style={{ position: 'relative', zIndex: 2 }}>Créer une offre avec invitation</span>
              <span style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 100%)',
                zIndex: 1
              }}></span>
            </Button>
            {/* <button className="flex-1 border border-gray-300 py-2 rounded-md font-semibold hover:bg-gray-100 transition">
              Contact
            </button> */}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default DetailsSearch;
