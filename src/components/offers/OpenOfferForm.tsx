import React, { useState, useEffect } from 'react';
import { Briefcase, DollarSign, Calendar, Globe, Building, Link, Filter, Languages, MapPin, Clock, Tag, ChevronDown, Search, X } from 'lucide-react';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';
import FormTextarea from '../ui/FormTextarea';
import FormSelect from '../ui/FormSelect';
import FormMultiSelect from '../ui/FormMultiSelect';
import Checkbox from '../ui/Checkbox';
import { Check } from 'lucide-react';
import { MAIN_CATEGORIES, getCategoryOptions } from '../../data/categories';
import { useToast } from '../../context/ToastContext';

export interface OpenOfferFormData {
  id?: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company: string;
  website: string;
  description: string;
  recruitmentType: 'company' | 'personal';
  openToApplications: boolean;
  autoInvite: boolean;
  status: 'pending' | 'open' | 'closed' | 'in_progress' | 'completed' | 'invited';
  files: File[];
  filters: {
    languages: string[];
    skills: string[];
    location: string;
    experience_years: number;
    availability_status: string;
  };
}

interface OpenOfferFormProps {
  initialData?: Partial<OpenOfferFormData>;
  onSubmit: (data: OpenOfferFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const OpenOfferForm: React.FC<OpenOfferFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const { showToast } = useToast();
  const isEditMode = !!initialData?.id;

  // Options pour les catégories principales
  const categoryOptions = getCategoryOptions(MAIN_CATEGORIES);

  // États pour la gestion des catégories et compétences
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [filteredSkills, setFilteredSkills] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [allSkills, setAllSkills] = useState<string[]>([]);

  // Liste des compétences par catégorie
  const skillsByCategory: Record<string, string[]> = {
    'modeling': ['Blender', 'Maya', '3ds Max', 'ZBrush', 'Substance Painter', 'Hard Surface Modeling', 'Organic Modeling'],
    'animation': ['Animation de personnages', 'Animation d\'objets', 'Motion Capture', 'Rigging', 'Facial Animation'],
    'architectural': ['SketchUp', 'Revit', 'ArchiCAD', 'Lumion', 'V-Ray', 'Rendu architectural', 'Modélisation BIM'],
    'product': ['Fusion 360', 'SolidWorks', 'Rhino 3D', 'KeyShot', 'Prototypage 3D', 'Design industriel'],
    'character': ['Character Design', 'Character Modeling', 'Character Rigging', 'Facial Rigging', 'Sculpting'],
    'environment': ['Environment Design', 'Landscape Modeling', 'Terrain Generation', 'World Building', 'Level Design'],
    'vr_ar': ['Unity', 'Unreal Engine', 'WebXR', 'A-Frame', 'ARKit', 'ARCore', 'Oculus SDK'],
    'game_art': ['Game Asset Creation', 'Low Poly Modeling', 'Texture Baking', 'UV Mapping', 'PBR Texturing'],
  };

  // Options pour les langues
  const languageOptions = [
    { value: 'french', label: 'Français' },
    { value: 'english', label: 'Anglais' },
    { value: 'spanish', label: 'Espagnol' },
    { value: 'german', label: 'Allemand' },
    { value: 'italian', label: 'Italien' },
    { value: 'portuguese', label: 'Portugais' },
    { value: 'arabic', label: 'Arabe' },
    { value: 'chinese', label: 'Chinois' },
  ];

  // Les compétences sont maintenant gérées par le dictionnaire skillsByCategory

  // Options pour la disponibilité
  const availabilityOptions = [
    { value: 'available', label: 'Disponible' },
    { value: 'limited', label: 'Disponibilité limitée' },
    { value: 'busy', label: 'Occupé' },
    { value: 'unavailable', label: 'Indisponible' },
  ];

  // Options pour le statut
  const statusOptions = [
    { value: 'pending', label: 'En attente' },
    { value: 'open', label: 'Ouvert' },
    { value: 'closed', label: 'Fermé' },
    { value: 'in_progress', label: 'En cours' },
    { value: 'completed', label: 'Terminé' },
    { value: 'invited', label: 'Invité' },
  ];

  // Form state
  const [formData, setFormData] = useState<OpenOfferFormData>({
    title: initialData?.title || '',
    categories: initialData?.categories || [],
    budget: initialData?.budget || '',
    deadline: initialData?.deadline || '',
    company: initialData?.company || '',
    website: initialData?.website || '',
    description: initialData?.description || '',
    recruitmentType: initialData?.recruitmentType || 'company',
    openToApplications: initialData?.openToApplications ?? true,
    autoInvite: initialData?.autoInvite ?? false,
    status: initialData?.status || 'pending',
    files: initialData?.files || [],
    filters: initialData?.filters || {
      languages: [],
      skills: [],
      location: '',
      experience_years: 0,
      availability_status: 'available',
    },
  });

  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Toggle advanced filters
  const [showFilters, setShowFilters] = useState(
    initialData?.filters && (
      initialData.filters.languages.length > 0 ||
      initialData.filters.skills.length > 0 ||
      initialData.filters.location ||
      initialData.filters.experience_years > 0
    )
  );

  // Initialiser la liste complète des compétences
  useEffect(() => {
    const skills: string[] = [];
    Object.values(skillsByCategory).forEach(categorySkills => {
      categorySkills.forEach(skill => {
        if (!skills.includes(skill)) {
          skills.push(skill);
        }
      });
    });
    setAllSkills(skills.sort());
    setFilteredSkills(skills.sort());
  }, []);

  // Filtrer les compétences en fonction des catégories sélectionnées
  useEffect(() => {
    if (formData.categories.length > 0) {
      // Fusionner toutes les compétences des catégories sélectionnées
      const mergedSkills = formData.categories
        .flatMap(cat => skillsByCategory[cat] || [])
        .filter((skill, index, arr) => arr.indexOf(skill) === index); // enlever les doublons
      setFilteredSkills(mergedSkills);
    } else {
      setFilteredSkills(allSkills);
    }
  }, [formData.categories, allSkills, skillsByCategory]);

  // Filtrer les compétences en fonction du terme de recherche
  useEffect(() => {
    if (searchTerm) {
      const baseSkills = selectedCategory ? skillsByCategory[selectedCategory] : allSkills;
      const filtered = baseSkills.filter(skill =>
        skill.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredSkills(filtered);
    } else {
      if (selectedCategory && skillsByCategory[selectedCategory]) {
        setFilteredSkills(skillsByCategory[selectedCategory]);
      } else {
        setFilteredSkills(allSkills);
      }
    }
  }, [searchTerm, selectedCategory, allSkills]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Normaliser l'URL si c'est le champ website
    if (name === 'website' && value) {
      let normalizedUrl = value.trim();
      if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
        normalizedUrl = 'https://' + normalizedUrl;
      }
      setFormData(prev => ({ ...prev, [name]: normalizedUrl }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle multi-select changes
  const handleMultiSelectChange = (name: string, values: string[]): void => {
    setFormData(prev => ({ ...prev, [name]: values }));
  };

  // Handle filter changes
  const handleFilterChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [name]: value,
      },
    }));
  };

  // Gestion de la recherche
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Ajouter une compétence depuis la liste
  const handleAddSkillFromList = (skill: string) => {
    if (!formData.filters.skills.includes(skill)) {
      handleFilterChange('skills', [...formData.filters.skills, skill]);
    }
  };

  // Form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('handleSubmit called'); // Debug log

    // Validate form
    const newErrors: Record<string, string> = {};
    let hasErrors = false;

    if (!formData.title.trim()) {
      newErrors.title = 'Le titre est requis';
      showToast('warning', 'Le champ Titre de l\'offre est requis');
      console.log('Le champ Titre de l\'offre est requis');
      hasErrors = true;
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est requise';
      showToast('warning', 'Le champ Description est requis');
      console.log('Le champ Description est requis');
      hasErrors = true;
    }

    if (!formData.budget.trim()) {
      newErrors.budget = 'Le budget est requis';
      showToast('warning', 'Le champ Budget est requis');
      console.log('Le champ Budget est requis');
      hasErrors = true;
    }

    if (!formData.deadline) {
      newErrors.deadline = 'La date limite est requise';
      showToast('warning', 'Le champ Date limite est requis');
      hasErrors = true;
    } else {
      const deadlineDate = new Date(formData.deadline);
      const today = new Date();

      if (deadlineDate < today) {
        newErrors.deadline = 'La date limite doit être dans le futur';
        showToast('warning', 'La date limite doit être dans le futur');
        console.log('La date limite doit être dans le futur');
        hasErrors = true;
      }
    }

    // if (!formData.company.trim()) {
    //   newErrors.company = 'L\'entreprise est requise';
    //   showToast('warning', 'Le champ Entreprise est requis');
    //   console.log('Le champ Entreprise est requis');
    //   hasErrors = true;
    // }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    // Submit form only if there are no errors
    onSubmit(formData);
  };

  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title || '',
        categories: initialData.categories || [],
        budget: initialData.budget || '',
        deadline: initialData.deadline || '',
        company: initialData.company || '',
        website: initialData.website || '',
        description: initialData.description || '',
        recruitmentType: initialData.recruitmentType || 'company',
        openToApplications: initialData.openToApplications ?? true,
        autoInvite: initialData.autoInvite ?? false,
        status: initialData.status || 'open',
        files: initialData.files || [],
        filters: initialData.filters || {
          languages: [],
          skills: [],
          location: '',
          experience_years: 0,
          availability_status: 'available',
        },
      });
    }
  }, [initialData]);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-neutral-200">
        <h2 className="text-xl font-semibold text-neutral-900">
          {isEditMode ? 'Modifier l\'offre' : 'Créer une nouvelle offre'}
        </h2>
        <p className="text-neutral-600 text-sm mt-1">
          {isEditMode
            ? 'Mettez à jour les détails de votre offre'
            : 'Remplissez les détails de votre offre pour trouver les meilleurs professionnels'}
        </p>
      </div>

      <form 
        onSubmit={(e) => {
          console.log('Form submitted');
          handleSubmit(e);
        }} 
        className="p-6"
      >
        <div className="space-y-6">
          {/* Offer Title */}
          <FormInput
            label="Titre de l'offre *"
            id="title"
            name="title"
            placeholder="Ex: Création d'un personnage 3D pour jeu vidéo"
            value={formData.title}
            onChange={handleInputChange}
            error={errors.title}
            icon={<Briefcase className="h-5 w-5 text-neutral-400" />}
          />

          {/* Pas de sélecteur de catégories ici - déplacé dans les filtres avancés */}

          {/* Budget */}
          <FormInput
            label="Budget *"
            id="budget"
            name="budget"
            placeholder="Ex: 500€ - 1000€"
            value={formData.budget}
            onChange={handleInputChange}
            error={errors.budget}
            icon={<DollarSign className="h-5 w-5 text-neutral-400" />}
          />

          {/* Deadline */}
          <FormInput
            label="Date limite *"
            id="deadline"
            name="deadline"
            type="date"
            value={formData.deadline ? new Date(formData.deadline).toISOString().split('T')[0] : ''}
            onChange={handleInputChange}
            error={errors.deadline}
            icon={<Calendar className="h-5 w-5 text-neutral-400" />}
          />

          {/* Company */}
          {/* <FormInput
            label="Entreprise *"
            id="company"
            name="company"
            placeholder="Nom de votre entreprise"
            value={formData.company}
            onChange={handleInputChange}
            error={errors.company}
            icon={<Building className="h-5 w-5 text-neutral-400" />}
          /> */}

          {/* Website */}
          {/* <FormInput
            label="Site web"
            id="website"
            name="website"
            placeholder="monsite.com ou https://monsite.com"
            value={formData.website}
            onChange={handleInputChange}
            icon={<Link className="h-5 w-5 text-neutral-400" />}
          /> */}

          {/* Description */}
          <FormTextarea
            label="Description *"
            id="description"
            name="description"
            placeholder="Décrivez votre projet en détail..."
            value={formData.description}
            onChange={handleInputChange}
            error={errors.description}
            rows={6}
          />

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Annuler
            </Button>

            <Button
              type="submit"
              variant="primary"
              isLoading={isLoading}
              leftIcon={isEditMode ? <Check className="h-5 w-5" /> : undefined}
              style={{ backgroundColor: '#2980b9', color: 'black' }}
            >
              {isEditMode ? 'Mettre à jour l\'offre' : 'Créer l\'offre'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default OpenOfferForm;
