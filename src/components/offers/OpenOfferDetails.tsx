import React from 'react';
import { Calendar, DollarSign, Clock, User, Building, Globe, Eye, Users, Edit, Trash2, Share2, MessageSquare } from 'lucide-react';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Avatar from '../ui/Avatar';

interface OpenOfferDetailsProps {
  id: number;
  title: string;
  description: string;
  categories?: string[];
  budget: string;
  deadline: string;
  company?: string;
  website?: string;
  recruitment_type?: 'company' | 'personal';
  open_to_applications?: boolean;
  status: string;
  created_at: string;
  updated_at: string;
  views_count?: number;
  applications_count?: number;
  client?: {
    id: number;
    name: string;
    avatar?: string;
  };
  filters?: {
    languages?: string[];
    skills?: string[];
    location?: string;
    experience_years?: number;
    availability_status?: string;
  };
  onEdit?: (offer: any) => void;
  onDelete?: (id: number) => void;
  onApply?: (id: number) => void;
  onContact?: (clientId: number) => void;
  onShare?: (id: number) => void;
  isOwner?: boolean;
  isProfessional?: boolean;
}

const OpenOfferDetails: React.FC<OpenOfferDetailsProps> = ({
  id,
  title,
  description,
  categories = [],
  budget,
  deadline,
  company,
  website,
  recruitment_type = 'company',
  open_to_applications = true,
  status,
  created_at,
  updated_at,
  views_count = 0,
  applications_count = 0,
  client,
  filters = {},
  onEdit,
  onDelete,
  onApply,
  onContact,
  onShare,
  isOwner = false,
  isProfessional = false,
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (dateString: string) => {
    const deadline = new Date(dateString);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Délai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'open':
        return 'success';
      case 'in_progress':
        return 'primary';
      case 'completed':
        return 'neutral';
      case 'closed':
        return 'danger';
      case 'invited':
        return 'info';
      default:
        return 'neutral';
    }
  };

  // Get status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'open':
        return 'Ouvert';
      case 'in_progress':
        return 'En cours';
      case 'completed':
        return 'Terminé';
      case 'closed':
        return 'Fermé';
      case 'invited':
        return 'Invité';
      default:
        return 'Inconnu';
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main content */}
      <div className="lg:col-span-2">
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
            <h2 className="text-xl font-semibold text-neutral-900">Détails de l'offre</h2>
            <Badge color={getStatusBadgeColor(status)}>
              {getStatusLabel(status)}
            </Badge>
          </div>

          <div className="p-6">
            <h1 className="text-2xl font-bold text-neutral-900 mb-4">{title}</h1>

            {/* Categories */}
            {categories.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {categories.map((category, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-neutral-100 text-neutral-800"
                  >
                    {category}
                  </span>
                ))}
              </div>
            )}

            {/* Description */}
            <div className="prose prose-neutral max-w-none mb-6">
              <p className="whitespace-pre-line">{description}</p>
            </div>

            {/* Key details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {/* Budget */}
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Budget</h3>
                  <p className="text-neutral-900">{budget}</p>
                </div>
              </div>

              {/* Deadline */}
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Date limite</h3>
                  <p className="text-neutral-900">{formatDate(deadline)}</p>
                </div>
              </div>

              {/* Days remaining */}
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Temps restant</h3>
                  <p className="text-neutral-900">{getDaysRemaining(deadline)}</p>
                </div>
              </div>

              {/* Company */}
              {company && (
                <div className="flex items-center">
                  <Building className="h-5 w-5 text-neutral-500 mr-3" />
                  <div>
                    <h3 className="text-sm font-medium text-neutral-700">Entreprise</h3>
                    <p className="text-neutral-900">{company}</p>
                  </div>
                </div>
              )}

              {/* Website */}
              {website && (
                <div className="flex items-center">
                  <Globe className="h-5 w-5 text-neutral-500 mr-3" />
                  <div>
                    <h3 className="text-sm font-medium text-neutral-700">Site web</h3>
                    <a
                      href={website.startsWith('http') ? website : `https://${website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary-600 hover:text-primary-700"
                    >
                      {website}
                    </a>
                  </div>
                </div>
              )}

              {/* Views count */}
              <div className="flex items-center">
                <Eye className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Vues</h3>
                  <p className="text-neutral-900">{views_count}</p>
                </div>
              </div>

              {/* Applications count (for clients only) */}
              {isOwner && (
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-neutral-500 mr-3" />
                  <div>
                    <h3 className="text-sm font-medium text-neutral-700">Candidatures</h3>
                    <p className="text-neutral-900">{applications_count}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Client info (for professionals only) */}
            {isProfessional && client && (
              <div className="bg-neutral-50 rounded-lg p-4 border border-neutral-200 mb-6">
                <div className="flex items-center">
                  <Avatar
                    src={client.avatar}
                    fallback={client.name.charAt(0)}
                    size="md"
                    className="mr-3"
                  />
                  <div>
                    <h3 className="text-sm font-medium text-neutral-700">Client</h3>
                    <p className="text-neutral-900">{client.name}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Required skills and languages (if specified) */}
            {(filters.skills?.length || filters.languages?.length || filters.location) && (
              <div className="bg-neutral-50 rounded-lg p-4 border border-neutral-200 mb-6">
                <h3 className="text-sm font-medium text-neutral-700 mb-3">Critères recherchés</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Skills */}
                  {filters.skills && filters.skills.length > 0 && (
                    <div>
                      <h4 className="text-xs font-medium text-neutral-500 mb-2">Compétences</h4>
                      <div className="flex flex-wrap gap-2">
                        {filters.skills.map((skill, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-200 text-neutral-800"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Languages */}
                  {filters.languages && filters.languages.length > 0 && (
                    <div>
                      <h4 className="text-xs font-medium text-neutral-500 mb-2">Langues</h4>
                      <div className="flex flex-wrap gap-2">
                        {filters.languages.map((language, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-200 text-neutral-800"
                          >
                            {language}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Location */}
                  {filters.location && (
                    <div>
                      <h4 className="text-xs font-medium text-neutral-500 mb-2">Localisation</h4>
                      <span className="text-sm text-neutral-700">{filters.location}</span>
                    </div>
                  )}

                  {/* Experience years */}
                  {filters.experience_years && filters.experience_years > 0 && (
                    <div>
                      <h4 className="text-xs font-medium text-neutral-500 mb-2">Expérience minimale</h4>
                      <span className="text-sm text-neutral-700">
                        {filters.experience_years} an{filters.experience_years > 1 ? 's' : ''}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Metadata */}
            <div className="text-xs text-neutral-500 mt-6 pt-6 border-t border-neutral-200">
              <p>Créé le {formatDate(created_at)}</p>
              <p>Dernière mise à jour le {formatDate(updated_at)}</p>
              <p>
                Type de recrutement: {recruitment_type === 'company' ? 'Entreprise' : 'Personnel'}
              </p>
              <p>
                {open_to_applications
                  ? 'Ouvert aux candidatures'
                  : 'Fermé aux candidatures (sur invitation uniquement)'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1">
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden sticky top-6">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h3 className="text-lg font-semibold text-neutral-900">Actions</h3>
          </div>

          <div className="p-6 space-y-4">
            {/* Actions for clients (owners) */}
            {isOwner && (
              <>
                {onEdit && (
                  <Button
                    variant="outline"
                    leftIcon={<Edit className="h-5 w-5" />}
                    onClick={() => {
                      // Appeler la fonction onEdit avec les détails de l'offre
                      onEdit({ id, title, description, categories, budget, deadline, company, website, recruitment_type, open_to_applications, status, filters });

                      // Ajouter le paramètre edit=true à l'URL pour permettre la persistance de l'état d'édition
                      const url = new URL(window.location.href);
                      url.searchParams.set('edit', 'true');
                      window.history.pushState({}, '', url);
                    }}
                    fullWidth
                  >
                    Modifier l'offre
                  </Button>
                )}

                {onDelete && (
                  <Button
                    variant="danger"
                    leftIcon={<Trash2 className="h-5 w-5" />}
                    onClick={() => onDelete(id)}
                    fullWidth
                  >
                    Supprimer l'offre
                  </Button>
                )}
              </>
            )}

            {/* Actions for professionals */}
            {isProfessional && (
              <>
                {onApply && status === 'open' && (
                  <Button
                    variant="primary"
                    onClick={() => onApply(id)}
                    fullWidth
                    style={{ backgroundColor: '#2980b9', color: 'black' }}
                  >
                    Postuler à cette offre
                  </Button>
                )}

                {onContact && client && (
                  <Button
                    variant="outline"
                    leftIcon={<MessageSquare className="h-5 w-5" />}
                    onClick={() => onContact(client.id)}
                    fullWidth
                  >
                    Contacter le client
                  </Button>
                )}
              </>
            )}

            {/* Share action (for everyone) */}
            {onShare && (
              <Button
                variant="ghost"
                leftIcon={<Share2 className="h-5 w-5" />}
                onClick={() => onShare(id)}
                fullWidth
              >
                Partager l'offre
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OpenOfferDetails;
