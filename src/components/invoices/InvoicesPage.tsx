import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Filter,
  SlidersHorizontal,
  FileText,
  Download,
  AlertTriangle,
  ChevronLeft,
  ChevronRight,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  XCircle,
  Eye
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import Badge from '../ui/Badge';

interface Invoice {
  id: number;
  invoiceNumber: string;
  projectTitle: string;
  amount: string;
  status: 'paid' | 'pending' | 'overdue' | 'cancelled';
  issueDate: string;
  dueDate: string;
  clientName?: string;
  professionalName?: string;
}

const InvoicesPage: React.FC = () => {
  const navigate = useNavigate();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState<string>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');
  const isProfessional = user?.is_professional === true;

  useEffect(() => {
    const fetchInvoices = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/invoices`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des factures');
        }

        const data = await response.json();

        // Transformer les données en format Invoice
        const formattedInvoices = data.invoices.map((invoice: any) => ({
          id: invoice.id,
          invoiceNumber: invoice.invoice_number,
          projectTitle: invoice.project_title,
          amount: `${invoice.amount} €`,
          status: invoice.status,
          issueDate: invoice.issue_date,
          dueDate: invoice.due_date,
          clientName: invoice.client_name,
          professionalName: invoice.professional_name,
        }));

        setInvoices(formattedInvoices);
        setFilteredInvoices(formattedInvoices);
        setError(null);
      } catch (err) {
        console.error('Error fetching invoices:', err);
        setError('Impossible de récupérer les factures. Veuillez réessayer plus tard.');
        // Utiliser des données statiques en cas d'erreur
        const mockInvoices = getMockInvoices();
        setInvoices(mockInvoices);
        setFilteredInvoices(mockInvoices);
      } finally {
        setLoading(false);
      }
    };

    fetchInvoices();
  }, [token, isProfessional]);

  // Filtrer les factures lorsque les filtres changent
  useEffect(() => {
    let result = [...invoices];

    // Filtre par recherche
    if (searchTerm) {
      result = result.filter(invoice =>
        invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.projectTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (invoice.clientName && invoice.clientName.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (invoice.professionalName && invoice.professionalName.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filtre par statut
    if (statusFilter !== 'all') {
      result = result.filter(invoice => invoice.status === statusFilter);
    }

    // Filtre par date
    if (dateFilter !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      switch (dateFilter) {
        case 'month':
          const monthAgo = new Date(today);
          monthAgo.setMonth(monthAgo.getMonth() - 1);
          result = result.filter(invoice => {
            const invoiceDate = new Date(invoice.issueDate);
            return invoiceDate >= monthAgo;
          });
          break;
        case 'quarter':
          const quarterAgo = new Date(today);
          quarterAgo.setMonth(quarterAgo.getMonth() - 3);
          result = result.filter(invoice => {
            const invoiceDate = new Date(invoice.issueDate);
            return invoiceDate >= quarterAgo;
          });
          break;
        case 'year':
          const yearAgo = new Date(today);
          yearAgo.setFullYear(yearAgo.getFullYear() - 1);
          result = result.filter(invoice => {
            const invoiceDate = new Date(invoice.issueDate);
            return invoiceDate >= yearAgo;
          });
          break;
      }
    }

    // Tri
    result.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'date':
          comparison = new Date(a.issueDate).getTime() - new Date(b.issueDate).getTime();
          break;
        case 'amount':
          comparison = parseFloat(a.amount) - parseFloat(b.amount);
          break;
        case 'number':
          comparison = a.invoiceNumber.localeCompare(b.invoiceNumber);
          break;
        default:
          comparison = 0;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredInvoices(result);
    setCurrentPage(1); // Réinitialiser à la première page après filtrage
  }, [searchTerm, statusFilter, dateFilter, sortBy, sortOrder, invoices]);

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredInvoices.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);

  const paginate = (pageNumber: number) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  const handleViewInvoice = (invoiceId: number) => {
    // Naviguer vers la page de détail de la facture
    console.log(`View invoice ${invoiceId}`);
  };

  const handleDownloadInvoice = (invoiceId: number, event: React.MouseEvent) => {
    // Empêcher la propagation pour ne pas déclencher handleViewInvoice
    event.stopPropagation();
    console.log(`Download invoice ${invoiceId}`);
  };

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  // Données statiques pour le fallback
  const getMockInvoices = (): Invoice[] => {
    return [
      {
        id: 1,
        invoiceNumber: 'INV-2023-001',
        projectTitle: 'Création d\'un personnage 3D pour jeu vidéo',
        amount: '1 200 €',
        status: 'paid',
        issueDate: '2023-05-15',
        dueDate: '2023-06-15',
        clientName: isProfessional ? 'Studio GameArt' : undefined,
        professionalName: !isProfessional ? 'Thomas Martin' : undefined,
      },
      {
        id: 2,
        invoiceNumber: 'INV-2023-002',
        projectTitle: 'Animation d\'une scène d\'introduction',
        amount: '800 €',
        status: 'pending',
        issueDate: '2023-06-20',
        dueDate: '2023-07-20',
        clientName: isProfessional ? 'AppTech Solutions' : undefined,
        professionalName: !isProfessional ? 'Sophie Dubois' : undefined,
      },
      {
        id: 3,
        invoiceNumber: 'INV-2023-003',
        projectTitle: 'Modélisation d\'objets pour environnement virtuel',
        amount: '500 €',
        status: 'paid',
        issueDate: '2023-07-05',
        dueDate: '2023-08-05',
        clientName: isProfessional ? 'VR Experiences' : undefined,
        professionalName: !isProfessional ? 'Lucas Bernard' : undefined,
      },
      {
        id: 4,
        invoiceNumber: 'INV-2023-004',
        projectTitle: 'Logo 3D pour entreprise',
        amount: '700 €',
        status: 'overdue',
        issueDate: '2023-07-10',
        dueDate: '2023-08-10',
        clientName: isProfessional ? 'TechCorp' : undefined,
        professionalName: !isProfessional ? 'Emma Petit' : undefined,
      },
      {
        id: 5,
        invoiceNumber: 'INV-2023-005',
        projectTitle: 'Modélisation de produit pour e-commerce',
        amount: '900 €',
        status: 'cancelled',
        issueDate: '2023-08-01',
        dueDate: '2023-09-01',
        clientName: isProfessional ? 'E-Shop Plus' : undefined,
        professionalName: !isProfessional ? 'Nicolas Roux' : undefined,
      },
    ];
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge color="green">Payée</Badge>;
      case 'pending':
        return <Badge color="blue">En attente</Badge>;
      case 'overdue':
        return <Badge color="red">En retard</Badge>;
      case 'cancelled':
        return <Badge color="gray">Annulée</Badge>;
      default:
        return <Badge color="gray">Inconnu</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // Calcul des statistiques
  const totalAmount = invoices
    .filter(invoice => invoice.status === 'paid')
    .reduce((sum, invoice) => sum + parseFloat(invoice.amount), 0)
    .toFixed(2);

  const pendingAmount = invoices
    .filter(invoice => invoice.status === 'pending')
    .reduce((sum, invoice) => sum + parseFloat(invoice.amount), 0)
    .toFixed(2);

  const overdueAmount = invoices
    .filter(invoice => invoice.status === 'overdue')
    .reduce((sum, invoice) => sum + parseFloat(invoice.amount), 0)
    .toFixed(2);

  return (
    <DashboardLayout
      title="Mes factures"
      subtitle="Gérez et suivez toutes vos factures"
      actions={
        <Button
          variant="outline"
          onClick={() => navigate('/dashboard')}
        >
          Retour au tableau de bord
        </Button>
      }
    >
      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
          <div className="bg-blue-100 p-3 rounded-full mr-4">
            <FileText className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <p className="text-sm text-neutral-600">Total factures</p>
            <p className="text-xl font-semibold">{invoices.length}</p>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
          <div className="bg-green-100 p-3 rounded-full mr-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <p className="text-sm text-neutral-600">Montant payé</p>
            <p className="text-xl font-semibold">{totalAmount} €</p>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
          <div className="bg-amber-100 p-3 rounded-full mr-4">
            <Clock className="h-6 w-6 text-amber-600" />
          </div>
          <div>
            <p className="text-sm text-neutral-600">En attente</p>
            <p className="text-xl font-semibold">{pendingAmount} €</p>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
          <div className="bg-red-100 p-3 rounded-full mr-4">
            <XCircle className="h-6 w-6 text-red-600" />
          </div>
          <div>
            <p className="text-sm text-neutral-600">En retard</p>
            <p className="text-xl font-semibold">{overdueAmount} €</p>
          </div>
        </div>
      </div>

      {/* Barre de recherche et filtres */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Rechercher une facture..."
              className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <select
              className="px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              aria-label="Filtrer par statut"
            >
              <option value="all">Tous les statuts</option>
              <option value="paid">Payées</option>
              <option value="pending">En attente</option>
              <option value="overdue">En retard</option>
              <option value="cancelled">Annulées</option>
            </select>
            <select
              className="px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              aria-label="Filtrer par période"
            >
              <option value="all">Toutes les dates</option>
              <option value="month">Dernier mois</option>
              <option value="quarter">Dernier trimestre</option>
              <option value="year">Dernière année</option>
            </select>
            <Button
              variant="outline"
              leftIcon={<SlidersHorizontal className="h-5 w-5" />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Plus
            </Button>
          </div>
        </div>

        {/* Filtres avancés */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-neutral-200 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Trier par</label>
              <div className="flex">
                <select
                  className="flex-grow px-3 py-2 border border-neutral-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  aria-label="Trier par"
                >
                  <option value="date">Date</option>
                  <option value="amount">Montant</option>
                  <option value="number">Numéro</option>
                </select>
                <button
                  type="button"
                  className="px-3 py-2 border border-l-0 border-neutral-300 rounded-r-lg bg-neutral-50 hover:bg-neutral-100"
                  onClick={toggleSortOrder}
                >
                  {sortOrder === 'asc' ? '↑' : '↓'}
                </button>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Période personnalisée</label>
              <div className="flex gap-2">
                <input
                  type="date"
                  className="flex-1 px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  aria-label="Date de début"
                  placeholder="Date de début"
                />
                <span className="flex items-center text-neutral-500">à</span>
                <input
                  type="date"
                  className="flex-1 px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  aria-label="Date de fin"
                  placeholder="Date de fin"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Montant</label>
              <div className="flex gap-2">
                <input
                  type="number"
                  placeholder="Min"
                  className="flex-1 px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
                <span className="flex items-center text-neutral-500">à</span>
                <input
                  type="number"
                  placeholder="Max"
                  className="flex-1 px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Liste des factures */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-red-700 mb-2">Erreur</h2>
          <p className="text-red-600">{error}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </Button>
        </div>
      ) : filteredInvoices.length === 0 ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-8 text-center">
          <FileText className="h-16 w-16 text-neutral-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-neutral-800 mb-2">Aucune facture trouvée</h2>
          <p className="text-neutral-600 mb-6">
            Aucune facture ne correspond à vos critères de recherche. Essayez de modifier vos filtres.
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-neutral-900">
              {filteredInvoices.length} facture{filteredInvoices.length > 1 ? 's' : ''}
            </h3>
            <div className="flex items-center text-sm text-neutral-500">
              <span>Affichage de {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredInvoices.length)} sur {filteredInvoices.length}</span>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-neutral-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Numéro
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Projet
                  </th>
                  {isProfessional ? (
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Client
                    </th>
                  ) : (
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Professionnel
                    </th>
                  )}
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Date d'émission
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Date d'échéance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Montant
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-neutral-200">
                {currentItems.map((invoice) => (
                  <tr
                    key={invoice.id}
                    className="hover:bg-neutral-50 cursor-pointer"
                    onClick={() => handleViewInvoice(invoice.id)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-900">
                      {invoice.invoiceNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600">
                      {invoice.projectTitle}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600">
                      {isProfessional ? invoice.clientName : invoice.professionalName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600">
                      {formatDate(invoice.issueDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600">
                      {formatDate(invoice.dueDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-900">
                      {invoice.amount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(invoice.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          type="button"
                          className="text-neutral-600 hover:text-neutral-900"
                          aria-label="Voir la facture"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewInvoice(invoice.id);
                          }}
                        >
                          <Eye className="h-5 w-5" />
                        </button>
                        <button
                          type="button"
                          className="text-neutral-600 hover:text-neutral-900"
                          aria-label="Télécharger la facture"
                          onClick={(e) => handleDownloadInvoice(invoice.id, e)}
                        >
                          <Download className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-neutral-200 flex justify-between items-center">
              <Button
                variant="ghost"
                size="sm"
                leftIcon={<ChevronLeft className="h-4 w-4" />}
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Précédent
              </Button>
              <div className="flex items-center space-x-2">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                  <button
                    key={number}
                    type="button"
                    onClick={() => paginate(number)}
                    className={`px-3 py-1 rounded-md ${
                      currentPage === number
                        ? 'bg-primary-600 text-white'
                        : 'text-neutral-600 hover:bg-neutral-100'
                    }`}
                  >
                    {number}
                  </button>
                ))}
              </div>
              <Button
                variant="ghost"
                size="sm"
                rightIcon={<ChevronRight className="h-4 w-4" />}
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Suivant
              </Button>
            </div>
          )}
        </div>
      )}
    </DashboardLayout>
  );
};

export default InvoicesPage;
