import React, { useState } from "react";
import { API_BASE_URL } from '../config';

const Equipes: React.FC = () => {
  // Récupération des données du localStorage
  const storedProfile = JSON.parse(localStorage.getItem("userProfile") || "{}");
  const initialIdNumber = storedProfile?.profile_data?.identity_document_number || "";

  const [idDocument, setIdDocument] = useState<File | null>(null);
  const [idNumber, setIdNumber] = useState<string>(initialIdNumber);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  // Gestion du fichier
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setIdDocument(event.target.files[0]);
    }
  };

  // Gestion de l'input texte
  const handleIdNumberChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIdNumber(event.target.value);
  };

  // Envoi des données à l'API
  const handleSubmit = async () => {
    if (!idDocument || !idNumber) {
      setMessage("❌ Veuillez sélectionner une pièce d'identité et entrer son numéro.");
      return;
    }

    setLoading(true);
    setMessage("");

    const token = localStorage.getItem("token");
    const formData = new FormData();
    formData.append("identity_document", idDocument);
    formData.append("identity_document_number", idNumber);

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/profile/completion/kyc`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data"
          },
          body: formData,
        }
      );

      const data = await response.json();

      if (response.ok) {
        setMessage("✅ KYC mis à jour avec succès !");
        
        // Mettre à jour le localStorage
        const updatedProfile = {
          ...storedProfile,
          profile_data: {
            ...storedProfile.profile_data,
            identity_document_number: idNumber,
          },
        };
        localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
      } else {
        setMessage("❌ Erreur : " + (data.message || "Impossible d'enregistrer les KYC."));
      }
    } catch (error) {
      setMessage("❌ Erreur réseau, veuillez réessayer.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      <h2 className="text-sm font-bold text-gray-700 mb-4">KYC & Certification</h2>

      {message && (
        <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
          {message}
        </div>
      )}

      {/* Pièce d'identité */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700">Pièce d'identité</label>
        <input
          type="file"
          accept="image/*,application/pdf"
          onChange={handleFileChange}
          className="w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* ID du document */}
      <div className="mb-4">
        <label htmlFor="identity_document_number" className="block text-sm font-medium text-gray-700 mb-1">
          ID du Document
        </label> 
        <input
          type="text"
          id="identity_document_number"
          value={idNumber}
          onChange={handleIdNumberChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      {/* Bouton d'enregistrement */}
      <div className="mt-6">
        <button
          onClick={handleSubmit}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition"
          disabled={loading}
        >
          {loading ? "Envoi en cours..." : "Enregistrer mes KYC & Certification"}
        </button>
      </div>
    </div>
  );
};

export default Equipes;
