import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Bell, CheckCircle, Filter } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import NotificationItem, { NotificationItemProps } from './NotificationItem';
import Button from '../ui/Button';
import Badge from '../ui/Badge';

const NotificationsPage: React.FC = () => {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<NotificationItemProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const token = localStorage.getItem('token');

  // Fetch notifications
  useEffect(() => {
    const fetchNotifications = async () => {
      setLoading(true);
      try {
        // In a real implementation, you would fetch from your API
        // For now, we'll use mock data
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock notifications data
        const mockNotifications: NotificationItemProps[] = [
          {
            id: 1,
            title: 'Nouveau message',
            message: 'Thomas Martin vous a envoyé un message concernant votre projet "Création d\'un personnage 3D".',
            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
            read: false,
            type: 'message',
            link: '/discussions/201',
            sender: {
              id: 201,
              name: 'Thomas Martin',
              avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
            },
          },
          {
            id: 2,
            title: 'Mise à jour de projet',
            message: 'Le projet "Animation d\'une scène d\'introduction" a été mis à jour avec de nouveaux fichiers.',
            timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
            read: true,
            type: 'project',
            link: '/offre/2',
          },
          {
            id: 3,
            title: 'Paiement reçu',
            message: 'Vous avez reçu un paiement de 500€ pour le projet "Modélisation d\'objets pour environnement virtuel".',
            timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
            read: false,
            type: 'payment',
            link: '/invoices',
          },
          {
            id: 4,
            title: 'Nouvelle évaluation',
            message: 'Lucas Bernard vous a donné une évaluation 5 étoiles pour votre travail sur "Modélisation d\'objets pour environnement virtuel".',
            timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
            read: true,
            type: 'review',
            sender: {
              id: 203,
              name: 'Lucas Bernard',
              avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
            },
          },
          {
            id: 5,
            title: 'Bienvenue sur la plateforme',
            message: 'Bienvenue sur Hi 3D Artiste ! Complétez votre profil pour commencer à trouver des projets ou des professionnels.',
            timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
            read: true,
            type: 'system',
          },
        ];
        
        // Filter notifications if needed
        let filteredNotifications = [...mockNotifications];
        if (activeFilter) {
          filteredNotifications = mockNotifications.filter(
            notification => notification.type === activeFilter
          );
        }
        
        setNotifications(filteredNotifications);
        setHasMore(false); // For mock data, we don't have pagination
        setError(null);
      } catch (err) {
        console.error('Error fetching notifications:', err);
        setError('Impossible de charger les notifications');
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, [token, activeFilter, page]);

  // Handle notification click
  const handleNotificationClick = (notification: NotificationItemProps) => {
    // Mark as read
    if (!notification.read) {
      handleMarkAsRead(notification.id);
    }

    // Navigate to the appropriate page based on notification type
    if (notification.link) {
      navigate(notification.link);
    } else {
      switch (notification.type) {
        case 'message':
          navigate(`/discussions/${notification.sender?.id}`);
          break;
        case 'project':
          navigate(`/offre/${notification.id}`);
          break;
        case 'payment':
          navigate('/invoices');
          break;
        case 'review':
          navigate(`/profile/${notification.sender?.id}`);
          break;
        default:
          // Do nothing for system notifications
          break;
      }
    }
  };

  // Mark notification as read
  const handleMarkAsRead = (id: number) => {
    // In a real implementation, you would call your API
    // For now, we'll update the state directly
    setNotifications(prevNotifications =>
      prevNotifications.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  // Mark all notifications as read
  const handleMarkAllAsRead = () => {
    // In a real implementation, you would call your API
    // For now, we'll update the state directly
    setNotifications(prevNotifications =>
      prevNotifications.map(notification => ({
        ...notification,
        read: true,
      }))
    );
  };

  // Load more notifications
  const handleLoadMore = () => {
    setPage(prevPage => prevPage + 1);
  };

  // Filter notifications
  const handleFilterChange = (filter: string | null) => {
    setActiveFilter(filter);
    setPage(1);
  };

  // Count unread notifications
  const unreadCount = notifications.filter(notification => !notification.read).length;

  return (
    <DashboardLayout
      title="Notifications"
      subtitle={`${notifications.length} notification${notifications.length !== 1 ? 's' : ''} au total`}
      actions={
        unreadCount > 0 ? (
          <Button
            variant="outline"
            leftIcon={<CheckCircle className="h-5 w-5" />}
            onClick={handleMarkAllAsRead}
          >
            Tout marquer comme lu
          </Button>
        ) : undefined
      }
    >
      {/* Filters */}
      <div className="mb-6 flex items-center space-x-2 overflow-x-auto pb-2">
        <Button
          variant={activeFilter === null ? 'primary' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange(null)}
        >
          Toutes
        </Button>
        <Button
          variant={activeFilter === 'message' ? 'primary' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('message')}
        >
          Messages
        </Button>
        <Button
          variant={activeFilter === 'project' ? 'primary' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('project')}
        >
          Projets
        </Button>
        <Button
          variant={activeFilter === 'payment' ? 'primary' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('payment')}
        >
          Paiements
        </Button>
        <Button
          variant={activeFilter === 'review' ? 'primary' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('review')}
        >
          Évaluations
        </Button>
        <Button
          variant={activeFilter === 'system' ? 'primary' : 'outline'}
          size="sm"
          onClick={() => handleFilterChange('system')}
        >
          Système
        </Button>
      </div>

      {/* Notifications list */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
        {loading && notifications.length === 0 ? (
          <div className="flex justify-center items-center p-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <div className="text-red-500 mb-4">{error}</div>
            <Button
              variant="primary"
              onClick={() => window.location.reload()}
            >
              Réessayer
            </Button>
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-12 text-center">
            <Bell className="h-12 w-12 text-neutral-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-neutral-700 mb-1">Aucune notification</h3>
            <p className="text-neutral-500">
              Vous n'avez pas encore reçu de notifications.
            </p>
          </div>
        ) : (
          <>
            <div className="divide-y divide-neutral-200">
              {notifications.map(notification => (
                <NotificationItem
                  key={notification.id}
                  {...notification}
                  onClick={() => handleNotificationClick(notification)}
                />
              ))}
            </div>
            
            {/* Load more button */}
            {hasMore && (
              <div className="p-4 border-t border-neutral-200 bg-neutral-50 text-center">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={loading}
                >
                  {loading ? 'Chargement...' : 'Charger plus'}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default NotificationsPage;
