import React from 'react';
import { Briefcase, Send, ThumbsUp, ThumbsDown, UserPlus } from 'lucide-react';
import { NotificationItemProps } from '../NotificationItem';

// Types de notifications liées aux appels d'offre
export type OfferNotificationType =
  | 'offer_received'       // Professionnel a reçu un appel d'offre
  | 'offer_invitation'     // Professionnel a reçu une invitation personnalisée
  | 'offer_interested'     // Client a reçu une notification d'intérêt d'un professionnel
  | 'offer_not_available'  // Client a reçu une notification de non-disponibilité
  | 'offer_published'      // Client a publié un appel d'offre
  | 'offer_closed'         // Un appel d'offre a été clôturé
  | 'offer_selected'       // Un professionnel a été sélectionné pour un appel d'offre
  | 'offer_message';       // Un message a été reçu dans le fil de discussion d'un appel d'offre

// Interface pour les notifications d'appel d'offre
export interface OfferNotificationData {
  offer_id: number;
  offer_title: string;
  offer_type?: OfferNotificationType;
  professional_id?: number;
  professional_name?: string;
  professional_avatar?: string;
  client_id?: number;
  client_name?: string;
  client_avatar?: string;
  message?: string;
}

// Fonction pour générer une notification d'appel d'offre
export const createOfferNotification = (
  id: number,
  type: OfferNotificationType,
  data: OfferNotificationData,
  timestamp: string,
  read: boolean = false
): NotificationItemProps => {
  let title = '';
  let message = '';
  let notificationType: 'message' | 'project' | 'payment' | 'review' | 'system' = 'project';
  let link = `/dashboard/offers/${data.offer_id}`;
  let icon = <Briefcase className="h-5 w-5" />;
  let sender = undefined;

  switch (type) {
    case 'offer_received':
      title = 'Nouvel appel d\'offre';
      message = `Vous avez reçu un nouvel appel d'offre : "${data.offer_title}"`;
      icon = <Briefcase className="h-5 w-5" />;
      if (data.client_id && data.client_name) {
        sender = {
          id: data.client_id,
          name: data.client_name,
          avatar: data.client_avatar,
        };
      }
      break;

    case 'offer_invitation':
      title = 'Invitation personnalisée';
      message = `Vous avez reçu une invitation personnalisée pour l'appel d'offre : "${data.offer_title}"`;
      icon = <UserPlus className="h-5 w-5" />;
      if (data.client_id && data.client_name) {
        sender = {
          id: data.client_id,
          name: data.client_name,
          avatar: data.client_avatar,
        };
      }
      break;

    case 'offer_interested':
      title = 'Professionnel intéressé';
      message = `${data.professional_name} est intéressé par votre appel d'offre : "${data.offer_title}"`;
      icon = <ThumbsUp className="h-5 w-5" />;
      if (data.professional_id && data.professional_name) {
        sender = {
          id: data.professional_id,
          name: data.professional_name,
          avatar: data.professional_avatar,
        };
      }
      break;

    case 'offer_not_available':
      title = 'Professionnel non disponible';
      message = `${data.professional_name} n'est pas disponible pour votre appel d'offre : "${data.offer_title}"`;
      icon = <ThumbsDown className="h-5 w-5" />;
      if (data.professional_id && data.professional_name) {
        sender = {
          id: data.professional_id,
          name: data.professional_name,
          avatar: data.professional_avatar,
        };
      }
      break;

    case 'offer_published':
      title = 'Appel d\'offre publié';
      message = `Votre appel d'offre "${data.offer_title}" a été publié avec succès`;
      icon = <Send className="h-5 w-5" />;
      link = `/dashboard/projects/${data.offer_id}`;
      break;

    case 'offer_closed':
      title = 'Appel d\'offre clôturé';
      message = `L'appel d'offre "${data.offer_title}" a été clôturé`;
      icon = <Briefcase className="h-5 w-5" />;
      link = `/dashboard/projects/${data.offer_id}`;
      break;

    case 'offer_selected':
      title = 'Sélectionné pour un projet';
      message = `Vous avez été sélectionné pour l'appel d'offre : "${data.offer_title}"`;
      icon = <ThumbsUp className="h-5 w-5" />;
      if (data.client_id && data.client_name) {
        sender = {
          id: data.client_id,
          name: data.client_name,
          avatar: data.client_avatar,
        };
      }
      break;

    case 'offer_message':
      title = 'Nouveau message';
      message = data.message || `Nouveau message concernant l'appel d'offre : "${data.offer_title}"`;
      notificationType = 'message';
      icon = <Briefcase className="h-5 w-5" />;
      link = `/discussions/${data.offer_id}`;
      if (data.professional_id && data.professional_name) {
        sender = {
          id: data.professional_id,
          name: data.professional_name,
          avatar: data.professional_avatar,
        };
      } else if (data.client_id && data.client_name) {
        sender = {
          id: data.client_id,
          name: data.client_name,
          avatar: data.client_avatar,
        };
      }
      break;
  }

  // Create the notification object without the icon property
  return {
    id,
    title,
    message,
    timestamp,
    read,
    type: notificationType,
    link,
    sender,
  };
};
