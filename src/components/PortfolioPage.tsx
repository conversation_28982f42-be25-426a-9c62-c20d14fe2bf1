import React, { useMemo, useState, useMemo as useReactMemo } from "react";
import CategoryList from "./CategoryList";
import Gallery from "./Gallery";
import GalleryService from "./GalleryService";
import { API_BASE_URL } from "./../config";
import DesignersSection from "../components/DesignersSection";
import LogoServicesSection from "../components/LogoServicesSection";

interface GalleryItem {
  id: number;
  title: string;
  author: string;
  authorAvatar: string;
  isPro: boolean;
  likes: number;
  views: string;
  image: string;
  price?: string;
}

type Props = {
  pros: any[];
  services?: any[];
  viewType?: "default" | "designers" | "services";
  query?: string;
  searchLoading: boolean;
};

const PortfolioPage: React.FC<Props> = ({
  pros,
  viewType = "default",
  services = [],
  query,
  searchLoading,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  const getUrlProlfil = (path: string) => {
    return path
      ? `${API_BASE_URL}${path}`
      : "https://randomuser.me/api/portraits/men/32.jpg";
  };

  const categories: string[] = [
    "Modélisation 3D",
    "Animation 3D",
    "Rendu 3D",
    "Texturing & Shading",
    "Rigging & Skinning",
    "Compositing 3D",
    "Effets Visuels (VFX)",
    "Architecture 3D",
    "Jeux Vidéo 3D",
    "Produit 3D & Industriel",
    "Personnages 3D",
    "Environnements 3D",
    "Motion Design 3D",
    "Réalité Virtuelle (VR)",
    "Réalité Augmentée (AR)",
    "Scan 3D & Photogrammétrie",
    "Modélisation Médicale 3D",
    "Impression 3D",
    "Simulations Physiques",
    "Éclairage 3D",
  ];

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
  };

  const filteredPros = useMemo(() => {
    if (!selectedCategory) return pros;
    const needle = selectedCategory.toLowerCase();
    return pros.filter((pro) =>
      pro?.service_offer?.some((s: any) =>
        (s?.category || "").toLowerCase().includes(needle)
      )
    );
  }, [selectedCategory, pros]);

  const galleryItems: GalleryItem[] = useMemo(
    () =>
      (filteredPros || []).map((pro) => ({
        id: pro?.id,
        title: pro?.title || "Untitled",
        author: `${pro?.first_name ?? ""} ${pro?.last_name ?? ""}`.trim(),
        authorAvatar: pro?.profile_picture_path
          ? getUrlProlfil(pro.profile_picture_path)
          : "https://randomuser.me/api/portraits/men/32.jpg",
        isPro: pro?.availability_status === "available",
        likes: pro?.likes_count || 0,
        views: pro?.views_count?.toLocaleString?.() || "0",
        image:
          pro?.achievements?.[0]?.image_url ||
          pro?.service_offer?.[0]?.image_url ||
          (pro?.profile_picture_path
            ? `${API_BASE_URL}${pro.profile_picture_path}`
            : "https://randomuser.me/api/portraits/men/32.jpg"),
      })),
    [filteredPros]
  );

  // ---------- Loader global pendant la recherche ----------
  const Loader = (
    <>
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
      <div className="text-center py-6 text-gray-500 text-lg font-medium">
        🔍 Recherche en cours...
      </div>
    </>
  );

  return (
    <div
      style={{
        marginTop: "60px",
        fontFamily: "Arial, sans-serif",
        fontSize: "25px",
      }}
    >
      <CategoryList
        onCategorySelect={handleCategorySelect}
        selectedCategory={selectedCategory}
      />

      {/* Si on cherche, on affiche le loader quel que soit le viewType */}
      {searchLoading ? (
        Loader
      ) : (
        <>
          {viewType === "default" && (
            // sécurise le prop et évite les crashs si services est undefined
            <GalleryService items={services ?? []} />
          )}

          {viewType === "designers" && (
            <DesignersSection professionals={filteredPros} query={query} />
          )}

          {viewType === "services" && (
            // Tu peux garder LogoServicesSection si tu préfères l’ancien rendu
            // <LogoServicesSection services={services} query={query} />
            <GalleryService items={services ?? []} query={query} />
          )}
        </>
      )}
    </div>
  );
};

export default React.memo(PortfolioPage);

// import React, { useMemo, useState } from "react";
// import CategoryList from "./CategoryList";
// import Gallery from "./Gallery";
// import GalleryService from "./GalleryService";
// import { API_BASE_URL } from "./../config";
// import DesignersSection from "../components/DesignersSection";
// import LogoServicesSection from "../components/LogoServicesSection";

// interface GalleryItem {
//   id: number;
//   title: string;
//   author: string;
//   authorAvatar: string;
//   isPro: boolean;
//   likes: number;
//   views: string;
//   image: string;
//   price? : string;
// }

// type Props = {
//   pros: any[];
//   services?: any[];
//   viewType?: "default" | "designers" | "services";
//   query?: string;
//   searchLoading: boolean;
// };
// const PortfolioPage: React.FC<Props> = ({
//   pros,
//   viewType = "default",
//   services,
//   query,
//   searchLoading,
// }) => {
//   const [selectedCategory, setSelectedCategory] = useState<string>("");

//   const getUrlProlfil = (path: string) => {
//     return path
//       ? `${API_BASE_URL}${path}`
//       : "https://randomuser.me/api/portraits/men/32.jpg";
//   };
//   const categories: string[] = [
//     "Modélisation 3D",
//     "Animation 3D",
//     "Rendu 3D",
//     "Texturing & Shading",
//     "Rigging & Skinning",
//     "Compositing 3D",
//     "Effets Visuels (VFX)",
//     "Architecture 3D",
//     "Jeux Vidéo 3D",
//     "Produit 3D & Industriel",
//     "Personnages 3D",
//     "Environnements 3D",
//     "Motion Design 3D",
//     "Réalité Virtuelle (VR)",
//     "Réalité Augmentée (AR)",
//     "Scan 3D & Photogrammétrie",
//     "Modélisation Médicale 3D",
//     "Impression 3D",
//     "Simulations Physiques",
//     "Éclairage 3D",
//   ];

//   const handleCategorySelect = (category: string) => {
//     // alert(`Filtering by category: ${category}`);
//     setSelectedCategory(category);
//   };

//   const filteredPros = useMemo(() => {
//     return pros.filter((pro) =>
//       pro.service_offer?.some((s: any) =>
//         (s.category || "")
//           .toLowerCase()
//           .includes(selectedCategory.toLowerCase())
//       )
//     );
//   }, [selectedCategory, pros]);

//   const galleryItems: GalleryItem[] = useMemo(
//     () =>
//       filteredPros.map((pro) => ({
//         id: pro.id,
//         title: pro.title || "Untitled",
//         author: `${pro.first_name} ${pro.last_name}`,
//         authorAvatar: pro.profile_picture_path
//           ? getUrlProlfil(pro.profile_picture_path)
//           : "https://randomuser.me/api/portraits/men/32.jpg",
//         isPro: pro.availability_status === "available",
//         likes: pro.likes_count || 0,
//         views: pro.views_count?.toLocaleString?.() || "0",
//         image:
//           pro.achievements?.[0]?.image_url ||
//           pro.service_offer?.[0]?.image_url ||
//           (pro.profile_picture_path
//             ? `${API_BASE_URL}${pro.profile_picture_path}`
//             : "https://randomuser.me/api/portraits/men/32.jpg"),
//       })),
//     [filteredPros]
//   );

//   return (
//     <div 
//       style={{
//         marginTop: "60px",
//         fontFamily: "Arial, sans-serif",
//         fontSize: "25px",
//       }}
//     >
//       <CategoryList
//         categories={categories.filter((c): c is string => c !== undefined)}
//         onCategorySelect={handleCategorySelect}
//         selectedCategory={selectedCategory}
//       />
//       {/* <Gallery items={galleryItems} /> galleryItems */}
//       {/* {viewType === "default" && <Gallery items={galleryItems} />} */}
//       {viewType === "default" && <GalleryService items={services} />}

//       {viewType === "designers" &&
//         (searchLoading ? (
//           <>
//             <div className="flex justify-center items-center h-96">
//               <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
//             </div>
//             <div className="text-center py-10 text-gray-500 text-lg font-medium">
//               🔍 Professional search in progress...
//             </div>
//           </>
//         ) : (
//           <DesignersSection professionals={pros} query={query} />
//         ))}

//       {viewType === "services" &&
//         (searchLoading ? (
//           <>
//             <div className="flex justify-center items-center h-96">
//               <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
//             </div>
//             <div className="text-center py-10 text-gray-500 text-lg font-medium">
//               🔍 Service search in progress...
//             </div>
//           </>
//         ) : (
//           // <LogoServicesSection services={services} query={query} />
//           <GalleryService items={services} query={query}/>
//         ))}
//     </div>
//   );
// };

// export default PortfolioPage;
