import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import ExplorerRealizations from '../ExplorerRealizations';
import ExplorerServices from '../ExplorerServices';
import {
  ArrowLeft,
  Calendar,
  Clock,
  DollarSign,
  Download,
  Edit,
  FileText,
  MessageSquare,
  Paperclip,
  Trash2,
  User,
  CheckCircle,
  XCircle,
  AlertTriangle,
  MapPin,
  Mail,
  Phone,
  Star,
  Briefcase,
  Award,
  Globe,
  Languages
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import Header from '../Header';
import Footer from '../Footer';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Avatar from '../ui/Avatar';
import Card from '../ui/Card';
import { CardBody, CardTitle } from '../ui/Card';
import Grid from '../layout/Grid';
import Section from '../layout/Section';
import Container from '../layout/Container';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
}

// Interface pour les éléments du portfolio
interface PortfolioItem {
  id?: number;
  path?: string; 
  name?: string;
  type?: string;
  created_at?: string;
  // description?: string;
}

interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  skills: string[] | null;
  languages: string[] | null;
  availability_status: string | null;
  services_offered: string[];
  hourly_rate: string;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  avatar?: string;
  cover_photo?: string;
  profile_picture_path?: string;
  rating?: number;
  review_count?: number;
  bio?: string;
  title?: string;
  portfolio?: PortfolioItem[];
  user: User;
}

function ProfessionalDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [professional, setProfessional] = useState<FreelanceProfile | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingInvite, setLoadingInvitation] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const [offers, setOffers] = useState([]);
  const [selectedOfferId, setSelectedOfferId] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  console.log("Utisateur: ",user);

  // Fonction pour formater l'URL de l'image
  const getImageUrl = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  useEffect(() => {
    const fetchProfessional = async () => {
      setLoading(true);
      try {
        // Essayer d'abord l'endpoint professionals
        let response = await fetch(`${API_BASE_URL}/api/professionals/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        });

        // Si l'endpoint professionals échoue, essayer l'endpoint users
        if (!response.ok) {
          console.log('Endpoint professionals a échoué, essai de l\'endpoint users');
          response = await fetch(`${API_BASE_URL}/api/users/${id}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
          });
        }

        if (!response.ok) {
          throw new Error('Impossible de récupérer les détails du professionnel');
        }

        const data = await response.json();
        console.log('Données du professionnel récupérées:', data);

        // Traiter les données selon le format
        if (data.professional) {
          // Traiter les skills qui peuvent être une chaîne JSON ou un tableau
          let skills = [];
          if (data.professional.skills) {
            if (Array.isArray(data.professional.skills)) {
              skills = data.professional.skills;
            } else if (typeof data.professional.skills === 'string') {
              try {
                skills = JSON.parse(data.professional.skills);
              } catch (e) {
                skills = [data.professional.skills]; // Si ce n'est pas un JSON valide, le traiter comme une chaîne simple
              }
            }
          }

          // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
          let portfolio = [];
          if (data.professional.portfolio) {
            if (Array.isArray(data.professional.portfolio)) {
              portfolio = data.professional.portfolio;
            } else if (typeof data.professional.portfolio === 'string') {
              try {
                portfolio = JSON.parse(data.professional.portfolio);
              } catch (e) {
                portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
              }
            }
          }

          // Mettre à jour les données du professionnel
          setProfessional({
            ...data.professional,
            skills: skills,
            portfolio: portfolio.length > 0 ? portfolio : [
              { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
              { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
              { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
            ]
          });
        } else if (data.user && data.profile_data) {
          // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
          let portfolio = [];
          if (data.profile_data.portfolio) {
            if (Array.isArray(data.profile_data.portfolio)) {
              portfolio = data.profile_data.portfolio;
            } else if (typeof data.profile_data.portfolio === 'string') {
              try {
                portfolio = JSON.parse(data.profile_data.portfolio);
              } catch (e) {
                portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
              }
            }
          }

          setProfessional({
            ...data.profile_data,
            user: data.user,
            portfolio: portfolio.length > 0 ? portfolio : [
              { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
              { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
              { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
            ]
          });
        } else if (data.user) {
          setProfessional({
            id: data.user.id,
            user_id: data.user.id,
            first_name: data.user.first_name,
            last_name: data.user.last_name,
            phone: '',
            address: '',
            city: data.user.city || 'Paris',
            country: data.user.country || 'France',
            skills: data.user.skills || ['Animation 3D', 'Modélisation 3D', 'Rigging'],
            languages: ['Français', 'Anglais'],
            availability_status: data.user.availability_status || 'available',
            services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
            hourly_rate: data.user.hourly_rate || '45',
            completion_percentage: data.completion_percentage || 100,
            created_at: data.user.created_at,
            updated_at: data.user.updated_at,
            avatar: data.user.avatar,
            profile_picture_path: data.user.profile_picture_path,
            rating: data.user.rating || 4.8,
            review_count: data.user.review_count || 27,
            bio: data.user.bio || 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
            title: data.user.title || 'Artiste 3D',
            portfolio: data.user.portfolio || [
              { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
              { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
              { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
            ],
            user: data.user,
          });
        } else {
          // Utiliser des données de démonstration
          setProfessional({
            id: parseInt(id || '1'),
            user_id: parseInt(id || '1'),
            first_name: 'Thomas',
            last_name: 'Martin',
            phone: '+33 6 12 34 56 78',
            address: '123 Rue de la Création',
            city: 'Paris',
            country: 'France',
            skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
            languages: ['Français', 'Anglais'],
            availability_status: 'available',
            services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
            hourly_rate: '45',
            completion_percentage: 100,
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
            profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
            rating: 4.8,
            review_count: 27,
            bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
            title: 'Artiste 3D',
            user: {
              id: parseInt(id || '1'),
              first_name: 'Thomas',
              last_name: 'Martin',
              email: '<EMAIL>',
              is_professional: true,
            },
          });
        }
      } catch (err) {
        console.error('Erreur lors de la récupération du professionnel:', err);
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('Une erreur inconnue est survenue');
        }

        // Utiliser des données de démonstration en cas d'erreur
        setProfessional({
          id: parseInt(id || '1'),
          user_id: parseInt(id || '1'),
          first_name: 'Thomas',
          last_name: 'Martin',
          phone: '+33 6 12 34 56 78',
          address: '123 Rue de la Création',
          city: 'Paris',
          country: 'France',
          skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
          languages: ['Français', 'Anglais'],
          availability_status: 'available',
          services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
          hourly_rate: '45',
          completion_percentage: 100,
          created_at: '2023-01-01',
          updated_at: '2023-01-01',
          avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
          rating: 4.8,
          review_count: 27,
          portfolio: [
            { name: 'Projet 3D 1', path: 'https://picsum.photos/seed/101/300/200', type: 'Modélisation 3D pour un jeu vidéo' },
            { name: 'Animation 3D', path: 'https://picsum.photos/seed/102/300/200', type: 'Animation de personnage' },
            { name: 'Rendu architectural', path: 'https://picsum.photos/seed/103/300/200', type: 'Visualisation architecturale' },
          ],
          bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
          title: 'Artiste 3D',
          user: {
            id: parseInt(id || '1'),
            first_name: 'Thomas',
            last_name: 'Martin',
            email: '<EMAIL>',
            is_professional: true,
          },
        });
      } finally {
        setLoading(false);
      }
    };

    const fetchClientPendingOffers = async () => {
      if (!token) return;
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/client/open-offers/pending`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Erreur ${response.status}`);
        }

        const data = await response.json();
        console.log("Mes Offre : ", data);
        setOffers(data.offers);
        return data.offers;
      } catch (error) {
        console.error('Erreur lors de la récupération des offres client :', error);
        return [];
      }finally{
        setLoading(false);
      }
    };

    fetchProfessional();
    fetchClientPendingOffers();
  }, [id]);

   // 2. Envoyer l'invitation
  const sendInvitation = async () => {
    if (!selectedOfferId) return alert("Sélectionne une offre.");
    if (!token) return alert("Vous devez vous connecter pour invité un pro.");
    setLoadingInvitation(true);
    try {
      const res = await fetch(`${API_BASE_URL}/api/open-offers/${selectedOfferId}/invite`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ professional_id: professional?.user_id }),
      });

      console.log("professional_id===========", professional?.user_id)

      const result = await res.json();
      if (res.ok) {
        alert("Invitation envoyée avec succès !");
        setShowModal(false);
      } else {
        alert("Erreur : " + result.message);
      }
    } catch (err) {
      console.error(err);
      alert("Une erreur est survenue.");
    } finally {
      setLoadingInvitation(false);
    }
  };

  const handleContactProfessional = () => {
      if(token){
        if(user && !user.is_professional) {
          navigate(`/discussions/${professional?.user_id}`);
        }else{
          alert("Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer.");
        }
        
      }else{
        navigate('/login');
      }
    // }
    

  };

  const handleBackToList = () => {
    // navigate('/lists-independants');
     navigate(-1);
  };

  // Fonction pour créer une offre ouverte avec invitation automatique
  const handleCreateOpenOffer = () => {
    // const token = localStorage.getItem('token');
    
    if(token){
      if(user && !user.is_professional) {
        navigate(`/dashboard/projects?invite=${professional?.user_id}&create=true`);
      }else{
         alert("Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer.");
      }
    }else{
      navigate('/login');
    }

  };

  const handleInvitePro = () => {
    // const token = localStorage.getItem('token');
    if(token){
      if(user && !user.is_professional) {
        setShowModal(true);
      }else{
         alert("Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer.");
      }
      
    }else{
      navigate('/login');
    }

  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <div className="text-center p-6">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-neutral-700">Chargement des détails du professionnel...</p>
        </div>
      </div>
    );
  }

  if (error && !professional) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200 max-w-md">
          <h2 className="text-xl font-semibold text-red-700 mb-2">Erreur</h2>
          <p className="text-neutral-700">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={handleBackToList}
          >
            Retour à la liste des professionnels
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      <Header />

      <Section className="py-8">
        <Container>
          <Button
            variant="ghost"
            className="mb-6"
            leftIcon={<ArrowLeft className="h-4 w-4" />}
            onClick={handleBackToList}
          >
            Retour à la liste des professionnels
          </Button>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Colonne de gauche - Informations principales */}
            <div className="lg:col-span-2 space-y-8">
              {/* Carte principale du professionnel */}
              <Card>
                <div className="h-64 overflow-hidden">
                  <img
                    src={getImageUrl(professional?.cover_photo, "https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D")}
                    alt={`${professional?.first_name} ${professional?.last_name}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.onerror = null; // empêche les boucles infinies
                      e.currentTarget.src = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // chemin de l'image par défaut
                    }}
                  />
                </div>
                <CardBody className="p-6">
                  <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
                    <div className="flex items-center mb-4 md:mb-0">
                      <Avatar
                        size="xl"
                        src={getImageUrl(professional?.avatar)}
                        fallback={`${professional?.first_name?.[0]}${professional?.last_name?.[0]}`}
                        className="mr-4 border-4 border-white shadow-md"
                      />
                      <div>
                        <CardTitle className="text-2xl">{professional?.first_name} {professional?.last_name}</CardTitle>
                        <p className="text-neutral-600">{professional?.title || 'Artiste 3D'}</p>
                        <div className="flex items-center mt-1">
                          <Star className="h-4 w-4 text-yellow-500 mr-1" />
                          <span className="font-medium">{professional?.rating || 4.8}</span>
                          <span className="text-neutral-500 ml-1">({professional?.review_count || 27} avis)</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col space-y-2">
                      <Badge
                        variant={professional?.availability_status === 'available' ? 'success' : professional?.availability_status === 'busy' ? 'warning' : 'error'}
                        size="md"
                        className="mb-2"
                      >
                        {professional?.availability_status === 'available' ? 'Disponible' : professional?.availability_status === 'busy' ? 'Occupé' : 'Indisponible'}
                      </Badge>
                      <div className="flex items-center text-neutral-600">
                        <DollarSign className="h-4 w-4 mr-1" />
                        <span>{professional?.hourly_rate || '45'}€/heure</span>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="font-semibold text-lg mb-2">À propos</h3>
                    <p className="text-neutral-700">
                      {professional?.bio || 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.'}
                    </p>
                  </div>

                  <div className="mb-6">
                    <h3 className="font-semibold text-lg mb-2">Compétences</h3>
                    <div className="flex flex-wrap gap-2">
                      {professional?.skills?.map((skill, index) => (
                        <Badge key={index} variant="neutral">{skill}</Badge>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="flex items-center">
                      <MapPin className="h-5 w-5 text-neutral-500 mr-2" />
                      <span>{professional?.city}, {professional?.country}</span>
                    </div>
                    <div className="flex items-center">
                      <Mail className="h-5 w-5 text-neutral-500 mr-2" />
                      <span>{professional?.user?.email}</span>
                    </div>
                    {professional?.phone && (
                      <div className="flex items-center">
                        <Phone className="h-5 w-5 text-neutral-500 mr-2" />
                        <span>{professional?.phone}</span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <Languages className="h-5 w-5 text-neutral-500 mr-2" />
                      <span>{professional?.languages?.join(', ') || 'Français, Anglais'}</span>
                    </div>
                  </div>

                  <Button
                    variant="primary"
                    fullWidth
                    leftIcon={<MessageSquare className="h-5 w-5" />}
                    onClick={handleContactProfessional}
                  >
                    Contacter ce professionnel
                  </Button>
                </CardBody>
              </Card>

              {/* Services offerts */}
              {/* <Card>
                <CardBody className="p-6">
                  <CardTitle className="text-xl mb-4">Services offerts</CardTitle>
                  <Grid cols={1} mdCols={2} gap={4}>
                    {(professional?.services_offered || ['Animation 3D', 'Modélisation 3D', 'Rigging']).map((service, index) => (
                      <Card key={index} className="border border-neutral-200">
                        <CardBody className="p-4">
                          <div className="flex items-center mb-2">
                            <Briefcase className="h-5 w-5 text-primary mr-2" />
                            <h3 className="font-medium">{service}</h3>
                          </div>
                          <p className="text-sm text-neutral-600">
                            Service professionnel de {service.toLowerCase()} pour vos projets créatifs.
                          </p>
                        </CardBody>
                      </Card>
                    ))}
                  </Grid>
                </CardBody>
              </Card> */}
              <Card>
                <CardBody className="p-6">
                  {professional && professional.user_id && (
                    <ExplorerServices professionalId={professional.user_id} />
                  )}
                </CardBody>
              </Card>

              {/* Portfolio */}
              <Card>
                <CardBody className="p-6">
                  <CardTitle className="text-xl mb-4">Portfolio</CardTitle>
                  <Grid cols={1} mdCols={2} lgCols={3} gap={4}>
                    {professional?.portfolio && Array.isArray(professional.portfolio) && professional.portfolio.length > 0 ? (
                      professional.portfolio.map((item, index) => (
                        <div key={index} className="overflow-hidden rounded-lg border border-neutral-200">
                          <img
                            src={getImageUrl(item.path, `https://picsum.photos/seed/${professional?.id || 1}${index}/300/200`)}
                            alt={item.name || `Réalisation ${index + 1}`}
                            className="w-full h-40 object-cover"
                          />
                          {item.name && (
                            <div className="p-2 bg-white">
                              <p className="text-sm font-medium">{item.name}</p>
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      // Afficher des images de démonstration si aucun portfolio n'est disponible
                      [1, 2, 3, 4, 5, 6].map((item) => (
                        <div key={item} className="overflow-hidden rounded-lg border border-neutral-200">
                          <img
                            src={`https://picsum.photos/seed/${professional?.id || 1}${item}/300/200`}
                            alt={`Réalisation ${item}`}
                            className="w-full h-40 object-cover"
                          />
                        </div>
                      ))
                    )}
                  </Grid>
                </CardBody>
              </Card>

              {/* Réalisations et Certifications */}
              <Card>
                <CardBody className="p-6">
                  {professional && professional.id && (
                    <ExplorerRealizations professionalId={professional.id} />
                  )}
                </CardBody>
              </Card>
            </div>

            {/* Colonne de droite - Informations complémentaires */}
            <div className="space-y-8">
              {/* Carte de contact */}
              <Card>
                <CardBody className="p-6">
                  <CardTitle className="text-xl mb-4">Contact</CardTitle>
                  <div className="space-y-4">
                    <Button
                      variant="primary"
                      fullWidth
                      leftIcon={<MessageSquare className="h-5 w-5" />}
                      onClick={handleContactProfessional}
                    >
                      Envoyer un message
                    </Button>
                    <Button
                      variant="primary"
                      fullWidth
                      leftIcon={<Briefcase className="h-5 w-5" />}
                      onClick={handleCreateOpenOffer}
                      style={{
                        backgroundColor: '#2980b9',
                        color: 'white',
                        marginTop: '12px',
                        marginBottom: '6px',
                        padding: '12px',
                        fontSize: '1rem',
                        fontWeight: 'bold',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      <span style={{ position: 'relative', zIndex: 2 }}>Demander une offre</span>
                      <span style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 100%)',
                        zIndex: 1
                      }}></span>
                    </Button>

                    <Button
                      variant="primary"
                      fullWidth
                      leftIcon={<Briefcase className="h-5 w-5" />}
                      onClick={handleInvitePro}
                      // onClick={() => setShowModal(true)}
                      style={{
                        backgroundColor: '#2980b9',
                        color: 'white',
                        marginTop: '6px',
                        marginBottom: '12px',
                        padding: '12px',
                        fontSize: '1rem',
                        fontWeight: 'bold',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      <span style={{ position: 'relative', zIndex: 2 }}>Inviter à une offre ouverte</span>
                      <span style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 100%)',
                        zIndex: 1
                      }}></span>
                    </Button>
                    <a
                      href={`mailto:${professional?.user?.email}`}
                      className="inline-flex items-center justify-center px-4 py-2 border border-neutral-300 text-sm font-medium rounded-md text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 w-full"
                    >
                      <Mail className="h-5 w-5 mr-2" />
                      Envoyer un email
                    </a>
                  </div>
                </CardBody>
              </Card>

              {/* Carte des avis */}
              <Card>
                <CardBody className="p-6">
                  <CardTitle className="text-xl mb-4">Avis clients</CardTitle>
                  <div className="flex items-center mb-4">
                    <div className="flex items-center mr-4">
                      <Star className="h-6 w-6 text-yellow-500 mr-1" />
                      <span className="text-2xl font-bold">{professional?.rating || 4.8}</span>
                    </div>
                    <div className="text-neutral-600">
                      <span className="font-medium">{professional?.review_count || 27}</span> avis
                    </div>
                  </div>
                  <div className="space-y-4">
                    {[
                      { name: 'Julie D.', rating: 5, comment: 'Excellent travail, très professionnel et réactif.' },
                      { name: 'Marc L.', rating: 4, comment: 'Bonne communication et résultat de qualité.' },
                      { name: 'Sophie M.', rating: 5, comment: 'Je recommande vivement, travail impeccable.' }
                    ].map((review, index) => (
                      <div key={index} className="border-b border-neutral-200 pb-4 last:border-0 last:pb-0">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium">{review.name}</span>
                          <div className="flex">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${i < review.rating ? 'text-yellow-500' : 'text-neutral-300'}`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-neutral-600 text-sm">{review.comment}</p>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>

              {/* Carte de disponibilité */}
              <Card>
                <CardBody className="p-6">
                  <CardTitle className="text-xl mb-4">Disponibilité</CardTitle>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span>Statut actuel</span>
                      <Badge
                        variant={professional?.availability_status === 'available' ? 'success' : professional?.availability_status === 'busy' ? 'warning' : 'error'}
                      >
                        {professional?.availability_status === 'available' ? 'Disponible' : professional?.availability_status === 'busy' ? 'Occupé' : 'Indisponible'}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Délai de réponse</span>
                      <span className="text-neutral-700">Sous 24h</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Disponible pour</span>
                      <span className="text-neutral-700">Projets à court et long terme</span>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>


        {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded w-full max-w-md relative">
            <button onClick={() => setShowModal(false)} className="absolute top-2 right-2 text-gray-500">X</button>
            <h2 className="text-xl mb-4 font-bold">Sélectionner une offre</h2>

            <div className="space-y-2 max-h-60 overflow-y-auto">
              {offers.map((offer : any) => (
                <label key={offer.id} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    value={offer.id}
                    checked={selectedOfferId === offer.id}
                    onChange={() => setSelectedOfferId(offer.id)}
                  />
                  <span>{offer.title}</span>
                </label>
              ))}
            </div>

            <button
              onClick={sendInvitation}
              className="mt-4 bg-green-600 text-white px-4 py-2 rounded"
              disabled={loadingInvite}
            >
              {loadingInvite ? 'Envoi...' : "Envoyer l'invitation"}
            </button>
          </div>
        </div>
      )}
        </Container>
      </Section>

      <Footer />
    </div>
  );
}

export default ProfessionalDetails;
