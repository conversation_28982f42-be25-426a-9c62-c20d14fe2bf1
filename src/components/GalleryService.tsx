import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { API_BASE_URL } from "../config";

// type GalleryItem = {
//   id: number;
//   title: string;
//   author: string;
//   authorAvatar: string;
//   isPro?: boolean;
//   likes: number;
//   views: string;
//   image: string;
//   price?:string;
// };

type Props = {
  items: any[]|undefined;
  marginBottom?: number | string;
  onDislike?: (id: number) => void;
  query?:string;
};

const GalleryService: React.FC<Props> = ({ items, marginBottom, onDislike,query }) => {
  const navigate = useNavigate();
  

  if (items?.length === 0) {
    return (
      <div className="text-center py-20 text-gray-600 flex flex-col items-center justify-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-24 h-24 text-gray-400 mb-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={1.5}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M3 7a2 2 0 012-2h5l2 2h7a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V7z"
          />
        </svg>
        <h2 className="text-2xl font-semibold">No Services found</h2>
        <p className="text-gray-500 mt-2 max-w-md">
          We couldn't find any services matching your search or category.
          Try again with a different filter.
        </p>
      </div>
    );
  }

  return (
    <div
      className="w-full max-w-[1440px] mx-auto"
      style={marginBottom !== undefined ? { marginBottom } : undefined}
    >
      {query && query.trim() !== "" && (
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-3">
            {query} Services
          </h1>
          <p className="text-lg text-gray-600">
            {items?.length || 0} results
          </p>
        </div>
      )}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 px-4 justify-items-center">
        {items?.map((item) => {
          // const isLiked = likedProfiles.includes(item.id);
          // const totalLikes = likesCount[item.id] || item.likes;

          return (
            <div
              key={item.id}
              className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer"
              // onClick={() => navigate(`/professionals/${item.id}`)}
              onClick={() => {
                      navigate('/details-search', {
                        state: { service : item }
                      });
                }}
            >
              {/* Image container - ratio 4:3 */}
              <div className="relative w-full pb-[75%]">
                {" "}
                {/* 4:3 ratio */}
                <div
                  style={{
                    backgroundImage: `url(${item.image_url})`,
                    borderRadius: "5px",
                  }}
                  className="absolute inset-0 bg-cover bg-center z-1000"
                />
              </div>

              {/* Content container */}
              <div className="px-0 py-0 pr-4 flex flex-col mb-4">
                {/* Titre avec alignement fixe */}
                <div className="flex items-center min-h-[27px]">
                  <h2
                    className="font-medium text-[18px] line-clamp-1 flex-1"
                    style={{
                      fontFamily: "Arial, sans-serif",
                      color: "#000000",
                      fontWeight: "bold",
                    }}
                  >
                    {item.title}
                  </h2>
                  <div
                    className="flex items-center gap-4"
                    style={{ fontFamily: "Arial, sans-serif" }}
                  >
                    <span className="flex items-center gap-1 text-[12px]">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="ionicon"
                        viewBox="0 0 512 512"
                        width="14"
                        height="14"
                        fill="#787777ff"
                      >
                        <circle cx="256" cy="256" r="64" />
                        <path d="M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96c-42.52 0-84.33 12.15-124.27 36.11-40.73 24.43-77.63 60.12-109.68 106.07a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416c46.71 0 93.81-14.43 136.2-41.72 38.46-24.77 72.72-59.66 99.08-100.92a32.2 32.2 0 00-.1-34.76zM256 352a96 96 0 1196-96 96.11 96.11 0 01-96 96z" />
                      </svg>
                      {item.views}
                    </span>

                    <span
                      className="flex items-center gap-1 text-[12px]"
                      // onClick={(e) => handleLike(e, item.id)}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="ionicon"
                        viewBox="0 0 512 512"
                        width="14"
                        height="14"
                        fill = "#787777ff"
                        // fill={isLiked ? "#006EFF" : "#787777ff"}
                      >
                        <path d="M400 480a16 16 0 01-10.63-4L256 357.41 122.63 476A16 16 0 0196 464V96a64.07 64.07 0 0164-64h192a64.07 64.07 0 0164 64v368a16 16 0 01-16 16z"></path>
                      </svg>
                      {item.likes}
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-end">
                  {/* Auteur en bas à gauche */}
                  <div className="flex items-center gap-2">
                    <img
                      src={item.avatar}
                      alt={item.professional_name}
                      className="w-5 h-5 rounded-full object-cover border-white"
                    />
                    <div className="flex items-center gap-1">
                      <h3 className="text-gray-900 text-[12px] font-medium leading-tight mr-1">
                        {item.professional_name}
                      </h3>
                      {item.isPro && (
                        <span className="bg-[#000000] text-white text-[10px] rounded-full px-2.5 py-0.5 inline-block">
                          PRO
                        </span>
                      )}
                    </div>
                  </div>
                  {/* Métriques en bas à droite */}
                </div>
                
                <p className="text-[13px] mt-2">USD  <strong>{item.price}</strong></p>

              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default GalleryService;
