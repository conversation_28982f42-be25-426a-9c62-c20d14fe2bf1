import React, { useState } from "react";
import Tabs from "./Tabs"; // Importez le composant Tabs
import BasicInformation from "./BasicInformation";
import Equipes from "./Equipes";
import SocialLinks from "./SocialLinks";
import AboutMe from "./AboutMe";
import Experience from "./Experience";
import Links from "./Links";
import Service from "./Service";
import Availability from "./Availability";
import ProfilClient from "./ProfilClient";
import OffresOuvertes from "./OffresOuvertes";

const EditProfileClientsTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState("dashboard");

  const tabs = [
    { id: "dashboard", label: "Tableau de bord", component: <OffresOuvertes /> },
    { id: "basic", label: "Informations personnelles", component: <BasicInformation /> },
    { id: "equipes", label: "Profile client", component: <ProfilClient /> },
    
   
  ];

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      {/* Barre d'onglets */}
      <Tabs
        tabs={tabs.map((tab) => ({ id: tab.id, label: tab.label }))} // Passer la liste des onglets
        activeTab={activeTab}
        setActiveTab={setActiveTab}
      />

      {/* Contenu de l'onglet actif */}
      <div className="max-w-7xl mx-auto py-12">
        {tabs.find((tab) => tab.id === activeTab)?.component}
      </div>
    </div>
  );
};

export default EditProfileClientsTabs;