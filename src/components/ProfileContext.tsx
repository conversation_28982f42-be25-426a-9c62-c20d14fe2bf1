import React, { createContext, useContext, useState, useEffect } from "react";

// 1️⃣ Créer le contexte
const ProfileContext = createContext<any>(null);

// 2️⃣ Créer un Provider pour centraliser les données du profil
export const ProfileProvider = ({ children }: { children: React.ReactNode }) => {
  const [profile, setProfile] = useState<any>(JSON.parse(localStorage.getItem("userProfile") || "{}"));

  // 3️⃣ Écouter les changements dans le localStorage pour actualiser les données automatiquement
  useEffect(() => {
    const handleStorageChange = () => {
      setProfile(JSON.parse(localStorage.getItem("userProfile") || "{}"));
    };

    window.addEventListener("storage", handleStorageChange);
    
    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  return (
    <ProfileContext.Provider value={{ profile, setProfile }}>
      {children}
    </ProfileContext.Provider>
  );
};

// 4️⃣ Hook pour accéder aux données du profil
export const useProfile = () => useContext(ProfileContext);
