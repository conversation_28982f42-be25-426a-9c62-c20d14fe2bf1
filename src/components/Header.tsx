import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { User, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LogOut } from "lucide-react";
import ProfileWizard from "./ProfileWizard";
import Button from "./ui/Button";
import Dropdown from "./ui/Dropdown";
import MobileMenu from "./ui/MobileMenu";
import SearchBar from "./SearchBar";
import LoginForm from "./auth/LoginForm";
import RegisterForm from "./auth/RegisterForm";
import ForgotPasswordForm from "./auth/ForgotPasswordForm";

interface NavLink {
  label: string;
  href: string;
  children?: Array<{ label: string; href: string }>;
}

function Header() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const token = localStorage.getItem("token");
  const isAuthenticated = !!token && !!user && !!user.id;
  const navigate = useNavigate();

  const userDropdownItems = [
    {
      label: `${user.first_name || ""} ${user.last_name || ""}`,
      href: "/dashboard/profile",
      divider: true,
    },
    {
      label: "Tableau de bord",
      href: "/dashboard",
      icon: <BarChart className="w-4 h-4" />,
    },
    {
      label: "Mon profil",
      href: user.is_professional
        ? "/dashboard/profile"
        : "/dashboard/profile-client-dashboard",
      icon: <User className="w-4 h-4" />,
    },
    {
      label: "Paramètres",
      href: "/settings",
      icon: <Settings className="w-4 h-4" />,
    },
    {
      label: "Se déconnecter",
      onClick: handleLogout,
      icon: <LogOut className="w-4 h-4" />,
    },
  ];

  const navLinks: NavLink[] = [];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  function handleLogout() {
    localStorage.removeItem("user");
    localStorage.removeItem("token");
    localStorage.removeItem("userProfile");
    navigate("/");
  }

  function handleHomeClick() {
    navigate("/");
  }

  function handleLoginClick() {
    setIsLoginModalOpen(true);
  }

  function handleRegisterClick() {
    navigate("/register");
  }

  return (
    <header
      className={`sticky top-0 z-40 w-full ${
        isScrolled ? "py-3" : "py-5"
      } bg-transparent`} // Changé de bg-white à bg-transparent
    >
      <div className="w-full flex items-center justify-between h-[60px] px-[40px]">
        <div className="flex items-center gap-4">
          <div
            onClick={handleHomeClick}
            className="flex-shrink-0 cursor-pointer"
          >
            <img
              src="/img/logo-Hi3d.svg"
              alt="Hi3D Logo"
              className="h-5 w-14"
            />
          </div>

          <div className="flex items-center gap-2">
            <button className="flex items-center justify-center rounded-lg p-3 bg-gray-100 w-[65px] h-[52px]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
                width="28"
                height="28"
              >
                <path d="M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm32 304h-64a16 16 0 010-32h64a16 16 0 010 32zm48-64H176a16 16 0 010-32h160a16 16 0 010 32zm32-64H144a16 16 0 010-32h224a16 16 0 010 32z"></path>
              </svg>
            </button>
            <SearchBar width={500} height={52} iconSize={30} />
          </div>
        </div>

        <div className="flex items-center gap-3">
          {isAuthenticated ? (
            <>
              <button
                type="button"
                className="rounded-full hover:bg-neutral-800"
              >
                <img
                  src="/img/icone/envelope-f.png"
                  alt="Messages"
                  className="w-6 h-6"
                />
              </button>
              <Dropdown
                trigger={
                  <button
                    type="button"
                    className="rounded-full hover:bg-neutral-800"
                  >
                    <img
                      src="/img/icone/user.png"
                      alt="User"
                      className="w-5 h-5"
                    />
                  </button>
                }
                items={userDropdownItems}
                align="right"
              />
              <button
                type="button"
                className="rounded-full hover:bg-neutral-800"
                onClick={() => navigate("/favorite")}
              >
                <img
                  src="/img/icone/favoris.png"
                  alt="Favoris"
                  className="w-7 h-7"
                />
              </button>
              {user.is_professional ? (
                <button className="bg-black text-white rounded-full px-4 py-2 text-sm font-medium">
                  Go Pro
                </button>
              ) : (
                <button
                  className="bg-black text-white rounded-full px-4 py-2 text-sm font-medium"
                  onClick={() => navigate("/dashboard/projects?create=true")}
                >
                  Create an Offer
                </button>
              )}
            </>
          ) : (
            <div className="hidden sm:flex items-center gap-4">
              <button
                onClick={handleLoginClick}
                className="bg-blue-600 hover:bg-black text-white rounded-full px-6 py-2 transition-colors"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                }}
              >
                Log in
              </button>
            </div>
          )}
          <button
            type="button"
            className="md:hidden p-2 rounded-md hover:bg-neutral-800"
            onClick={() => setIsMobileMenuOpen(true)}
          >
            <Menu className="h-6 w-6 text-black" />
          </button>
        </div>
      </div>

      {/* Les autres parties du composant restent inchangées */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        links={navLinks}
        authButtons={
          !isAuthenticated && (
            <div className="flex flex-col space-y-3">
              <button
                onClick={handleLoginClick}
                className="w-full px-4 py-2 rounded-full bg-white text-neutral-900 font-medium"
              >
                Log in
              </button>
              <button
                onClick={handleRegisterClick}
                className="w-full px-4 py-2 rounded-full border border-white text-white font-medium"
              >
                Sign up
              </button>
            </div>
          )
        }
      />

      {isLoginModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative">
            <LoginForm
              onClose={() => setIsLoginModalOpen(false)}
              onSuccess={() => {
                setIsLoginModalOpen(false);
                navigate("/dashboard");
              }}
              onToggleForm={() => {
                setIsLoginModalOpen(false);
                navigate("/register");
              }}
              onOpenRegister={() => {
                setIsLoginModalOpen(false);
                setIsRegisterModalOpen(true);
              }}
              onOpenForgotPassword={() => {
                setIsLoginModalOpen(false);
                setShowForgotPassword(true);
              }}
            />
          </div>
        </div>
      )}

      {isRegisterModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative">
            <RegisterForm
              onClose={() => setIsRegisterModalOpen(false)}
              onToggleForm={() => {
                setIsRegisterModalOpen(false);
                setIsLoginModalOpen(true);
              }}
            />
          </div>
        </div>
      )}

      {showForgotPassword && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative">
            <ForgotPasswordForm
              onClose={() => setShowForgotPassword(false)}
              onBackToLogin={() => {
                setShowForgotPassword(false);
                setIsLoginModalOpen(true);
              }}
            />
          </div>
        </div>
      )}

      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-11/12 sm:w-3/4 max-w-lg relative">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute top-3 right-3 text-neutral-500 hover:text-neutral-800"
            >
              ✖
            </button>
            <ProfileWizard />
          </div>
        </div>
      )}
    </header>
  );
}

export default Header;