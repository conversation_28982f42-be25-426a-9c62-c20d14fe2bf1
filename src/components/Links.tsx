// import React, { useState } from "react";
// import { X } from "lucide-react";
// import { API_BASE_URL } from '../config';
// import { useProfile } from "./ProfileContext"; 

// const Links = () => {
//     const { profile, setProfile } = useProfile();
//     const storedProfile = JSON.parse(localStorage.getItem("userProfile") || "{}");
//     const initiallanguages = storedProfile?.profile_data?.languages || [];
  
//     const [languages, setlanguages] = useState<string[]>(initiallanguages);
//     const [newlanguage, setNewlanguage] = useState<string>("");
//     const [loading, setLoading] = useState(false);
//     const [message, setMessage] = useState("");
  
//     // Ajouter une compétence
//     const handleAddlanguage = () => {
//       if (newlanguage.trim() !== "" && !languages.includes(newlanguage.trim())) {
//         setlanguages([...languages, newlanguage.trim()]);
//         setNewlanguage("");
//       }
//     };
  
//     // Supprimer une compétence
//     const handleRemovelanguage = (languageToRemove: string) => {
//       setlanguages(languages.filter((language) => language !== languageToRemove));
//     };
  
//     // Gestion de l'entrée utilisateur
//     const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//       setNewlanguage(e.target.value);
//     };
  
//     // Ajouter une compétence avec "Entrée"
//     const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
//       if (e.key === "Enter") {
//         handleAddlanguage();
//       }
//     };
  
//     // Envoyer les compétences à l'API
//     const handleSubmit = async () => {
//       setLoading(true);
//       setMessage("");
  
//       const token = localStorage.getItem("token");
  
//       try {
//         const response = await fetch(
//           `${API_BASE_URL}/api/profile/completion/languages`,
//           {
//             method: "PUT",
//             headers: {
//               Authorization: `Bearer ${token}`,
//               "Content-Type": "application/json",
//             },
//             body: JSON.stringify({ languages }),
//           }
//         );
  
//         const data = await response.json();
  
//         if (response.ok) {
//           setMessage("✅ Langues mises à jour avec succès !");
//           // Mettre à jour le localStorage
//           const updatedProfile = {
//             ...storedProfile,
//             profile_data: {
//               ...storedProfile.profile_data,
//               languages: languages,
//             },
//           };

//           const updateJiab = {
//             ...profile,
//           profile_data: {
//             ...storedProfile.profile_data,
//             languages: languages,
//           },
//           };
//           localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
//           setProfile(updateJiab)
//         } else {
//           setMessage("❌ Erreur : " + (data.message || "Impossible d'enregistrer les langues."));
//         }
//       } catch (error) {
//         setMessage("❌ Erreur réseau, veuillez réessayer.");
//       } finally {
//         setLoading(false);
//       }
//     };
  
//     return (
//       <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
//         <h2 className="text-sm font-bold text-gray-700 mb-4">MES LANGUAGES</h2>
  
//         {message && (
//           <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
//             {message}
//           </div>
//         )}
  
//         <div className="space-y-4">
//           <div className="grid grid-cols-1 gap-4">
//             <h3 className="text-lg font-semibold mb-4">Langues</h3>
//             <input
//               type="text"
//               placeholder="Ajouter une compétence"
//               value={newlanguage}
//               onChange={handleInputChange}
//               onKeyDown={handleKeyDown}
//               className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
//             />
//           </div>
  
//           {/* Liste des compétences ajoutées */}
//           <div className="flex gap-2 flex-wrap mt-4">
//             {languages.map((language, index) => (
//               <div key={index} className="flex items-center gap-2 bg-gray-200 rounded-lg px-3 py-1">
//                 <span>{language}</span>
//                 <button type="button" onClick={() => handleRemovelanguage(language)} className="text-red-500">
//                   <X size={14} />
//                 </button>
//               </div>
//             ))}
//           </div>
  
//           {/* Bouton pour ajouter la compétence */}
//           <div className="mt-4">
//             <button type="button" onClick={handleAddlanguage} className="px-4 py-2 bg-green-500 text-white rounded-md">
//               Ajouter la langue
//             </button>
//           </div>
  
//           {/* Bouton pour envoyer les compétences à l'API */}
//           <div className="mt-4">
//             <button
//               type="button"
//               onClick={handleSubmit}
//               className="px-4 py-2 bg-blue-500 text-white rounded-md"
//               disabled={loading}
//             >
//               {loading ? "Envoi en cours..." : "Enregistrer mes langues"}
//             </button>
//           </div>
//         </div>
//       </div>
//     );
// };

// export default Links;

import React, { useState } from "react";
import { X } from "lucide-react";
import { API_BASE_URL, LANGUAGES } from '../config';
import { useProfile } from "./ProfileContext";

const Links = () => {
  const { profile, setProfile } = useProfile();
  const storedProfile = JSON.parse(localStorage.getItem("userProfile") || "{}");
  const initialLanguages = storedProfile?.profile_data?.languages || [];

  const [languages, setLanguages] = useState<string[]>(initialLanguages);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [search, setSearch] = useState("");

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
    setLanguages(selectedOptions);
  };

  const handleRemoveLanguage = (languageToRemove: string) => {
    setLanguages(languages.filter((language) => language !== languageToRemove));
  };

  const handleSubmit = async () => {
    setLoading(true);
    setMessage("");

    const token = localStorage.getItem("token");

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/profile/completion/languages`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ languages }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        setMessage("✅ Langues mises à jour avec succès !");
        const updatedProfile = {
          ...storedProfile,
          profile_data: {
            ...storedProfile.profile_data,
            languages,
          },
        };
        const updateJiab = {
          ...profile,
          profile_data: {
            ...storedProfile.profile_data,
            languages,
          },
        };
        localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
        setProfile(updateJiab);
      } else {
        setMessage("❌ Erreur : " + (data.message || "Impossible d'enregistrer les langues."));
      }
    } catch (error) {
      setMessage("❌ Erreur réseau, veuillez réessayer.");
    } finally {
      setLoading(false);
    }
  };

  // const filteredLanguages = LANGUAGES.filter(lang =>
  //   lang.name_fr.toLowerCase().includes(search.toLowerCase())
  // );

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      <h2 className="text-sm font-bold text-gray-700 mb-4">MES LANGUES</h2>

      {message && (
        <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
          {message}
        </div>
      )}

      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <h3 className="text-lg font-semibold mb-4">Langues parlées</h3>

          <input
            type="text"
            placeholder="Rechercher une langue..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />

          <select
            multiple
            value={languages}
            onChange={handleSelectChange}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 h-48"
          >
            {/* {filteredLanguages.map(lang => (
              <option key={lang.code} value={lang.code}>
                {lang.name_fr} ({lang.code})
              </option>
            ))} */}
          </select>
        </div>

        <div className="flex gap-2 flex-wrap mt-4">
          {languages.map((language, index) => {
            // const lang = LANGUAGES.find(l => l.code === language);
            return (
              <div key={index} className="flex items-center gap-2 bg-gray-200 rounded-lg px-3 py-1">
                {/* <span>{lang ? lang.name_fr : language}</span> */}
                <button type="button" onClick={() => handleRemoveLanguage(language)} className="text-red-500">
                  <X size={14} />
                </button>
              </div>
            );
          })}
        </div>

        <div className="mt-4">
          <button
            type="button"
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-500 text-white rounded-md"
            disabled={loading}
          >
            {loading ? "Envoi en cours..." : "Enregistrer mes langues"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Links;

