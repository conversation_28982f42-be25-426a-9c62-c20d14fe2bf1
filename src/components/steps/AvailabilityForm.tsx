import React from 'react';
import type { ProfileFormData } from '../types';

interface AvailabilityFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

export default function AvailabilityForm({ data, onChange }: AvailabilityFormProps) {
  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Disponibilités</h3>
        <div className="space-y-6">
          <div>
            
            <select
              id="availability"
              value={data.availability || ''}  // La valeur actuelle de disponibilité
              onChange={(e) => 
                onChange({ availability: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="available">Disponible</option>
              <option value="unavailable">Non Disponible</option>
              {/* <option value="vacation">En vacances</option> */}
            </select>
          </div>

          {/* Détails de disponibilité (JSON) */}
          <div>
            <label htmlFor="availability_details" className="block text-sm font-medium text-gray-700">
              Détails de disponibilité (JSON)
            </label>
            <textarea
              id="availability_details"
              value={data.availability_details || ''}
              onChange={(e) => onChange({ availability_details: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder='{"start": "08:00", "end": "18:00"}'
            ></textarea>
          </div>

          {/* Sélection de la date et/ou heure de réponse estimée */}
          <div>
            <label htmlFor="estimated_response_time" className="block text-sm font-medium text-gray-700">
              Temps de réponse estimé
            </label>
            <input
              id="estimated_response_time"
              type="datetime-local"
              value={data.estimated_response_time || ''}
              onChange={(e) => onChange({ estimated_response_time: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
        </div>
      </div>
    </div>
  );
}