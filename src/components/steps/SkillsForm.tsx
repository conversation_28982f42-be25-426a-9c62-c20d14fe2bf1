import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import type { ProfileFormData } from '../types';

interface SkillsFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

export default function SkillsForm({ data, onChange }: SkillsFormProps) {
  const [newSkill, setNewSkill] = useState<string>(''); // État pour la compétence actuelle

  // Fonction pour ajouter la compétence au tableau
  const handleAddSkill = () => {
    if (newSkill.trim() !== '') {
      const updatedSkills = [...data.skills, newSkill.trim()];
      onChange({ skills: updatedSkills });
      setNewSkill(''); // Réinitialiser l'entrée après ajout
    }
  };

  // Fonction pour gérer la suppression d'une compétence
  const handleRemoveSkill = (skillToRemove: string) => {
    const updatedSkills = data.skills.filter(skill => skill !== skillToRemove);
    onChange({ skills: updatedSkills });
  };

  // Fonction pour gérer la saisie de texte
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewSkill(e.target.value);
  };

  // Fonction pour ajouter une compétence quand "Entrée" est pressé
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleAddSkill();
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <h3 className="text-lg font-semibold mb-4">Compétences</h3>
            <input
              type="text"
              placeholder="Compétence"
              value={newSkill}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Affichage des compétences ajoutées sous forme de tags */}
          <div className="flex gap-2 flex-wrap mt-4">
            {data.skills.map((skill, index) => (
              <div key={index} className="flex items-center gap-2 bg-gray-200 rounded-lg px-3 py-1">
                <span>{skill}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveSkill(skill)}
                  className="text-red-500"
                >
                  <X size={14} />
                </button>
              </div>
            ))}
          </div>

          {/* Bouton pour ajouter la compétence manuellement */}
          <div className="mt-4">
            <button
              type="button"
              onClick={handleAddSkill}
              className="px-4 py-2 bg-blue-500 text-white rounded-md"
            >
              Ajouter la compétence
            </button>
          </div>          
        </div>
      </div>
    </div>
  );
}
