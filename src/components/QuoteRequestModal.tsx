import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header, DialogTitle } from './ui/dialog';
import { Button } from './ui/buttons';
import { Plus } from 'lucide-react';
import { API_BASE_URL } from '../config';
import { useParams, useNavigate } from 'react-router-dom';

interface QuoteRequestModalProps {
  token: string|undefined|null;
  user: any;
  pro: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const QuoteRequestModal = ({ token,user,pro, open, onOpenChange }: QuoteRequestModalProps) => {
  const [projectName, setProjectName] = useState('');
  const navigate = useNavigate();
  const [offers, setOffers] = useState([]);
  const [selectedOfferId, setSelectedOfferId] = useState(null);
  const [showModal, setShowModal] = useState(false);
    const [loadingInvite, setLoadingInvitation] = useState<boolean>(false);
  
    console.log("Utisateur: ",user);

  const getImageUrl = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
      if (!imagePath) return defaultImage;
      if (imagePath.startsWith('http')) return imagePath;
      if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
      return `${API_BASE_URL}/${imagePath}`;
    };

  
    // 2. Envoyer l'invitation
      const sendInvitation = async (idOffer : any) => {
        // if (!selectedOfferId) return alert("Sélectionne une offre.");
        if (!token) return alert("Vous devez vous connecter pour invité un pro.");
        setLoadingInvitation(true);
        try {
          const res = await fetch(`${API_BASE_URL}/api/open-offers/${idOffer}/invite`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ professional_id: pro?.user_id }),
          });
    
          console.log("professional_id===========", pro?.user_id)
    
          const result = await res.json();
          if (res.ok) {
            alert("Invitation envoyée avec succès !");
            setShowModal(false);
          } else {
            alert("Erreur : " + result.message);
          }
        } catch (err) {
          console.error(err);
          alert("Une erreur est survenue.");
        } finally {
          setLoadingInvitation(false);
        }
      };

  // Fonction pour créer une offre ouverte avec invitation automatique
  const handleCreateOpenOffer = () => {
    // const token = localStorage.getItem('token');
    
    if(token){
      if(user && !user.is_professional) {
        navigate(`/dashboard/projects?invite=${pro?.user_id}&create=true`);
      }else{
         alert("Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer.");
      }
    }else{
      navigate('/login');
    }

  };

  const handleInvitePro = () => {
    // const token = localStorage.getItem('token');
    if(token){
      if(user && !user.is_professional) {
        setShowModal(true);
      }else{
         alert("Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer.");
      }
      
    }else{
      navigate('/login');
    }

  };

  return (
     <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="max-w-2xl w-[92vw] h-[80vh] p-0 overflow-y-auto ring-1 ring-black/5 rounded-none bg-white"
        // className="max-w-2xl w-[92vw] p-0 overflow-hidden ring-1 ring-black/5"
      >
       
        <div className="bg-white">
          <DialogHeader className="mb-4 text-center">
            <div className="w-full md:h-80 overflow-hidden rounded-b-2xl">
              <img
                src={getImageUrl?.(pro?.cover_photo, "/img/popup.png")}
                alt={`${pro?.first_name ?? ""} ${pro?.last_name ?? ""}`.trim()}
                className="w-full h-full object-cover block"
                onError={(e) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = "/img/popup.png";
                }}
              />
            </div>
            <DialogTitle className="text-3xl md:text-4xl font-semibold tracking-tight text-gray-900 text-center">
              Request a quote from
            </DialogTitle>
          </DialogHeader>

          <h3 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2 text-center">
            {`${pro?.first_name ?? "Jack"} ${pro?.last_name ?? "and Moris Render"}`}
          </h3>

          <p className="text-gray-600 max-w-xl mx-auto leading-relaxed mb-6 text-center">
            Main advantage of using the application is: it helps you save time by centralizing all your project information in one place.
          </p>

          {/* CTA buttons */}
          <div className="space-y-4 max-w-xl mx-auto">
            {/* Primary */}
            <button
              type="button"
              onClick={handleCreateOpenOffer}
              className="w-full h-12 rounded-full bg-black text-white font-medium hover:bg-gray-900 transition"
            >
              Create a new quote request
            </button>

            {offers?.map((offer: any) => (
              <button
                key={offer.id}
                disabled={loadingInvite}
                type="button"
                onClick={() => sendInvitation(offer.id)}
                className="w-full h-12 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium transition flex items-center justify-center gap-3"
              >
                <Plus className="w-5 h-5" /> {loadingInvite ? "Envoi..." : `Invite to an open offer : ${offer.title}`}
              </button>
            ))}
          </div>
        </div>


        {/* Inner modal for selecting an offer (unchanged, restyled slightly) */}
        {showModal && (
          <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-[100]">
            <div className="bg-white rounded-2xl w-full max-w-md p-6 relative shadow-lg">
              <button onClick={() => setShowModal(false)} className="absolute top-3 right-3 text-gray-500">×</button>
              <h2 className="text-lg font-semibold mb-4">Sélectionner une offre</h2>
              <div className="space-y-2 max-h-60 overflow-y-auto pr-1">
                {offers?.map((offer: any) => (
                  <label key={offer.id} className="flex items-center gap-2 text-sm">
                    <input
                      type="radio"
                      value={offer.id}
                      checked={selectedOfferId === offer.id}
                      onChange={() => setSelectedOfferId(offer.id)}
                    />
                    <span>{offer.title}</span>
                  </label>
                ))}
              </div>
              <button
                onClick={sendInvitation}
                className="mt-4 w-full h-10 rounded-md bg-green-600 text-white hover:bg-green-700 transition"
                disabled={loadingInvite}
              >
                {loadingInvite ? "Envoi..." : "Envoyer l'invitation"}
              </button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default QuoteRequestModal;