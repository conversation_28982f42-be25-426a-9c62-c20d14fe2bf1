import React from "react";
import { useNavigate } from "react-router-dom";

interface SearchBarProps {
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  width?: number | string;
  height?: number;
  iconSize?: number;
  onSearchClick?: () => void; // Nouvelle prop
}

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = "What type of 3d artistes are you looking for ?",
  width = "100%",
  height = 52,
  iconSize = 30,
  onSearchClick,
}) => {
  const navigate = useNavigate();

  const handleSearchClick = () => {
    if (onSearchClick) {
      onSearchClick();
    } else {
      // Comportement par défaut si onSearchClick n'est pas fourni
      navigate("/search-global");
    }
  };

  return (
    <div className="relative" style={{ width }}>
      <input
        type="text"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className="rounded-lg py-2 px-4 pr-16 focus:outline-none focus:ring-2 focus:ring-neutral-300 placeholder-black w-full"
        style={{
          height: `${height}px`,
          fontSize: "14px",
          fontFamily: "'Inter', sans-serif",
          fontWeight: 300,
          lineHeight: "1em",
          backgroundColor: "#F5F5F5",
          borderRadius: "12px",
          color: "#000000",
          border: "none",
          letterSpacing: "0.5px",
          paddingRight: `${iconSize + 20}px`,
        }}
      />
      <button
        className="absolute right-3 top-1/2 transform -translate-y-1/2"
        onClick={handleSearchClick}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width={iconSize}
          height={iconSize}
          viewBox="0 0 63 63"
          fill="none"
        >
          <circle cx="31.2031" cy="31.3984" r="31" fill="#212121"></circle>
          <path
            d="M29.8507 38.6895C31.2272 39.2717 32.6887 39.5669 34.1945 39.5669C35.7003 39.5669 37.1618 39.2717 38.5383 38.6895C39.8673 38.1274 41.0604 37.323 42.0848 36.2987C43.1091 35.2743 43.9135 34.0812 44.4756 32.7522C45.0578 31.3757 45.353 29.9143 45.353 28.4085C45.353 26.9026 45.0578 25.4412 44.4756 24.0647C43.9135 22.7357 43.1091 21.5426 42.0848 20.5182C41.0604 19.4939 39.8673 18.6895 38.5383 18.1274C37.1618 17.5452 35.7004 17.25 34.1945 17.25C32.6888 17.25 31.2273 17.5452 29.8507 18.1274C28.5218 18.6895 27.3287 19.4939 26.3043 20.5182C25.28 21.5426 24.4756 22.7357 23.9135 24.0647C23.3313 25.4412 23.0361 26.9026 23.0361 28.4085C23.0361 29.9142 23.3313 31.3757 23.9135 32.7522C24.211 33.4558 24.5772 34.1208 25.0073 34.7433L17.6435 42.1071C16.8558 42.8948 16.8558 44.1718 17.6435 44.9595C18.0373 45.3533 18.5535 45.5503 19.0697 45.5503C19.5859 45.5503 20.1021 45.3533 20.4959 44.9595L27.8597 37.5957C28.4822 38.0257 29.1472 38.3919 29.8507 38.6895ZM28.203 22.4169C29.8034 20.8166 31.9312 19.9352 34.1945 19.9352C36.4578 19.9352 38.5856 20.8166 40.186 22.4169C41.7864 24.0173 42.6678 26.1451 42.6678 28.4085C42.6678 30.6718 41.7864 32.7996 40.186 34.3999C38.5856 36.0003 36.4578 36.8817 34.1945 36.8817C31.9312 36.8817 29.8034 36.0003 28.203 34.3999C26.6027 32.7996 25.7213 30.6718 25.7213 28.4085C25.7213 26.1452 26.6027 24.0174 28.203 22.4169Z"
            fill="white"
          ></path>
        </svg>
      </button>
    </div>
  );
};

export default SearchBar;
