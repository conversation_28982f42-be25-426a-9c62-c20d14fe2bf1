import React, { useRef, useState } from "react";

type Props = {
  onCategorySelect: (category: string) => void;
  selectedCategory: string;
};

const CategoryList: React.FC<Props> = ({
  onCategorySelect,
  selectedCategory,
}) => {
  const [showFilters, setShowFilters] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const categories: string[] = [
    "Modélisation 3D",
    "Animation 3D",
    "Rendu 3D",
    "Texturing & Shading",
    "Rigging & Skinning",
    "Compositing 3D",
    "Effets Visuels (VFX)",
    "Architecture 3D",
    "Jeux Vidéo 3D",
    "Produit 3D & Industriel",
    "Personnages 3D",
    "Environnements 3D",
    "Motion Design 3D",
    "Réalité Virtuelle (VR)",
    "Réalité Augmentée (AR)",
    "Scan 3D & Photogrammétrie",
    "Modélisation Médicale 3D",
    "Impression 3D",
    "Simulations Physiques",
    "Éclairage 3D",
  ];

  return (
    <div className="w-full max-w-[1440px] mx-auto my-8 px-0 font-sans">
      {/* Desktop layout */}
      <div className="hidden md:flex items-start gap-6">
        {/* Bouton Filtre */}
        <div className="flex-shrink-0 pt-[6px]">
          <button
            className="flex items-center justify-center bg-[#F5F5F5] text-gray-500 border-none rounded-full py-2 px-6 h-10 cursor-pointer transition-colors duration-200 shadow-sm gap-2"
            style={{
              fontFamily: "Arial, sans-serif",
              fontSize: "16px",
              color: "#18181B",
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="ionicon"
              viewBox="0 0 512 512"
              width="20"
              height="20"
            >
              <path d="M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm32 304h-64a16 16 0 010-32h64a16 16 0 010 32zm48-64H176a16 16 0 010-32h160a16 16 0 010 32zm32-64H144a16 16 0 010-32h224a16 16 0 010 32z"></path>
            </svg>
            Filtre
          </button>
        </div>

        {/* Conteneur scrollable des catégories */}
        <div
          ref={scrollContainerRef}
          className="flex-1 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent pb-2"
        >
          <div className="flex gap-8 pt-[6px]">
            {categories.map((cat) => (
              <button
                key={cat}
                onClick={() => onCategorySelect(cat)}
                className={`px-4 py-2 text-[15px] rounded-full cursor-pointer transition-all duration-200 flex-shrink-0 whitespace-nowrap ${
                  selectedCategory === cat
                    ? "bg-[#F5F5F5] text-gray-500 font-medium"
                    : "text-black font-normal hover:bg-[#F5F5F5] hover:text-gray-500"
                }`}
                style={{
                  fontFamily: "Arial, sans-serif",
                }}
              >
                {cat}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile layout */}
      <div className="md:hidden w-full">
        <div className="flex items-center justify-between mb-4">
          <button className="border border-gray-200 rounded-lg px-4 py-2 bg-white font-medium text-[13px] text-gray-700 cursor-pointer flex items-center">
            <span>Popular</span>
            <svg
              width="14"
              height="14"
              viewBox="0 0 20 20"
              fill="none"
              className="ml-1.5"
            >
              <path
                d="M6 8L10 12L14 8"
                stroke="#a1a1aa"
                strokeWidth="2.2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <div className="relative">
            <button
              className="border border-gray-200 rounded-3xl px-4 py-2 bg-white font-medium text-[14px] text-gray-700 cursor-pointer flex items-center"
              onClick={() => setShowFilters(!showFilters)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                className="mr-2"
              >
                <path
                  d="M3 6.5C3 6.22386 3.22386 6 3.5 6H16.5C16.7761 6 17 6.22386 17 6.5C17 6.77614 16.7761 7 16.5 7H3.5C3.22386 7 3 6.77614 3 6.5ZM5 10.5C5 10.2239 5.22386 10 5.5 10H14.5C14.7761 10 15 10.2239 15 10.5C15 10.7761 14.7761 11 14.5 11H5.5C5.22386 11 5 10.7761 5 10.5ZM8 14.5C8 14.2239 8.22386 14 8.5 14H11.5C11.7761 14 12 14.2239 12 14.5C12 14.7761 11.7761 15 11.5 15H8.5C8.22386 15 8 14.7761 8 14.5Z"
                  fill="#18181b"
                />
              </svg>
              Filter
            </button>

            {showFilters && (
              <div className="absolute top-12 right-0 bg-white border border-gray-200 rounded-lg p-6 shadow-lg z-50 min-w-[260px]">
                <div className="mb-4">
                  <label className="font-medium block mb-1">Tags</label>
                  <input
                    type="text"
                    placeholder="Search tags..."
                    className="w-full px-3 py-2 rounded border border-gray-200 text-sm"
                  />
                </div>
                <div className="mb-4">
                  <label className="font-medium block mb-1">Color</label>
                  <input
                    type="text"
                    placeholder="Enter hex or select"
                    className="w-full px-3 py-2 rounded border border-gray-200 text-sm"
                  />
                </div>
                <div className="mb-4">
                  <label className="font-medium block mb-1">Timeframe</label>
                  <select className="w-full px-3 py-2 rounded border border-gray-200 text-sm">
                    <option>Select a Timeframe</option>
                    <option>Today</option>
                    <option>This week</option>
                    <option>This month</option>
                  </select>
                </div>
                <button
                  className="mt-2 bg-gray-100 border-none rounded-md px-4 py-2 font-medium cursor-pointer w-full hover:bg-gray-200"
                  onClick={() => setShowFilters(false)}
                >
                  Fermer
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Catégories mobiles */}
        <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent pb-2">
          <div className="flex gap-6">
            {categories.map((cat) => (
              <button
                key={cat}
                onClick={() => onCategorySelect(cat)}
                className={`px-3 py-2 text-[14px] cursor-pointer flex-shrink-0 whitespace-nowrap ${
                  selectedCategory === cat
                    ? "text-black font-medium border-b-2 border-black"
                    : "text-gray-600 font-normal"
                }`}
                style={{
                  fontFamily: "Arial, sans-serif",
                }}
              >
                {cat}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryList;