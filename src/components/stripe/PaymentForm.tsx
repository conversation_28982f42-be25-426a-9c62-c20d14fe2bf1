import React, { useState } from 'react';
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { API_BASE_URL } from '../../config';
import './PaymentForm.css';

interface PaymentFormProps {
  selectedPlan: {
    name: string;
    price: number;
  } | null;
}

type PlanType = 'free' | 'pro' | 'enterprise';

interface PlanDetails {
  plan_id: number;
  stripe_price_id: string;
}

const CARD_OPTIONS = {
  style: {
    base: {
      iconColor: "#0570de",
      color: "#30313d",
      fontWeight: 500,
      fontFamily: '"Gill Sans", sans-serif',
      fontSize: "16px",
      fontSmoothing: "antialiased",
      backgroundColor: "#F6F8FA",
      borderRadius: "10px",
      padding: "12px",
      "::placeholder": {
        color: "#aab7c4",
      },
    },
    invalid: {
      iconColor: "#df1b41",
      color: "#df1b41",
    },
  },
  hidePostalCode: true,
};

// Fonction pour normaliser le nom du plan
const normalizePlanName = (name: string): PlanType => {
  const normalized = name.toLowerCase().trim();
  switch (normalized) {
    case 'entreprise':
    case 'enterprise':
      return 'enterprise';
    case 'pro':
      return 'pro';
    case 'free':
    case 'gratuit':
      return 'free';
    default:
      throw new Error(`Plan non reconnu: ${name}`);
  }
};

const PaymentForm: React.FC<PaymentFormProps> = ({ selectedPlan }) => {
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const stripe = useStripe();
  const elements = useElements();

  // Mapping des plans avec leurs IDs Stripe
  const planMapping: Record<PlanType, PlanDetails> = {
    'free': {
      plan_id: 1,
      stripe_price_id: 'price_1RSlU5FKK6JoGdxmf9PuiJfw'
    },
    'pro': {
      plan_id: 2,
      stripe_price_id: 'price_1RSlXxFKK6JoGdxmSv8SKhAk'
    },
    'enterprise': {
      plan_id: 3,
      stripe_price_id: 'price_1RSlZpFKK6JoGdxmRq9XwONW'
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setProcessing(true);
  
    if (!stripe || !elements || !selectedPlan) {
      setProcessing(false);
      return;
    }
  
    // Récupérer l'ID utilisateur depuis localStorage
    const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  
    // Vérifier si l'objet utilisateur est valide et contient un ID
    if (!currentUser || !currentUser.id) {
      setError('Informations utilisateur non disponibles. Veuillez vous reconnecter.');
      setProcessing(false);
      return;
    }
  
    const currentUserId = currentUser.id; // Récupérer l'ID utilisateur depuis l'objet currentUser
  
    try {
      const planName = normalizePlanName(selectedPlan.name);
      const planDetails = planMapping[planName];
  
      if (!planDetails) {
        setError(`Plan "${selectedPlan.name}" non trouvé.`);
        setProcessing(false);
        return;
      }
  
      // Créer la méthode de paiement
      const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
        type: "card",
        card: elements.getElement(CardElement)!,
      });
  
      if (paymentMethodError) {
        setError(paymentMethodError.message || 'Erreur lors du paiement');
        setProcessing(false);
        return;
      }
  
      // Appel à l'API pour créer le paiement
      const response = await fetch(`${API_BASE_URL}/api/subscriptions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: currentUserId, // Utiliser l'ID récupéré de localStorage
          plan_id: planDetails.plan_id,
          stripe_price_id: planDetails.stripe_price_id,
          payment_method_id: paymentMethod.id,
        }),
      });
  
      const data = await response.json();
  
      if (data.requires_action) {
        // Confirmer le paiement avec 3D Secure
        const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(
          data.client_secret,
          {
            payment_method: paymentMethod.id,
          }
        );
  
        if (confirmError) {
          setError(confirmError.message || 'Erreur 3D Secure');
          setProcessing(false);
          return;
        }
  
        // Si le paiement est confirmé
        if (paymentIntent && paymentIntent.status === 'succeeded') {
          // Confirmer le paiement côté serveur
          const confirmResponse = await fetch(`${API_BASE_URL}/api/subscriptions/confirm`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              payment_intent_id: paymentIntent.id,
            }),
          });
  
          const confirmData = await confirmResponse.json();
  
          if (confirmData.success) {
            setSuccess(true);
          } else {
            setError(confirmData.message || 'Erreur de confirmation');
          }
        } else {
          setError('Le paiement n\'a pas été confirmé');
        }
      } else if (data.success) {
        // Paiement réussi sans authentification 3D Secure
        setSuccess(true);
      } else {
        setError(data.message || 'Erreur lors du paiement');
      }
    } catch (err) {
      console.error('Erreur:', err);
      setError(err instanceof Error ? err.message : 'Erreur inconnue');
    } finally {
      setProcessing(false);
    }
  };

  return (
    <>
      {!success ? (
        <div className="stripe-form-container">
          <form onSubmit={handleSubmit}>
            <h3>Formulaire de paiement</h3>
            <fieldset>
              <div className="form-group">
                <CardElement options={CARD_OPTIONS} />
              </div>
            </fieldset>
            {error && <div className="error-message">{error}</div>}
            <button type="submit" disabled={processing}>
              {processing ? 'Traitement en cours...' : 'Payer'}
            </button>
          </form>
        </div>
      ) : (
        <div>
          <h2>Paiement effectué avec succès !</h2>
        </div>
      )}
    </>
  );
};

export default PaymentForm; 