import { useState, useEffect } from "react";
import { API_BASE_URL } from '../config';

interface Experience {
  id: number;
  title: string;
  company_name: string;
  start_date: string;
  end_date: string | '';
  description: string;
}

const ExperiencePro = () => {
  const [experiences, setExperiences] = useState<Experience[]>([]);
  const [newExperience, setNewExperience] = useState<Partial<Experience>>({
    title: "",
    company_name: "",
    start_date: "",
    end_date: "",
    description: "",
  });
  const [editingExperience, setEditingExperience] = useState<Experience | null>(null);

  useEffect(() => {
    const fetchExperiences = async () => {
      const token = localStorage.getItem("token");
      if (!token) return;

      try {
        const response = await fetch(`${API_BASE_URL}/api/experiences`, {
          method: "GET",
          headers: { Authorization: `Bearer ${token}` },
        });
        const data = await response.json();

        if (response.ok) {
          setExperiences(data.experiences);
        } else {
          console.error("Erreur lors de la récupération :", data.message);
        }
      } catch (error) {
        console.error("Erreur de requête :", error);
      }
    };

    fetchExperiences();
  }, []);

  const addExperience = async () => {
    const token = localStorage.getItem("token");
    if (!token) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/experiences`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newExperience),
      });

      const data = await response.json();

      if (response.ok) {
        setExperiences([...experiences, data.experience]);
        resetForm();
      } else {
        console.error("Erreur d'ajout :", data.message);
      }
    } catch (error) {
      console.error("Erreur de requête :", error);
    }
  };

  const updateExperience = async () => {
    if (!editingExperience) return;
    const token = localStorage.getItem("token");
    if (!token) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/experiences/${editingExperience.id}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newExperience),
      });

      const data = await response.json();

      if (response.ok) {
        setExperiences(experiences.map(exp => (exp.id === editingExperience.id ? data.experience : exp)));
        resetForm();
      } else {
        console.error("Erreur de modification :", data.message);
      }
    } catch (error) {
      console.error("Erreur de requête :", error);
    }
  };

  const deleteExperience = async (id: number) => {
    const token = localStorage.getItem("token");
    if (!token) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/experiences/${id}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        setExperiences(experiences.filter(exp => exp.id !== id));
      } else {
        console.error("Erreur de suppression");
      }
    } catch (error) {
      console.error("Erreur de requête :", error);
    }
  };

  const handleEdit = (experience: Experience) => {
    setEditingExperience(experience);
    setNewExperience({ ...experience }); // Copier l'expérience sélectionnée dans newExperience
  };

  const resetForm = () => {
    setNewExperience({ title: "", company_name: "", start_date: "", end_date: "", description: "" });
    setEditingExperience(null);
  };

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      <h2 className="text-3xl font-bold mb-6 text-gray-800">Expérience</h2>

      {experiences.map(exp => (
        <div key={exp.id} className="p-4 border rounded-lg mb-4">
          <h3 className="font-bold">{exp.title}</h3>
          <p>{exp.company_name}</p>
          {/* <p>{exp.start_date} - {exp.end_date || "Présent"}</p> */}
          <p>{new Date(exp.start_date).toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' })} - {new Date(exp.end_date).toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' }) || "Présent"}</p>
          <p>{exp.description}</p>
          <button onClick={() => handleEdit(exp)} className="mr-2 text-blue-500">Modifier</button>
          <button onClick={() => deleteExperience(exp.id)} className="text-red-500">Supprimer</button>
        </div>
      ))}

      <div className="bg-gray-100 p-6 rounded-xl shadow-sm">
        <h3 className="text-lg font-semibold mb-4">{editingExperience ? "Modifier l'expérience" : "Ajouter une expérience"}</h3>
        
        <input 
          type="text" 
          placeholder="Titre du poste" 
          value={newExperience.title} 
          onChange={(e) => setNewExperience({ ...newExperience, title: e.target.value })} 
          className="w-full p-3 border rounded-lg mb-3" 
        />
        <input 
          type="text" 
          placeholder="Nom de l'entreprise" 
          value={newExperience.company_name} 
          onChange={(e) => setNewExperience({ ...newExperience, company_name: e.target.value })} 
          className="w-full p-3 border rounded-lg mb-3" 
        />
        
        <div className="flex gap-4">
           <input
            type="date"
            value={newExperience.start_date}
            onChange={(e) => setNewExperience({ ...newExperience, start_date: e.target.value })}
            className="w-1/2 p-3 border rounded-lg mb-3"
          />

          <input
            type="date"
            value={newExperience.end_date || ""}
            onChange={(e) => setNewExperience({ ...newExperience, end_date: e.target.value })}
            className="w-1/2 p-3 border rounded-lg mb-3"
          />
        </div>

        <textarea
          placeholder="Description"
          value={newExperience.description}
          onChange={(e) => setNewExperience({ ...newExperience, description: e.target.value })}
          className="w-full p-3 border rounded-lg mb-3"
        />
        <button onClick={addExperience} className="bg-green-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-600 transition">Ajouter</button>
      </div>
    </div>
  );
};

export default ExperiencePro;


// import { useState, useEffect } from "react";
// import { API_BASE_URL } from '../config';

// interface Experience {
//   id: number;
//   title: string;
//   company_name: string;
//   start_date: string;
//   end_date: string | '';
//   description: string;
// }

// const ExperiencePro = () => {
//   const [experiences, setExperiences] = useState<Experience[]>([]);
//   const [newExperience, setNewExperience] = useState<Partial<Experience>>({
//     title: "",
//     company_name: "",
//     start_date: "",
//     end_date: "",
//     description: "",
//   });
//   const [editingExperience, setEditingExperience] = useState<Experience | null>(null);

//   useEffect(() => {
//     const fetchExperiences = async () => {
//       const token = localStorage.getItem("token");
//       if (!token) return;

//       try {
//         const response = await fetch(`${API_BASE_URL}/api/experiences`, {
//           method: "GET",
//           headers: { Authorization: `Bearer ${token}` },
//         });
//         const data = await response.json();

//         if (response.ok) {
//           setExperiences(data.experiences);
//         } else {
//           console.error("Erreur lors de la récupération :", data.message);
//         }
//       } catch (error) {
//         console.error("Erreur de requête :", error);
//       }
//     };

//     fetchExperiences();
//   }, []);

//   const addExperience = async () => {
//     const token = localStorage.getItem("token");
//     if (!token) return;

//     try {
//       const response = await fetch(`${API_BASE_URL}/api/experiences`, {
//         method: "POST",
//         headers: {
//           Authorization: `Bearer ${token}`,
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(newExperience),
//       });

//       const data = await response.json();

//       if (response.ok) {
//         setExperiences([...experiences, data.experience]);
//         setNewExperience({ title: "", company_name: "", start_date: "", end_date: "", description: "" });
//       } else {
//         console.error("Erreur d'ajout :", data.message);
//       }
//     } catch (error) {
//       console.error("Erreur de requête :", error);
//     }
//   };

//   const updateExperience = async () => {
//     if (!editingExperience) return;
//     const token = localStorage.getItem("token");
//     if (!token) return;

//     try {
//       const response = await fetch(`${API_BASE_URL}/api/experiences/${editingExperience.id}`, {
//         method: "PUT",
//         headers: {
//           Authorization: `Bearer ${token}`,
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(editingExperience),
//       });

//       const data = await response.json();

//       if (response.ok) {
//         setExperiences(experiences.map(exp => (exp.id === editingExperience.id ? data.experience : exp)));
//         setEditingExperience(null);
//       } else {
//         console.error("Erreur de modification :", data.message);
//       }
//     } catch (error) {
//       console.error("Erreur de requête :", error);
//     }
//   };

//   const deleteExperience = async (id: number) => {
//     const token = localStorage.getItem("token");
//     if (!token) return;

//     try {
//       const response = await fetch(`${API_BASE_URL}/api/experiences/${id}`, {
//         method: "DELETE",
//         headers: { Authorization: `Bearer ${token}` },
//       });

//       if (response.ok) {
//         setExperiences(experiences.filter(exp => exp.id !== id));
//       } else {
//         console.error("Erreur de suppression");
//       }
//     } catch (error) {
//       console.error("Erreur de requête :", error);
//     }
//   };

//   return (
//     <div className="w-full md:w-2/3 lg:w-1/2 bg-white p-8 shadow-lg rounded-xl ml-6 mt-6">
//       <h2 className="text-3xl font-bold mb-6 text-gray-800">Expérience</h2>

//       {experiences.map(exp => (
//         <div key={exp.id} className="p-4 border rounded-lg mb-4">
//           {editingExperience?.id === exp.id ? (
//             <input
//               type="text"
//               value={editingExperience.title}
//               onChange={(e) => setEditingExperience({ ...editingExperience, title: e.target.value })}
//               className="w-full p-2 border rounded-lg"
//             />
//           ) : (
//             <h3 className="font-bold">{exp.title}</h3>
//           )}
//           <p>{exp.company_name}</p>
//           <p>{exp.start_date} - {exp.end_date || "Présent"}</p>
//           <p>{exp.description}</p>
//           <button onClick={() => setEditingExperience(exp)} className="mr-2 text-blue-500">Modifier</button>
//           <button onClick={() => deleteExperience(exp.id)} className="text-red-500">Supprimer</button>
//         </div>
//       ))}

//       {editingExperience && (
//         <button onClick={updateExperience} className="bg-yellow-500 text-white px-4 py-2 rounded-lg mt-2">Enregistrer les modifications</button>
//       )}

//       <div className="bg-gray-100 p-6 rounded-xl shadow-sm">
//         <h3 className="text-lg font-semibold mb-4">Ajouter une expérience</h3>
//         <input 
//           type="text" 
//           placeholder="Titre du poste" 
//           value={newExperience.title} 
//           onChange={(e) => setNewExperience({ ...newExperience, title: e.target.value })} 
//           className="w-full p-3 border rounded-lg mb-3" 
//         />
//         <input 
//           type="text" 
//           placeholder="Nom de l'entreprise" 
//           value={newExperience.company_name} 
//           onChange={(e) => setNewExperience({ ...newExperience, company_name: e.target.value })} 
//           className="w-full p-3 border rounded-lg mb-3" 
//         />
        
//         <div className="flex gap-4">
//            <input
//             type="date"
//             value={newExperience.start_date}
//             onChange={(e) => setNewExperience({ ...newExperience, start_date: e.target.value })}
//             className="w-1/2 p-3 border rounded-lg mb-3"
//           />

//           <input
//             type="date"
//             value={newExperience.end_date || ""}
//             onChange={(e) => setNewExperience({ ...newExperience, end_date: e.target.value })}
//             className="w-1/2 p-3 border rounded-lg mb-3"
//           />
//         </div>

//         <textarea
//           placeholder="Description"
//           value={newExperience.description}
//           onChange={(e) => setNewExperience({ ...newExperience, description: e.target.value })}
//           className="w-full p-3 border rounded-lg mb-3"
//         />
//         <button onClick={addExperience} className="w-full bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 transition">Ajouter</button>
//       </div>
//     </div>
//   );
// };

// export default ExperiencePro;


// import { useState, useEffect } from "react";
// import { API_BASE_URL } from '../config';

// interface Experience {
//   id: number;
//   title: string;
//   company_name: string;
//   start_date: string;
//   end_date: string | null;
//   description: string;
// }

// const ExperiencePro = () => {
//   const [experiences, setExperiences] = useState<Experience[]>([]);
//   const [newExperience, setNewExperience] = useState<Partial<Experience>>({
//     title: "",
//     company_name: "",
//     start_date: "",
//     end_date: "",
//     description: "",
//   });

//   // Récupérer les expériences existantes
//   useEffect(() => {
//     const fetchExperiences = async () => {
//       const token = localStorage.getItem("token");
//       if (!token) return;

//       try {
//         const response = await fetch(`${API_BASE_URL}/api/experiences`, {
//           method: "GET",
//           headers: { Authorization: `Bearer ${token}` },
//         });
//         const data = await response.json();

//         if (response.ok) {
//           setExperiences(data.experiences);
//         } else {
//           console.error("Erreur lors de la récupération :", data.message);
//         }
//       } catch (error) {
//         console.error("Erreur de requête :", error);
//       }
//     };

//     fetchExperiences();
//   }, []);

//   // Gérer l'ajout d'une nouvelle expérience
//   const addExperience = async () => {
//     const token = localStorage.getItem("token");
//     if (!token) return;

//     try {
//       const response = await fetch(`${API_BASE_URL}/api/experiences`, {
//         method: "POST",
//         headers: {
//           Authorization: `Bearer ${token}`,
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(newExperience),
//       });

//       const data = await response.json();

//       if (response.ok) {
//         setExperiences((prev) => [...prev, data.experience]); // Mise à jour immédiate
//         setNewExperience({ title: "", company_name: "", start_date: "", end_date: "", description: "" }); // Réinitialiser le formulaire
//       } else {
//         console.error("Erreur d'ajout :", data.message);
//       }
//     } catch (error) {
//       console.error("Erreur de requête :", error);
//     }
//   };

//   return (
//     <div className="w-full md:w-2/3 lg:w-1/2 bg-white p-8 shadow-lg rounded-xl ml-6 mt-6">
//       <h2 className="text-3xl font-bold mb-6 text-gray-800">Expérience</h2>

//       {/* Affichage des expériences */}
//       {experiences.map((exp) => (
//         <div key={exp.id} className="p-4 border rounded-lg mb-4">
//           <h3 className="font-bold">{exp.title}</h3>
//           <p>{exp.company_name}</p>
//           <p>{exp.start_date} - {exp.end_date || "Présent"}</p>
//           <p>{exp.description}</p>
//         </div>
//       ))}

//       {/* Formulaire d'ajout */}
//       <div className="bg-gray-100 p-6 rounded-xl shadow-sm">
//         <h3 className="text-lg font-semibold mb-4">Ajouter une expérience</h3>

//         <input
//           type="text"
//           placeholder="Titre du poste"
//           value={newExperience.title}
//           onChange={(e) => setNewExperience({ ...newExperience, title: e.target.value })}
//           className="w-full p-3 border rounded-lg mb-3"
//         />

//         <input
//           type="text"
//           placeholder="Nom de l'entreprise"
//           value={newExperience.company_name}
//           onChange={(e) => setNewExperience({ ...newExperience, company_name: e.target.value })}
//           className="w-full p-3 border rounded-lg mb-3"
//         />

//         <div className="flex gap-4">
//           <input
//             type="date"
//             value={newExperience.start_date}
//             onChange={(e) => setNewExperience({ ...newExperience, start_date: e.target.value })}
//             className="w-1/2 p-3 border rounded-lg mb-3"
//           />

//           <input
//             type="date"
//             value={newExperience.end_date || ""}
//             onChange={(e) => setNewExperience({ ...newExperience, end_date: e.target.value })}
//             className="w-1/2 p-3 border rounded-lg mb-3"
//           />
//         </div>

//         <textarea
//           placeholder="Description"
//           value={newExperience.description}
//           onChange={(e) => setNewExperience({ ...newExperience, description: e.target.value })}
//           className="w-full p-3 border rounded-lg mb-3"
//         />

//         <button
//           onClick={addExperience}
//           className="w-full bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 transition"
//         >
//           Ajouter
//         </button>
//       </div>
//     </div>
//   );
// };

// export default ExperiencePro;



// import { useState } from "react";

// // Définition de l'interface pour une expérience
// interface Experience {
//   id: number;
//   title: string;
//   company: string;
//   startDate: string;
//   endDate: string;
//   description: string;
// }

// const ExperiencePro = () => {
//   const [experiences, setExperiences] = useState<Experience[]>([
//     {
//       id: 1,
//       title: "Développeur Web Senior",
//       company: "Entreprise XYZ",
//       startDate: "2020-01-15",
//       endDate: "2023-03-20",
//       description: "Responsable du développement frontend et backend...",
//     },
//   ]);

//   // Ajouter une nouvelle expérience
//   const addExperience = () => {
//     setExperiences((prev) => [
//       ...prev,
//       {
//         id: prev.length + 1,
//         title: "",
//         company: "",
//         startDate: "",
//         endDate: "",
//         description: "",
//       },
//     ]);
//   };

//   // Supprimer une expérience
//   const removeExperience = (id: number) => {
//     setExperiences((prev) => prev.filter((exp) => exp.id !== id));
//   };

//   // Mettre à jour une expérience
//   const handleChange = (id: number, field: keyof Experience, value: string) => {
//     setExperiences((prev) =>
//       prev.map((exp) => (exp.id === id ? { ...exp, [field]: value } : exp))
//     );
//   };

//   return (
//     <div className="w-full md:w-2/3 lg:w-1/2 bg-white p-8 shadow-lg rounded-xl ml-6 mt-6">
//       <h2 className="text-3xl font-bold mb-6 text-gray-800">Expérience</h2>
//       <form className="space-y-6">
//         {experiences.map((exp) => (
//           <div key={exp.id} className="bg-gray-100 p-6 rounded-xl shadow-sm relative">
//             <div className="space-y-4">
//               <div>
//                 <label className="block text-gray-700 font-medium mb-1">Titre du poste</label>
//                 <input
//                   type="text"
//                   value={exp.title}
//                   onChange={(e) => handleChange(exp.id, "title", e.target.value)}
//                   className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
//                   placeholder="Ex: Développeur Web"
//                 />
//               </div>

//               <div>
//                 <label className="block text-gray-700 font-medium mb-1">Nom de l'entreprise</label>
//                 <input
//                   type="text"
//                   value={exp.company}
//                   onChange={(e) => handleChange(exp.id, "company", e.target.value)}
//                   className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
//                   placeholder="Ex: Google"
//                 />
//               </div>

//               <div className="flex gap-4">
//                 <div className="w-1/2">
//                   <label className="block text-gray-700 font-medium mb-1">Date de début</label>
//                   <input
//                     type="date"
//                     value={exp.startDate}
//                     onChange={(e) => handleChange(exp.id, "startDate", e.target.value)}
//                     className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
//                   />
//                 </div>

//                 <div className="w-1/2">
//                   <label className="block text-gray-700 font-medium mb-1">Date de fin</label>
//                   <input
//                     type="date"
//                     value={exp.endDate}
//                     onChange={(e) => handleChange(exp.id, "endDate", e.target.value)}
//                     className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
//                   />
//                 </div>
//               </div>

//               <div>
//                 <label className="block text-gray-700 font-medium mb-1">Description</label>
//                 <textarea
//                   value={exp.description}
//                   onChange={(e) => handleChange(exp.id, "description", e.target.value)}
//                   className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
//                   placeholder="Décrivez votre expérience..."
//                 />
//               </div>
//             </div>

//             {experiences.length > 1 && (
//               <button
//                 type="button"
//                 onClick={() => removeExperience(exp.id)}
//                 className="absolute top-3 right-3 text-gray-600 hover:text-red-500 transition"
//               >
//                 ❌
//               </button>
//             )}
//           </div>
//         ))}

//         <button
//           type="button"
//           onClick={addExperience}
//           className="w-full bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 transition"
//         >
//           ➕ Ajouter une expérience
//         </button>

//         <button className="w-full bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition mt-4">
//           ✅ Enregistrer
//         </button>
//       </form>
//     </div>
//   );
// };

// export default ExperiencePro;
