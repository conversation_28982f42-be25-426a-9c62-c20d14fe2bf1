import Header from './Header';
import Footer from './Footer';
import Container from './layout/Container';
import React, { useState, useEffect } from 'react';
import MessageSidebar from './MessageSidebar';
import MessageContent from './MessageContent';
import MessageProfile from './MessageProfile';
import Brief from './Brief';
import Attachement from './Attachement';
import { useNotifications } from './notifications/NotificationContext';
import { useParams, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from './../config';

interface OfferDetails {
  id: number;
  title: string;
  description: string;
  budget: string;
  deadline: string;
  status: 'pending' | 'open' | 'closed' | 'in_progress' | 'completed' | 'invited';
  created_at: string;
  client: {
    id: number;
    name: string;
    avatar?: string;
  };
  professional?: {
    id: number;
    name: string;
    avatar?: string;
  };
  is_interested?: boolean;
  is_invited?: boolean;
}

export interface OpenOffer {
  id: number;
  user_id: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company: string;
  website: string;
  description: string;
  files: string | null;
  recruitment_type: 'company' | 'freelance' | string;
  open_to_applications: boolean;
  auto_invite: boolean;
  status: 'open' | 'closed' | 'pending' | 'completed' | string;
  views_count: number;
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string;
    is_professional: boolean;
    created_at: string;
    updated_at: string;
    profile_completed: boolean;
  };
  applications: Application[];
}

export interface Application {
  id: number;
  open_offer_id: number;
  proposal: string | null;
  status: 'pending' | 'accepted' | 'rejected'| 'invited' | string;
  created_at: string;
  updated_at: string;
  professional_profile_id: number;
  freelance_profile: FreelanceProfile;
}

export interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  avatar: string;
  portfolio_items: any[] | null;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  title: string | null;
  expertise: string | null;
  completion_percentage: number;
  profession: string;
  years_of_experience: number;
  hourly_rate: string;
  description: string | null;
  availability_status: 'available' | 'unavailable' | string;
  estimated_response_time: string | null;
  rating: string;
  skills: string[];
  languages: string[];
  services_offered: any[];
  portfolio: PortfolioItem[];
  social_links: any[];
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string;
    is_professional: boolean;
    created_at: string;
    updated_at: string;
    profile_completed: boolean;
  };
}

export interface PortfolioItem {
  id: string;
  path: string;
  name: string;
  type: string;
  created_at: string;
}

const Message: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'messages' | 'brief' | 'attachments'>('messages');
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [offer, setOffer] = useState<OfferDetails | null>(null);
  const[offerDetail, setOfferDetail] = useState<OpenOffer | null>(null);
  const[offerComplet, setOfferComplet] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { addOfferNotification } = useNotifications();
  const [selectedApplication, setSelectedApplication] = useState<any | null>(null);
  const [selectedProfessionalId, setSelectedProfessionalId] = useState<number | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);


  const token = localStorage.getItem('token');
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const isClient = currentUser.role === 'client' || !currentUser.is_professional;

  useEffect(() => {
      const fetchOfferDetails = async () => {
        if (!token || !id) return;
  
        setLoading(true);
        try {
          const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
  
          if (!response.ok) {
            throw new Error('Erreur lors de la récupération des détails de l\'appel d\'offre');
          }
  
          const data = await response.json();
          console.log("Réponse API pour les détails de l'offre:", data);
  
          // Adapter la structure de l'offre pour correspondre à notre interface
          if (data.open_offer) {
            setOfferComplet(data.open_offer);
            setOfferDetail(data.open_offer);
            // Si l'API renvoie user au lieu de client, adapter la structure
            if (data.open_offer.user && !data.open_offer.client) {
              data.open_offer.client = {
                id: data.open_offer.user.id,
                name: data.open_offer.user.first_name + ' ' + data.open_offer.user.last_name,
                avatar: data.open_offer.user.avatar,
              };
            }
  
            setOffer(data.open_offer);
          } else {
            throw new Error('Structure de réponse API invalide');
          }
        } catch (err) {
          console.error('Erreur:', err);
          setError('Impossible de charger les détails de l\'appel d\'offre');
  
          // Utiliser des données de secours
          setOffer({
            id: parseInt(id || '0'),
            title: 'Création d\'un environnement 3D pour jeu mobile',
            description: 'Nous recherchons un artiste 3D pour créer un environnement complet pour notre jeu mobile d\'aventure. Le projet comprend la modélisation, le texturing et l\'optimisation pour mobile.',
            budget: '1 500 €',
            deadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days from now
            status: 'open',
            created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
            client: {
              id: 101,
              name: 'MobileGames Studio',
              avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
            },
            professional: isClient ? {
              id: currentUser.id,
              name: currentUser.name || 'Professionnel',
              avatar: currentUser.avatar,
            } : undefined,
            is_interested: !isClient,
            is_invited: !isClient,
          });
        } finally {
          setLoading(false);
        }
      };
  
      fetchOfferDetails();
    }, [id, token, isClient, currentUser.id, currentUser.name, currentUser.avatar]);


    if(loading){
      return (
        <>
          <Header />
          <main style={{ minHeight: '60vh' }}>
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
            </div>
          </main>
          <Footer/>
        </>
      )
    }

  return (
    <>
      <Header />
      <main style={{ minHeight: '60vh' }}>
        <Container padding={false}>
          <h2 style={{ fontSize: '2rem', fontWeight: '600', margin: '65px 0 0 0' }}>{offer?.title}</h2>
          <div style={{ display: 'flex', gap: 32, borderBottom: '1.5px solid #222', width: '100%', maxWidth: 1200, marginTop: 24 }}>
            {[
              { key: 'messages', label: 'Messages' },
              { key: 'brief', label: 'Brief' },
              { key: 'attachments', label: 'Attachments' }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as 'messages' | 'brief' | 'attachments')}
                style={{
                  background: 'none',
                  border: 'none',
                  borderBottom: activeTab === tab.key ? '2.5px solid #222' : 'none',
                  color: activeTab === tab.key ? '#222' : '#888',
                  fontWeight: activeTab === tab.key ? 600 : 400,
                  fontSize: 15,
                  padding: '12px 0',
                  cursor: 'pointer',
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>
          <div style={{ display: 'flex', width: '100%', maxWidth: 1600, margin: '40px auto 0 auto', background: '#fff', borderRadius: 24 }}>
            {activeTab === 'brief' ? (
              <Brief offer={offer}/>
            ) : activeTab === 'attachments' ? (
              <Attachement />
            ) : (
              <>
                {isClient &&
                <MessageSidebar 
                  offerDetail={offerDetail}
                  setSelectedApplication={setSelectedApplication}
                  onSelectProfessional={setSelectedProfessionalId}
                />
                }
                <MessageContent 
                  offerId={offer?.id}
                  offerTitle={offer?.title}
                  clientId={offer?.client?.id}
                  clientName={offer?.client?.name || "Client"}
                  clientAvatar={offer?.client?.avatar}
                  professionalId={selectedProfessionalId ?? undefined}
                  professionalName={offer?.professional?.name || "Professionnel"}
                  professionalAvatar={offer?.professional?.avatar}
                  isClient={isClient}
                  onBack={() => navigate(-1)}
                  selectedFiles={selectedFiles}
                  setSelectedFiles={setSelectedFiles}
                />
                <MessageProfile 
                  application={selectedApplication} 
                  offerDetail={offerComplet}
                />
              </>
            )}
          </div>
        </Container>
      </main>
      <Footer />
    </>
  );
};

export default Message; 