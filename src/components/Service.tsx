import React, { useState } from "react";
import { X } from "lucide-react";
import { API_BASE_URL } from '../config';
import { useProfile } from "./ProfileContext"; 

const Service = () => {
    const { profile, setProfile } = useProfile();
    const storedProfile = JSON.parse(localStorage.getItem("userProfile") || "{}");
    const initialservices_offered = storedProfile?.profile_data?.services_offered || [];
    const initialWorkingHours = storedProfile?.profile_data?.hourly_rate || 0;
    
    const [workingHours, setWorkingHours] = useState<number>(initialWorkingHours);
    const [services_offered, setservices_offered] = useState<string[]>(initialservices_offered);
    const [newservices_offer, setNewservices_offer] = useState<string>("");
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState("");

    const handleWorkingHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setWorkingHours(Number(e.target.value));
      };
  
    // Ajouter une compétence
    const handleAddservices_offer = () => {
      if (newservices_offer.trim() !== "" && !services_offered.includes(newservices_offer.trim())) {
        setservices_offered([...services_offered, newservices_offer.trim()]);
        setNewservices_offer("");
      }
    };
  
    // Supprimer une compétence
    const handleRemoveservices_offer = (services_offerToRemove: string) => {
      setservices_offered(services_offered.filter((services_offer) => services_offer !== services_offerToRemove));
    };
  
    // Gestion de l'entrée utilisateur
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setNewservices_offer(e.target.value);
    };
  
    // Ajouter une compétence avec "Entrée"
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        handleAddservices_offer();
      }
    };
  
    // Envoyer les compétences à l'API
    const handleSubmit = async () => {
      setLoading(true);
      setMessage("");
  
      const token = localStorage.getItem("token");
  
      try {
        const response = await fetch(
          `${API_BASE_URL}/api/profile/completion/services`,
          {
            method: "PUT",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ services_offered, hourly_rate:workingHours }),
          }
        );
  
        const data = await response.json();
  
        if (response.ok) {
          setMessage("✅ Offre mises à jour avec succès !");
          // Mettre à jour le localStorage
          const updatedProfile = {
            ...storedProfile,
            profile_data: {
              ...storedProfile.profile_data,
              services_offered: services_offered,
              hourly_rate:workingHours,
            },
          };
          const updateJiab = {
            ...profile,
            profile_data: {
              ...storedProfile.profile_data,
              services_offered: services_offered,
              hourly_rate:workingHours,
            },
          };
          localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
          setProfile(updateJiab);
        } else {
          setMessage("❌ Erreur : " + (data.message || "Impossible d'enregistrer les offres."));
        }
      } catch (error) {
        setMessage("❌ Erreur réseau, veuillez réessayer.");
      } finally {
        setLoading(false);
      }
    };
  
    return (
      <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
        <h2 className="text-sm font-bold text-gray-700 mb-4">OFFRES & SERVICES </h2>
  
        {message && (
          <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
            {message}
          </div>
        )}
  
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <h3 className="text-lg font-semibold mb-4">Service</h3>
            <input
              type="text"
              placeholder="Ajouter un service"
              value={newservices_offer}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
  
          {/* Liste des compétences ajoutées */}
          <div className="flex gap-2 flex-wrap mt-4">
            {services_offered.map((services_offer, index) => (
              <div key={index} className="flex items-center gap-2 bg-gray-200 rounded-lg px-3 py-1">
                <span>{services_offer}</span>
                <button type="button" onClick={() => handleRemoveservices_offer(services_offer)} className="text-red-500">
                  <X size={14} />
                </button>
              </div>
            ))}
          </div>
  
          {/* Bouton pour ajouter la compétence */}
          <div className="mt-4">
            <button type="button" onClick={handleAddservices_offer} className="bg-green-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-600 transition">
              Ajouter le service
            </button>
          </div>

          <div className="grid grid-cols-1 gap-4">
            <h3 className="text-lg font-semibold mb-4">Tarif horaire</h3>
            <input
              type="text"
              placeholder="Tarif horaire"
              value={workingHours} // Si `experience` peut être nul, on utilise une chaîne vide
              onChange={handleWorkingHoursChange} // Convertir en nombre
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="0"
              step="1"
            />
          </div>
  
          {/* Bouton pour envoyer les compétences à l'API */}
          <div className="mt-4">
            <button
              type="button"
              onClick={handleSubmit}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition"
              disabled={loading}
            >
              {loading ? "Envoi en cours..." : "Enregistrer mes services et offres"}
            </button>
          </div>
        </div>
      </div>
    );
};

export default Service;
