import { API_BASE_URL } from '../config';

// Types pour les professionnels
export interface Professional {
  id: number;
  user_id?: number;
  first_name: string;
  last_name: string;
  title?: string;
  skills?: string[];
  rating?: number;
  review_count?: number;
  profile_picture_path?: string;
  city?: string;
  country?: string;
  availability_status?: 'available' | 'busy' | 'unavailable';
  achievements:any[];
  service_offer:any[];
  likes_count?: number;
  views_count?: number;
}

// Types pour les projets
export interface Project {
  id: number;
  title: string;
  description?: string;
  image_url?: string;
  category?: string;
  client_name?: string;
  professional_id?: number;
  professional_name?: string;
  price?:string;
  avatar?:string;
  views? : number,
  likes? : number,
  concepts? : number,
  revisions? : number,
  user_id?: number,
}

// Types pour les catégories
export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image_url?: string;
  count: number;
}

// Interface pour les options de filtrage
export interface FilterOptions {
  search?: string;
  availability?: string;
  skills?: string[];
  rating?: number | null;
  location?: string;
  category?: string;
  price_min?: number;
  price_max?: number;
  execution_time?: string;
  sort_by?: string; // Options: 'newest', 'rating', 'price_asc', 'price_desc'
}

// Fonction pour récupérer le token d'authentification
const getAuthToken = (): string | null => {
  return localStorage.getItem('token');
};

// Fonction générique pour les requêtes API
const fetchApi = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const token = getAuthToken();

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };

  if (token) {
    headers['Authorization' as keyof HeadersInit] = `Bearer ${token}`;
  }

  const url = `${API_BASE_URL}${endpoint}`;

  console.log(`API Request: ${options.method || 'GET'} ${url}`);

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(JSON.stringify({
        status: response.status,
        message: errorData.message || response.statusText,
        errors: errorData.errors,
      }));
    }

    return await response.json();
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

// Service d'exploration
export const exploreService = {
  // Récupérer tous les professionnels
  getProfessionals: async (): Promise<{ professionals: Professional[] }> => {
    try {
      // Utiliser l'endpoint public pour récupérer les professionnels
      return fetchApi('/api/professionals');
    } catch (error) {
      console.error('Erreur lors de la récupération des professionnels:', error);
      // En cas d'erreur, retourner un tableau vide
      return { professionals: [] };
    }
  },

  // Récupérer les professionnels avec leur disponibilité
  getProfessionalsWithAvailability: async (): Promise<{ professionals: Professional[] }> => {
    try {
      // Utiliser l'endpoint public pour récupérer les professionnels avec leur disponibilité
      return fetchApi('/api/professionals/availability');
    } catch (error) {
      console.error('Erreur lors de la récupération des professionnels avec disponibilité:', error);
      // En cas d'erreur, retourner un tableau vide
      return { professionals: [] };
    }
  },

  // Récupérer tous les profils freelance
  getFreelanceProfiles: async (): Promise<{ data: Professional[] }> => {
    try {
      // Utiliser l'endpoint public pour récupérer les profils freelance
      return fetchApi('/api/freelance-profiles');
    } catch (error) {
      console.error('Erreur lors de la récupération des profils freelance:', error);
      // En cas d'erreur, retourner un tableau vide
      return { data: [] };
    }
  },

  // Récupérer les projets (nécessite authentification)
  getProjects: async (): Promise<{ projects: Project[] }> => {
    try {
      // Utiliser l'endpoint protégé pour récupérer les projets
      return fetchApi('/api/dashboard/projects');
    } catch (error) {
      console.error('Erreur lors de la récupération des projets:', error);
      // En cas d'erreur, retourner un tableau vide
      return { projects: [] };
    }
  },

  // Récupérer les offres de service (nécessite authentification)
  getServiceOffers: async (): Promise<{ data: Project[] }> => {
    try {
      // Utiliser l'endpoint protégé pour récupérer les offres de service
      return fetchApi('/api/service-offers');
    } catch (error) {
      console.error('Erreur lors de la récupération des offres de service:', error);
      // En cas d'erreur, retourner un tableau vide
      return { data: [] };
    }
  },

  // Récupérer les catégories depuis l'API
  getCategories: async (): Promise<{ categories: Category[] }> => {
    try {
      // Utiliser l'endpoint public pour récupérer les catégories
      return fetchApi('/api/categories');
    } catch (error) {
      console.error('Erreur lors de la récupération des catégories:', error);
      // En cas d'erreur, retourner des catégories de secours
      const backupCategories: Category[] = [
        { id: 1, name: 'Modélisation 3D', slug: 'modelisation-3d', description: 'Création de modèles 3D pour divers usages', image_url: '/images/categories/modelisation.jpg', count: 24 },
        { id: 2, name: 'Animation 3D', slug: 'animation-3d', description: 'Animation de personnages et objets en 3D', image_url: '/images/categories/animation.jpg', count: 18 },
        { id: 3, name: 'Rendu 3D', slug: 'rendu-3d', description: 'Création d\'images et vidéos photoréalistes', image_url: '/images/categories/rendu.jpg', count: 15 },
        { id: 4, name: 'Texturing', slug: 'texturing', description: 'Application de textures sur des modèles 3D', image_url: '/images/categories/texturing.jpg', count: 12 },
        { id: 5, name: 'Rigging', slug: 'rigging', description: 'Création de squelettes pour l\'animation', image_url: '/images/categories/rigging.jpg', count: 8 },
        { id: 6, name: 'VFX', slug: 'vfx', description: 'Effets visuels pour films et jeux vidéo', image_url: '/images/categories/vfx.jpg', count: 10 },
      ];
      return { categories: backupCategories };
    }
  },

  // Filtrer les professionnels selon différents critères
  filterProfessionals: async (options: FilterOptions): Promise<{ professionals: Professional[] }> => {
    try {
      // Construire l'URL avec les paramètres de filtrage
      let url = '/api/professionals/filter?';
      const params = new URLSearchParams();

      // Ajouter les paramètres de filtrage à l'URL
      if (options.search) params.append('search', options.search);
      if (options.availability && options.availability !== 'all') params.append('availability', options.availability);
      if (options.skills && options.skills.length > 0) params.append('skills', options.skills.join(','));
      if (options.rating !== null && options.rating !== undefined) params.append('rating', options.rating.toString());
      if (options.location) params.append('location', options.location);
      if (options.category && options.category !== 'all') params.append('category', options.category);

      // Utiliser l'endpoint de filtrage avec les paramètres
      return fetchApi(`${url}${params.toString()}`);
    } catch (error) {
      console.error('Erreur lors du filtrage des professionnels:', error);
      // En cas d'erreur, retourner un tableau vide
      return { professionals: [] };
    }
  },

  // Filtrer les projets selon différents critères
  filterProjects: async (options: FilterOptions): Promise<{ projects: Project[] }> => {
    try {
      // Construire l'URL avec les paramètres de filtrage
      let url = '/api/dashboard/projects/filter?';
      const params = new URLSearchParams();

      // Ajouter les paramètres de filtrage à l'URL
      if (options.search) params.append('search', options.search);
      if (options.category && options.category !== 'all') params.append('category', options.category);
      if (options.skills && options.skills.length > 0) params.append('skills', options.skills.join(','));

      // Utiliser l'endpoint de filtrage avec les paramètres
      return fetchApi(`${url}${params.toString()}`);
    } catch (error) {
      console.error('Erreur lors du filtrage des projets:', error);
      // En cas d'erreur, retourner un tableau vide
      return { projects: [] };
    }
  },

  // Filtrer les services selon différents critères
  filterServices: async (options: FilterOptions): Promise<{ data: any[] }> => {
    try {
      // Construire l'URL avec les paramètres de filtrage
      let url = '/api/service-offers/filter?';
      const params = new URLSearchParams();

      // Ajouter les paramètres de filtrage à l'URL
      if (options.search) params.append('search', options.search);
      if (options.category && options.category !== 'all') params.append('category', options.category);
      if (options.price_min !== undefined) params.append('price_min', options.price_min.toString());
      if (options.price_max !== undefined) params.append('price_max', options.price_max.toString());
      if (options.execution_time && options.execution_time !== 'all') params.append('execution_time', options.execution_time);

      // Utiliser l'endpoint de filtrage avec les paramètres
      return fetchApi(`${url}${params.toString()}`);
    } catch (error) {
      console.error('Erreur lors du filtrage des services:', error);
      // En cas d'erreur, retourner un tableau vide
      return { data: [] };
    }
  },
};

export default exploreService;
