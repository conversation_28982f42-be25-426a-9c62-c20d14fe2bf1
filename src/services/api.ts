import { API_BASE_URL } from '../config';

// Types d'erreurs possibles
export type ApiError = {
  message: string;
  errors?: Record<string, string[]>;
  status?: number;
};

// Options par défaut pour les requêtes fetch
const defaultOptions: RequestInit = {
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
  credentials: 'include', // Inclure les cookies avec toutes les requêtes
};

// Fonction générique pour faire des requêtes API
async function fetchApi<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  // Ajouter le préfixe /api si l'endpoint ne commence pas par /api
  const apiEndpoint = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`;
  const url = `${API_BASE_URL}${apiEndpoint}`;

  console.log(`API Request: ${options.method || 'GET'} ${url}`);

  try {
    // Fusionner les options par défaut avec les options fournies
    const fetchOptions: RequestInit = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers,
      },
    };

    // Effectuer la requête
    const response = await fetch(url, fetchOptions);

    // Analyser la réponse JSON
    const data = await response.json();

    // Si la réponse n'est pas OK, lancer une erreur
    if (!response.ok) {
      console.error('Réponse API non OK:', {
        status: response.status,
        statusText: response.statusText,
        data
      });

      const error: ApiError = {
        message: data.message || 'Une erreur est survenue',
        errors: data.errors,
        status: response.status,
      };
      throw error;
    }

    return data as T;
  } catch (error) {
    console.error(`API Error (${url}):`, error);

    // Si c'est une erreur réseau (Failed to fetch)
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      const networkError: ApiError = {
        message: 'Impossible de se connecter au serveur. Veuillez vérifier votre connexion internet.',
      };
      throw networkError;
    }

    // Relancer l'erreur pour qu'elle soit gérée par l'appelant
    throw error;
  }
}

// Fonction pour tester la connexion au serveur backend
export const testApiConnection = async (): Promise<boolean> => {
  try {
    // On vérifie la connectivité en utilisant une route API valide
    console.log('Test de connectivité avec le serveur...');

    // Utiliser une route API valide pour tester la connectivité
    const response = await fetch(`${API_BASE_URL}/api/ping`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Si la route ping n'existe pas, essayer une autre route commune
    if (!response.ok && response.status === 404) {
      // Essayer avec une route qui existe probablement
      const fallbackResponse = await fetch(`${API_BASE_URL}/api/health-check`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      console.log('Connectivité avec le serveur établie (via fallback)');
      return true; // Si on arrive ici, le serveur répond
    }

    console.log('Connectivité avec le serveur établie');
    return true;
  } catch (error) {
    console.error('Erreur de connexion au serveur:', error);
    return false;
  }
};

// Service d'authentification
export const authService = {
  // Inscription
  register: async (userData: any) => {
    return fetchApi('/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  // Connexion
  login: async (credentials: { email: string; password: string }) => {
    return fetchApi('/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  },

  // Déconnexion
  logout: async () => {
    return fetchApi('/logout', {
      method: 'POST',
    });
  },

  // Récupération du profil utilisateur
  getUser: async () => {
    return fetchApi('/user');
  },

  // Test de connectivité
  ping: async () => {
    // Utiliser une route API valide pour vérifier la connectivité
    try {
      const response = await fetch(`${API_BASE_URL}/api/ping`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return { message: 'pong', status: 'success' };
    } catch (error) {
      throw new Error('Impossible de se connecter au serveur');
    }
  },
};

export default {
  auth: authService,
};
