// Service de simulation d'API pour le profil client
// À utiliser temporairement jusqu'à ce que l'API backend soit disponible

interface SocialLinks {
  linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
}

interface ClientProfile {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  bio?: string;
  avatar?: string;
  birth_date?: string;
  company_name?: string;
  company_size?: string;
  industry?: string;
  position?: string;
  website?: string;
  social_links?: SocialLinks;
  preferences?: {
    notifications?: boolean;
    newsletter?: boolean;
    project_updates?: boolean;
  };
  created_at: string;
  updated_at: string;
  completion_percentage: number;
}

// Profil client simulé
const mockClientProfile: ClientProfile = {
  id: 1,
  first_name: "<PERSON>",
  last_name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  phone: "+33 6 12 34 56 78",
  address: "123 Rue de Paris",
  city: "Paris",
  country: "France",
  bio: "Entrepreneur passionné par l'innovation et les nouvelles technologies. Je recherche des professionnels pour m'aider à développer mes projets.",
  avatar: "https://randomuser.me/api/portraits/men/1.jpg",
  birth_date: "1985-05-15",
  company_name: "Dupont Innovations",
  company_size: "11-50",
  industry: "Technology",
  position: "CEO & Fondateur",
  website: "https://dupontinnovations.com",
  social_links: {
    linkedin: "https://linkedin.com/in/jeandupont",
    twitter: "https://twitter.com/jeandupont",
    facebook: "https://facebook.com/jeandupont",
    instagram: "https://instagram.com/jeandupont"
  },
  preferences: {
    notifications: true,
    newsletter: true,
    project_updates: true
  },
  created_at: "2023-01-15T10:30:00Z",
  updated_at: "2023-04-20T14:45:00Z",
  completion_percentage: 85
};

// Fonction pour récupérer le profil client
export const getClientProfile = async (): Promise<{ profile: ClientProfile }> => {
  // Simuler un délai réseau
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return { profile: mockClientProfile };
};

// Fonction pour mettre à jour le profil client
export const updateClientProfile = async (profileData: any): Promise<{ profile: ClientProfile, message: string }> => {
  // Simuler un délai réseau
  await new Promise(resolve => setTimeout(resolve, 800));
  
  // Fusionner les données mises à jour avec le profil existant
  const updatedProfile = {
    ...mockClientProfile,
    ...profileData,
    // Mettre à jour les timestamps
    updated_at: new Date().toISOString(),
    // Recalculer le pourcentage de complétion
    completion_percentage: calculateCompletionPercentage(profileData)
  };
  
  return { 
    profile: updatedProfile,
    message: "Profil mis à jour avec succès"
  };
};

// Fonction pour compléter le profil client (première connexion)
export const completeClientProfile = async (profileData: any): Promise<{ profile: ClientProfile, message: string }> => {
  // Simuler un délai réseau
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Fusionner les données mises à jour avec le profil existant
  const updatedProfile = {
    ...mockClientProfile,
    ...profileData,
    // Mettre à jour les timestamps
    updated_at: new Date().toISOString(),
    // Marquer comme complété
    completion_percentage: 100
  };
  
  return { 
    profile: updatedProfile,
    message: "Profil complété avec succès"
  };
};

// Fonction pour vérifier le statut de complétion du profil
export const getProfileCompletionStatus = async (): Promise<{ completion_percentage: number, is_first_login: boolean }> => {
  // Simuler un délai réseau
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return {
    completion_percentage: mockClientProfile.completion_percentage,
    is_first_login: Math.random() > 0.5 // Simuler aléatoirement si c'est la première connexion
  };
};

// Fonction utilitaire pour calculer le pourcentage de complétion du profil
const calculateCompletionPercentage = (profile: any): number => {
  const requiredFields = ['first_name', 'last_name', 'email'];
  const optionalFields = [
    'phone', 'address', 'city', 'country', 'bio', 'avatar', 'birth_date',
    'company_name', 'company_size', 'industry', 'position', 'website'
  ];
  
  // Compter les champs requis remplis
  const requiredFilled = requiredFields.filter(field => !!profile[field]).length;
  
  // Compter les champs optionnels remplis
  const optionalFilled = optionalFields.filter(field => !!profile[field]).length;
  
  // Calculer le pourcentage
  const totalFields = requiredFields.length + optionalFields.length;
  const filledFields = requiredFilled + optionalFilled;
  
  return Math.round((filledFields / totalFields) * 100);
};

export default {
  getClientProfile,
  updateClientProfile,
  completeClientProfile,
  getProfileCompletionStatus
};
