export type ProfileType = "independent" | "company" | "individual"; // Définition du type

export interface ProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  profileType: ProfileType; // Utilisation du type spécifique
  identity_document_number: string;
  experience: number;
  portfolio: string[];
  education: string;
  degree: string;
  skills: string[];
  languages: string[];
  availability: string;
  services: string[];
  workingHours: string;
}