import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import HomePage from './components/HomePage';
import ContactPage from './components/ContactPage';
import OfferDetails from "./components/OfferDetails";
import ListeIndependant from './components/ListeIndependant';
import MessagingPage from './components/messaging/MessagingPage';
import { AuthProvider } from './contexts/AuthContext';
import Dashboard from './components/dashboard/Dashboard';
import NotificationsPage from './components/notifications/NotificationsPage';
import { NotificationProvider } from './components/notifications/NotificationContext';
import ProjectManagementPage from './components/projects/ProjectManagementPage';
import ProjectApplicationForm from './components/projects/ProjectApplicationForm';
import AllProjectsPage from './components/projects/AllProjectsPage';
import ProjectDetailsPage from './components/projects/ProjectDetailsPage';
import AllActivitiesPage from './components/activities/AllActivitiesPage';
import AllProfessionalsPage from './components/professionals/AllProfessionalsPage';
import InvoicesPage from './components/invoices/InvoicesPage';
import ProfileDashboard from './components/dashboard/ProfileDashboard';
import DashboardEditProfile from './components/dashboard/DashboardEditProfile';
import CreateProject from './components/dashboard/CreateProject';
import ClientProfilePage from './components/profile/ClientProfilePage';
import ClientProfileEditPage from './components/profile/ClientProfileEditPage';
import ClientDashboardWrapper from './components/dashboard/ClientDashboardWrapper';

// Pages components
import AboutPage from './components/pages/AboutPage';
import OffersPage from './components/pages/OffersPage';
import ExplorePage from './components/pages/ExplorePage';

// Modern Profile Pages
import ProfilePage from './pages/ProfilePage';
import EditProfilePage from './pages/EditProfilePage';

// Auth components
import AuthPage from './components/auth/AuthPage';
import SimpleLoginForm from './components/auth/SimpleLoginForm';

// Context providers
import { ProfileProvider } from "./components/ProfileContext";

function App() {
  return (
    <ProfileProvider>
      <NotificationProvider>
        <AuthProvider>
          <Router>
            <Routes>
              {/* Pages principales */}
              <Route path="/" element={<HomePage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/offers" element={<OffersPage />} />
              <Route path="/explore" element={<ExplorePage />} />
              <Route path="/contact" element={<ContactPage />} />
              
              {/* Routes d'authentification */}
              <Route path="/register" element={<AuthPage />} />
              <Route path="/login" element={<AuthPage />} />
              <Route path="/simple-login" element={<SimpleLoginForm />} />
              <Route path="/forgot-password" element={<AuthPage />} />
              <Route path="/reset-password" element={<AuthPage />} />
              
              {/* Routes de profil */}
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/profile/edit" element={<EditProfilePage />} />
              
              {/* Routes de projets et offres */}
              <Route path="/projects" element={<ProjectManagementPage />} />
              <Route path="/projects/:id" element={<ProjectManagementPage />} />
              <Route path="/projects/:id/apply" element={<ProjectApplicationForm />} />
              <Route path="/offre/:id" element={<OfferDetails />} />
              <Route path="/lists-independants" element={<ListeIndependant />} />
              
              {/* Routes de dashboard */}
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/dashboard/profile" element={<ProfileDashboard />} />
              <Route path="/dashboard/profile/edit" element={<DashboardEditProfile />} />
              <Route path="/dashboard/client-profile" element={<ClientProfilePage />} />
              <Route path="/dashboard/client-profile/edit" element={<ClientProfileEditPage />} />
              <Route path="/dashboard/create-project" element={<CreateProject />} />
              <Route path="/dashboard/projects" element={<AllProjectsPage />} />
              <Route path="/dashboard/projects/:id" element={<ProjectDetailsPage />} />
              
              {/* Routes de notifications et messages */}
              <Route path="/notifications" element={<NotificationsPage />} />
              <Route path="/discussions" element={<MessagingPage />} />
              <Route path="/discussions/:id" element={<MessagingPage />} />
              
              {/* Autres routes */}
              <Route path="/all-projects" element={<AllProjectsPage />} />
              <Route path="/all-activities" element={<AllActivitiesPage />} />
              <Route path="/all-professionals" element={<AllProfessionalsPage />} />
              <Route path="/invoices" element={<InvoicesPage />} />
              
              {/* Route par défaut */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Router>
        </AuthProvider>
      </NotificationProvider>
    </ProfileProvider>
  );
}

export default App;
