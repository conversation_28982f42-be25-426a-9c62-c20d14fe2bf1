import { useState, ChangeEvent, FormEvent } from 'react';

interface ValidationRules {
  [key: string]: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    validate?: (value: any, formValues: any) => boolean | string;
  };
}

interface FormErrors {
  [key: string]: string;
}

/**
 * Hook personnalisé pour la gestion des formulaires avec validation
 * @param initialValues Valeurs initiales du formulaire
 * @param validationRules Règles de validation pour chaque champ
 * @param onSubmit Fonction à exécuter lors de la soumission du formulaire
 */
const useForm = <T extends Record<string, any>>(
  initialValues: T,
  validationRules: ValidationRules = {},
  onSubmit: (values: T, event: FormEvent<HTMLFormElement>) => void
) => {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Valider un champ spécifique
  const validateField = (name: string, value: any): string => {
    const rules = validationRules[name];
    if (!rules) return '';

    if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return 'Ce champ est obligatoire';
    }

    if (rules.minLength && typeof value === 'string' && value.length < rules.minLength) {
      return `Ce champ doit contenir au moins ${rules.minLength} caractères`;
    }

    if (rules.maxLength && typeof value === 'string' && value.length > rules.maxLength) {
      return `Ce champ ne doit pas dépasser ${rules.maxLength} caractères`;
    }

    if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
      return 'Format invalide';
    }

    if (rules.validate) {
      const result = rules.validate(value, values);
      if (typeof result === 'string') return result;
      if (result === false) return 'Champ invalide';
    }

    return '';
  };

  // Valider tous les champs
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    Object.keys(validationRules).forEach((name) => {
      const error = validateField(name, values[name]);
      if (error) {
        newErrors[name] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  // Gérer les changements de valeur
  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    
    // Gérer les cases à cocher
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setValues((prev) => ({ ...prev, [name]: checked }));
    } else {
      setValues((prev) => ({ ...prev, [name]: value }));
    }

    // Marquer le champ comme touché
    if (!touched[name]) {
      setTouched((prev) => ({ ...prev, [name]: true }));
    }

    // Valider le champ si déjà touché
    if (touched[name]) {
      const error = validateField(name, type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : value);
      setErrors((prev) => ({ ...prev, [name]: error }));
    }
  };

  // Gérer la perte de focus
  const handleBlur = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    
    // Marquer le champ comme touché
    setTouched((prev) => ({ ...prev, [name]: true }));
    
    // Valider le champ
    const error = validateField(name, type === 'checkbox' 
      ? (e.target as HTMLInputElement).checked 
      : value);
    setErrors((prev) => ({ ...prev, [name]: error }));
  };

  // Gérer la soumission du formulaire
  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Marquer tous les champs comme touchés
    const allTouched: Record<string, boolean> = {};
    Object.keys(validationRules).forEach((name) => {
      allTouched[name] = true;
    });
    setTouched(allTouched);
    
    // Valider le formulaire
    const isValid = validateForm();
    
    if (isValid) {
      setIsSubmitting(true);
      onSubmit(values, e);
      setIsSubmitting(false);
    }
  };

  // Réinitialiser le formulaire
  const resetForm = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  };

  // Mettre à jour une valeur spécifique
  const setValue = (name: string, value: any) => {
    setValues((prev) => ({ ...prev, [name]: value }));
    
    if (touched[name]) {
      const error = validateField(name, value);
      setErrors((prev) => ({ ...prev, [name]: error }));
    }
  };

  return {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    resetForm,
    setValue,
    validateForm
  };
};

export default useForm;
