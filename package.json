{"name": "freelance-platform", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/material": "^6.4.6", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.89", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/recharts": "^1.8.29", "axios": "^1.9.0", "bootstrap": "^5.3.3", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "framer-motion": "^12.4.7", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-quill": "^2.0.0", "react-router-dom": "^6.22.3", "react-scripts": "^5.0.1", "recharts": "^2.15.3", "sweetalert2": "^11.6.13", "swiper": "^11.2.4", "tailwind-merge": "^3.3.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zod": "^3.22.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-icons": "^2.2.7", "autoprefixer": "^10.4.18", "postcss": "^8.4.35", "source-map-loader": "^5.0.0", "tailwindcss": "^3.4.1"}}