# Liste des composants obsolètes à supprimer

Ces composants ont été remplacés par des versions plus modernes ou ne sont plus utilisés dans l'application.

## Composants de profil obsolètes
- `frontend/src/components/Home.tsx`
- `frontend/src/components/Profile.tsx`
- `frontend/src/components/EditProfile.tsx`
- `frontend/src/components/EditProfileClient.tsx`
- `frontend/src/components/ProfileForm.tsx`
- `frontend/src/components/ProfileWizard.tsx`
- `frontend/src/components/ProfileDetails.tsx`
- `frontend/src/components/context/ProfileWizardContext.tsx`

## Composants de projet obsolètes
- `frontend/src/components/ProjetPerso.tsx`
- `frontend/src/components/CreatProjetClient.tsx`
- `frontend/src/components/CreateProfileClient.tsx`

## Autres composants obsolètes
- `frontend/src/components/ProfessianelIndependant.tsx`
- `frontend/src/components/ArtistDetails.tsx`
- `frontend/src/components/ProOpenOffers.tsx`
- `frontend/src/components/DiscussionMessages.tsx`

## Instructions pour la suppression

Pour supprimer ces fichiers, vous pouvez utiliser la commande suivante :

```powershell
# Exemple pour Windows PowerShell
Remove-Item frontend/src/components/Home.tsx
Remove-Item frontend/src/components/Profile.tsx
# etc.
```

ou

```bash
# Exemple pour Linux/Mac
rm frontend/src/components/Home.tsx
rm frontend/src/components/Profile.tsx
# etc.
```

**Note importante :** Avant de supprimer ces fichiers, assurez-vous que :
1. Toutes les fonctionnalités ont été migrées vers les nouveaux composants
2. Aucune référence à ces composants n'existe dans le code restant
3. Vous avez une sauvegarde ou un système de contrôle de version (comme Git) en place
